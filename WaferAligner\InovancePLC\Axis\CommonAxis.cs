﻿using WaferAligner.Communication.Inovance.Abstractions;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using WaferAligner.Services.Logging.Abstractions;
using Microsoft.Extensions.DependencyInjection;
using System.Threading.Channels;
using WaferAligner;
using WaferAligner.Common;
using WaferAligner.Services.Logging.Extensions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using WaferAligner.EventIds;
using WaferAligner.Services;
namespace AlignerUI
{
    public enum AxisAction
    {
        None = 0,
        JogB = 1,
        JogF = 2,
        Home = 3,
        MoveABS = 4,
        Stop = 5,
        Reset = 6,
        ZeroSet = 7,
        Power = 8,
        MoveOff = 9,
        PowerOff = 10
    }
    public enum AxisPositionNumber
    {
        Pos1 = 1,
        Pos2 = 2,
        Pos3 = 3,
        None = 4
    }

    public abstract partial class CommonAxis<T> : GenericAxis
    {
        #region 
        //[ObservableProperty] private double topWaferPhotoZ = 0;//ZTakePhoto//上拍照位
        //[ObservableProperty] private double bottomWaferPhotoZ = 0;//TakeDownWafer//下拍照位
        //[ObservableProperty] private double topWaferTakeUpPos = 0;//TakeUpWafer//上吸合位
        //[ObservableProperty] private double bottomWaferTakeDownPos = 0;//TakeDownWafer1//下贴片位
        //[ObservableProperty] private double topWaferZLevel = 0;//ZLevel//调平位


        [ObservableProperty] private bool arrive_position_1 = false;
        [ObservableProperty] private bool arrive_position_2 = false;
        [ObservableProperty] private bool arrive_position_3 = false;

        [ObservableProperty] private bool arrive_position = false;
        [ObservableProperty] private bool reset_done = false;
        [ObservableProperty] private bool stop_done = false;

        [ObservableProperty] private T position;//写入数据变量
        [ObservableProperty] private T realTimePosition;//读取数据变量

        [ObservableProperty] private T position1;
        [ObservableProperty] private T realTimePosition1;

        [ObservableProperty] private T position2;
        [ObservableProperty] private T realTimePosition2;

        [ObservableProperty] private T position3;
        [ObservableProperty] private T realTimePosition3;

        [ObservableProperty] private int jogSpeed = 0;
        [ObservableProperty] private double realTimeJogSpeed = 0;


        [ObservableProperty] private int runSpeed = 0;
        [ObservableProperty] private float realTimeRunSpeed = 0;

        [ObservableProperty] private T currentPosition;

        [ObservableProperty] private T realTimeCurrentPosition;

        [ObservableProperty] private double currentSpeed = 0;

        [ObservableProperty] private int realTimeCurrentSpeed = 0;

        #endregion 

        private string preFix = string.Empty;
        private readonly ILoggingService _loggingService;
        public CommonAxis(string axis, string plcAddress, int port, IPlcInstance plcInstance, ILoggingService loggingService = null) : base(axis, plcAddress: plcAddress, port: port, plcInstance, loggingService)
        {
            // 通过构造函数参数获取日志服务
            _loggingService = loggingService;

            #region Add monitor variable
            //MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.ZTakePhoto", typeof(double));//上拍照位
            //MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.TakeDownWafer", typeof(double));//下拍照位
            //MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.TakeUpWafer", typeof(double));//上吸合位
            //MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.TakeDownWafer1", typeof(double));//下贴片位
            //MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.ZLevel", typeof(float));//调平位 



            MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.{AxisName}Position1Reached", typeof(bool));
            MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.{AxisName}Position2Reached", typeof(bool));
            MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.{AxisName}Position3Reached", typeof(bool));

            MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.{AxisName}AbsDone", typeof(bool));//绝对定位完成
            MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.{AxisName}ResetDone", typeof(bool));//复位完成
            MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.{AxisName}StopDone", typeof(bool));//停止完成

            MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.{AxisName}Target", typeof(T));
            MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.{AxisName}Position1", typeof(T));
            MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.{AxisName}Position2", typeof(T));
            MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.{AxisName}Position3", typeof(T));

            MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.{AxisName}Velo", typeof(double));//JOG速度
            //MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.{AxisName}Velo", typeof(float));//运动速度           
            MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.{AxisName}RealDistance", typeof(T));//当前位置
            MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.{AxisName}RealVelo", typeof(double));//当前速度

            #endregion

            #region Add monitor action
            //VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.ZTakePhoto", (obj) => TopWaferPhotoZ = (double)obj);//上拍照位
            //VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.TakeDownWafer", (obj) => BottomWaferPhotoZ = (double)obj);//下拍照位
            //VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.TakeUpWafer", (obj) => TopWaferTakeUpPos = (double)obj);//上吸合位
            //VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.TakeDownWafer1", (obj) => BottomWaferTakeDownPos = (double)obj);//下贴片位
            //VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.ZLevel", (obj) => TopWaferZLevel = (double)obj);//调平位


            VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.{AxisName}Position1Reached", (obj) => Arrive_position_1 = (bool)obj);
            VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.{AxisName}Position2Reached", (obj) => Arrive_position_2 = (bool)obj);
            VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.{AxisName}Position3Reached", (obj) => Arrive_position_3 = (bool)obj);

            VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.{AxisName}AbsDone", (obj) => Arrive_position = (bool)obj);
            VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.{AxisName}ResetDone", (obj) => reset_done = (bool)obj);
            VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.{AxisName}StopDone", (obj) => stop_done = (bool)obj);

            VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.{AxisName}Target", (obj) => RealTimePosition = (T)obj);
            VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.{AxisName}Position1", (obj) => RealTimePosition1 = (T)obj);
            VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.{AxisName}Position2", (obj) => RealTimePosition2 = (T)obj);
            VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.{AxisName}Position3", (obj) => RealTimePosition3 = (T)obj);

            VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.{AxisName}Velo", (obj) => RealTimeJogSpeed = (double)obj);
            //VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.{AxisName}Velo", (obj) => RealTimeRunSpeed = (float)obj);

            VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.{AxisName}RealDistance", (obj) => CurrentPosition = (T)obj);
            VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.{AxisName}RealVelo", (obj) => CurrentSpeed = (double)obj);

            #endregion
        }


        [RelayCommand]
        public async Task SetZeroPoint()
        {
            bool IsHome = false;
            if ((AxisName == "LX") || (AxisName == "RX") || (AxisName == "LY") || (AxisName == "RY"))
            {
                var result = MessageBox.Show("确认重新设置零点，点击确认后所有位置信息将会失效。", "注意", MessageBoxButtons.YesNo, MessageBoxIcon.Warning);
                if (result == DialogResult.Yes) IsHome = true;
            }
            else//LZ、RZ、Z
            {
                IsHome = true;
            }
            if (IsHome)
            {
                if (AxisName != "Z")
                {
                    await WritePLCVariable($"{AxisConstants.AXIS_GVL}.{AxisName}AxisActions", AxisAction.Home);
                    await Task.Delay(200);
                    await WritePLCVariable($"{AxisConstants.AXIS_GVL}.{AxisName}Execute", true);
                }
                else
                {
                    await WritePLCVariable($"{AxisConstants.AXIS_GVL}.{AxisName}HomeExecute", true);
                    await Task.Delay(200);
                    PLCVarReadInfo ReadHomeConfirm = new() { Name = $"{AxisConstants.AXIS_GVL}.ZHomeConfirmChuck", Type = typeof(bool) };
                    
                    // 使用父类的ReadPLCVariable方法替代ConstValue.PLC.ReadVariableAsync
                    await ReadPLCVariable(ReadHomeConfirm);
                    // 由于ReadPLCVariable不返回值，需要使用轮询变量方式检测状态变化
                    
                    int i = 0;
                    bool homeConfirmed = false;
                    
                    // 通过变量注册来监听ZHomeConfirmChuck状态
                    var originalAction = VariableChangeActions.ContainsKey($"{AxisConstants.AXIS_GVL}.ZHomeConfirmChuck") ? 
                        VariableChangeActions[$"{AxisConstants.AXIS_GVL}.ZHomeConfirmChuck"] : null;
                    
                    // 临时添加监听动作
                    VariableChangeActions[$"{AxisConstants.AXIS_GVL}.ZHomeConfirmChuck"] = (obj) => homeConfirmed = (bool)obj;
                    
                    // 添加变量到监控列表（如果不存在）
                    if (!MonitorVariables.ContainsKey($"{AxisConstants.AXIS_GVL}.ZHomeConfirmChuck"))
                    {
                        MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.ZHomeConfirmChuck", typeof(bool));
                        AddRegistry(); // 确保新添加的变量被注册
                    }
                    
                    // 等待ZHomeConfirmChuck变为true或超时
                    while (!homeConfirmed)
                    {
                        await Task.Delay(50);
                        i++;
                        if (i > 300) break;
                    }
                    
                    // 恢复原始Action（如果有）
                    if (originalAction != null)
                        VariableChangeActions[$"{AxisConstants.AXIS_GVL}.ZHomeConfirmChuck"] = originalAction;
                    else
                        VariableChangeActions.Remove($"{AxisConstants.AXIS_GVL}.ZHomeConfirmChuck");
                    
                    if (i == 301)
                    {
                        _loggingService?.LogError($"{AxisConstants.AXIS_GVL}.ZHomeConfirmChuck超时", EventIds.Z_Home_Confirm_Chuck_Timeout);
                        return;
                    }
                    
                    await Task.Delay(1000);
                    await WritePLCVariable($"{AxisConstants.AXIS_GVL}.{AxisName}HomeConfirmedChuck", true);
                }//else

            }
        }

        #region  定位运动
        [RelayCommand]
        public async Task StopPosition()
        {
            await WritePLCVariable($"{AxisConstants.AXIS_GVL}.{AxisName}AxisActions", AxisAction.Stop);
            await Task.Delay(200);
            await WritePLCVariable($"{AxisConstants.AXIS_GVL}.{AxisName}Execute", true);
        }

        [RelayCommand]
        public async Task GoPosition()
        {
            await WritePLCVariable($"{AxisConstants.AXIS_GVL}.{AxisName}AxisActions", AxisAction.MoveABS);
            await Task.Delay(200);
            await WritePLCVariable($"{AxisConstants.AXIS_GVL}.{AxisName}PositionState", AxisPositionNumber.None);
            await Task.Delay(200);
            await WritePLCVariable($"{AxisConstants.AXIS_GVL}.{AxisName}Execute", true);
            await Task.Delay(200);
        }


        [RelayCommand]
        public async Task GoPosition1()
        {
            await WritePLCVariable($"{AxisConstants.AXIS_GVL}.{AxisName}AxisActions", AxisAction.MoveABS);
            await Task.Delay(200);
            await WritePLCVariable($"{AxisConstants.AXIS_GVL}.{AxisName}PositionState", AxisPositionNumber.Pos1);
            await Task.Delay(200);
            await WritePLCVariable($"{AxisConstants.AXIS_GVL}.{AxisName}Execute", true);
        }


        [RelayCommand]
        public async Task GoPosition2()
        {
            await WritePLCVariable($"{AxisConstants.AXIS_GVL}.{AxisName}AxisActions", AxisAction.MoveABS);
            await Task.Delay(200);
            await WritePLCVariable($"{AxisConstants.AXIS_GVL}.{AxisName}PositionState", AxisPositionNumber.Pos2);
            await Task.Delay(200);
            await WritePLCVariable($"{AxisConstants.AXIS_GVL}.{AxisName}Execute", true);
        }

        [RelayCommand]
        public async Task GoPosition3()
        {
            await WritePLCVariable($"{AxisConstants.AXIS_GVL}.{AxisName}AxisActions", AxisAction.MoveABS);
            await Task.Delay(200);
            await WritePLCVariable($"{AxisConstants.AXIS_GVL}.{AxisName}PositionState", AxisPositionNumber.Pos3);
            await Task.Delay(200);
            await WritePLCVariable($"{AxisConstants.AXIS_GVL}.{AxisName}Execute", true);
        }
        #endregion 定位运动

        #region  设置目标位置
        [RelayCommand]
        public async Task SetPosition() => await WritePLCVariable($"{AxisConstants.AXIS_GVL}.{AxisName}Target", (T)Position);
        public async Task SetPosition(T value)
        {
            await WritePLCVariable($"{AxisConstants.AXIS_GVL}.{AxisName}PositionState", AxisPositionNumber.None);
            await Task.Delay(200);
            await WritePLCVariable($"{AxisConstants.AXIS_GVL}.{AxisName}Target", value);
            await Task.Delay(200);
        }

        [RelayCommand]
        public async Task SetPosition1() => await WritePLCVariable($"{AxisConstants.AXIS_GVL}.{AxisName}Position1", (T)Position1);
        public async Task SetPosition1(T value) => await WritePLCVariable($"{AxisConstants.AXIS_GVL}.{AxisName}Position1", value);

        [RelayCommand]
        public async Task SetPosition2() => await WritePLCVariable($"{AxisConstants.AXIS_GVL}.{AxisName}Position2", (T)Position2);
        public async Task SetPosition2(T value) => await WritePLCVariable($"{AxisConstants.AXIS_GVL}.{AxisName}Position2", value);
        [RelayCommand]
        public async Task SetPosition3() => await WritePLCVariable($"{AxisConstants.AXIS_GVL}.{AxisName}Position3", (T)Position3);
        public async Task SetPosition3(T value) => await WritePLCVariable($"{AxisConstants.AXIS_GVL}.{AxisName}Position13", value);

        #endregion  设置目标位置

        #region  设置速度

        [RelayCommand]
        public async Task SetJogSpeed() => await WritePLCVariable($"{AxisConstants.AXIS_GVL}.{AxisName}JogVelo", (float)JogSpeed);
        public async Task SetJogSpeed(double value) => await WritePLCVariable($"{AxisConstants.AXIS_GVL}.{AxisName}JogVelo", value);

        //public async Task<object> GetJogSpeed() => await ReadPLCVariable(new PLCVarReadInfo() { Name = $"{AxisConstants.AXIS_GVL}.{AxisName}Velo", Type = typeof(float) });



        [RelayCommand]
        public async Task SetRunSpeed() => await WritePLCVariable($"{AxisConstants.AXIS_GVL}.{AxisName}Velo", (float)RunSpeed);
        public async Task SetRunSpeed(double value) => await WritePLCVariable($"{AxisConstants.AXIS_GVL}.{AxisName}Velo", value);
        public async Task GetRunSpeed() => await ReadPLCVariable(new PLCVarReadInfo() { Name = $"{AxisConstants.AXIS_GVL}.{AxisName}Velo", Type = typeof(float) });

        #endregion  设置速度

        #region JOG运动
        [RelayCommand]
        public async Task JOF_F_Start()
        {
            await WritePLCVariable($"{AxisConstants.AXIS_GVL}.{AxisName}AxisActions", AxisAction.JogF);
            await Task.Delay(200);
            await WritePLCVariable($"{AxisConstants.AXIS_GVL}.{AxisName}Execute", true);
            await Task.Delay(200);
        }

        [RelayCommand]
        public async Task JOG_B_Start()
        {
            await WritePLCVariable($"{AxisConstants.AXIS_GVL}.{AxisName}AxisActions", AxisAction.JogB);
            await Task.Delay(200);
            await WritePLCVariable($"{AxisConstants.AXIS_GVL}.{AxisName}Execute", true);
            await Task.Delay(200);
        }

        [RelayCommand]
        public async Task JOG_Stop()
        {
            await WritePLCVariable($"{AxisConstants.AXIS_GVL}.{AxisName}AxisActions", AxisAction.Stop);
            await Task.Delay(200);
            await WritePLCVariable($"{AxisConstants.AXIS_GVL}.{AxisName}AxisActions", AxisAction.Stop);
            await Task.Delay(200);
            await WritePLCVariable($"{AxisConstants.AXIS_GVL}.{AxisName}Execute", true);
            await Task.Delay(200);
        }
        #endregion JOG运动




    }
}
