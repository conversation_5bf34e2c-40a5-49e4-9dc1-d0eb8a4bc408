﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2012/06/nuspec.xsd">
  <metadata>
    <id>WaferAligner.Infrastructure.Common</id>
    <version>1.0.0</version>
    <authors>WaferAligner Team</authors>
    <license type="expression">MIT</license>
    <licenseUrl>https://licenses.nuget.org/MIT</licenseUrl>
    <description>WaferAligner基础设施通用组件库，包含资源管理、定时器封装、UI线程安全调用、任务扩展、性能监控等功能</description>
    <tags>WinForms Infrastructure ResourceManager Timer UI Performance</tags>
    <repository type="git" url="https://github.com/your-org/WaferAligner" />
    <dependencies>
      <group targetFramework="net6.0-windows7.0">
        <dependency id="WaferAligner.EventIds" version="1.0.0" exclude="Build,Analyzers" />
        <dependency id="WaferAligner.Services.Logging" version="1.0.0" exclude="Build,Analyzers" />
        <dependency id="Microsoft.Extensions.Logging.Abstractions" version="9.0.6" exclude="Build,Analyzers" />
        <dependency id="System.Reactive" version="5.0.0" exclude="Build,Analyzers" />
        <dependency id="System.Reflection" version="4.3.0" exclude="Build,Analyzers" />
      </group>
    </dependencies>
    <frameworkReferences>
      <group targetFramework="net6.0-windows7.0">
        <frameworkReference name="Microsoft.WindowsDesktop.App.WindowsForms" />
      </group>
    </frameworkReferences>
  </metadata>
  <files>
    <file src="C:\Users\<USER>\Desktop\WaferAligner-0717-3.9.3- part\src\Infrastructure\WaferAligner.Infrastructure.Common\bin\Debug\net6.0-windows\WaferAligner.Infrastructure.Common.dll" target="lib\net6.0-windows7.0\WaferAligner.Infrastructure.Common.dll" />
  </files>
</package>