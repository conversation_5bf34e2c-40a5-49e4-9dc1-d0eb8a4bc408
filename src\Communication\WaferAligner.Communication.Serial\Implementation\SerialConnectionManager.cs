﻿using System;
using System.Threading;
using System.Threading.Tasks;
using WaferAligner.Services.Abstractions;
using WaferAligner.Services.Extensions;
using WaferAligner.Infrastructure.Common;
using WaferAligner.EventIds;
using WaferAligner.Communication.Serial.Interfaces;
using WaferAligner.Communication.Serial.Configuration;

namespace WaferAligner.Communication.Serial.Implementation
{
    /// <summary>
    /// 串口连接管理器实现
    /// </summary>
    public class SerialConnectionManager : ISerialConnectionManager, IDisposable
    {
        private readonly ILoggingService _loggingService;
        private readonly ISerialComWrapper _serialComWrapper;
        private readonly WaferAligner.Infrastructure.Common.ResourceManager _resourceManager;
        private readonly TimerWrapper _keepAliveTimer;
        private readonly object _lockObj = new object();
        private bool _isConnected = false;
        private bool _disposed = false;
        private SerialPortConfiguration _serialConfig;
        
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="loggingService">日志服务</param>
        /// <param name="serialComWrapper">串口通信封装</param>
        /// <param name="resourceManager">资源管理器</param>
        public SerialConnectionManager(
            ILoggingService loggingService, 
            ISerialComWrapper serialComWrapper,
            WaferAligner.Infrastructure.Common.ResourceManager resourceManager)
        {
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
            _serialComWrapper = serialComWrapper ?? throw new ArgumentNullException(nameof(serialComWrapper));
            _resourceManager = resourceManager ?? throw new ArgumentNullException(nameof(resourceManager));
            
            // 创建并注册保活定时器
            _keepAliveTimer = new TimerWrapper(5000);
            _keepAliveTimer.AddElapsedHandler(OnKeepAliveTimer);
            _resourceManager.RegisterResource("SerialConnectionManager_KeepAliveTimer", _keepAliveTimer);
            
            // 注册自定义清理操作
            _resourceManager.RegisterCustomCleanup(async () => await DisconnectAsync());
        }

        /// <inheritdoc/>
        public bool IsConnected => _isConnected;
        
        /// <inheritdoc/>
        public event EventHandler<SerialConnectionEventArgs> ConnectionStateChanged;
        
        /// <inheritdoc/>
        public async Task InitializeAsync()
        {
            _loggingService.LogInformation("初始化串口连接管理器", WaferAligner.EventIds.EventIds.Serial_Connection_Started);

            // 加载串口配置
            _serialConfig = SerialPortConfiguration.LoadFromConfig();

            // 验证配置有效性
            if (!_serialConfig.IsValid())
            {
                _loggingService.LogWarning($"串口配置无效，使用默认配置: {_serialConfig.GetSummary()}", WaferAligner.EventIds.EventIds.Serial_Connection_Failed);
                _serialConfig = SerialPortConfiguration.GetDefaultConfiguration();
            }

            _loggingService.LogInformation($"串口配置加载完成: {_serialConfig.GetSummary()}", WaferAligner.EventIds.EventIds.Serial_Connection_Started);

            // 启动保活定时器
            _keepAliveTimer.Start();

            // 尝试连接
            await ConnectAsync(_serialConfig.ComPort, _serialConfig.BaudRate);
        }
        
        /// <inheritdoc/>
        public async Task<bool> ConnectAsync(int comPort, int baudRate, CancellationToken cancellationToken = default)
        {
            if (_disposed) return false;
            
            try
            {
                bool isDevelopmentMode = CheckDevelopmentMode();
                
                if (isDevelopmentMode)
                {
                    _loggingService.LogInformation("开发模式：模拟串口连接成功", WaferAligner.EventIds.EventIds.Serial_Connection_Succeeded);
                    UpdateConnectionState(true);
                    return true;
                }
                
                // 更新配置中的串口参数（如果与当前配置不同）
                if (_serialConfig == null)
                {
                    _serialConfig = SerialPortConfiguration.GetDefaultConfiguration();
                }

                // 使用配置中的超时时间
                using (var cts = new CancellationTokenSource(_serialConfig.Timeout))
                using (var linkedCts = CancellationTokenSource.CreateLinkedTokenSource(cts.Token, cancellationToken))
                {
                    return await Task.Run(() => {
                        try
                        {
                            lock(_lockObj)
                            {
                                if (_isConnected) return true;
                                
                                _loggingService.LogDebug($"开始连接串口 COM{comPort}:{baudRate}", WaferAligner.EventIds.EventIds.Serial_Connection_Started);
                                
                                // 先设置控制轴数量
                                int setAxisResult = _serialComWrapper.SetControlAxis(3); // 控制3个轴：X、Y、R
                                if (setAxisResult != 1)
                                {
                                    _loggingService.LogWarning($"设置控制轴数量失败，错误码: {setAxisResult}", WaferAligner.EventIds.EventIds.Serial_Connection_Failed);
                                    return false;
                                }
                                
                                int result = _serialComWrapper.OpenComPort(comPort, baudRate);
                                bool success = result == 1;
                                
                                if (success)
                                {
                                    _loggingService.LogInformation($"串口 COM{comPort} 连接成功", WaferAligner.EventIds.EventIds.Serial_Connection_Succeeded);
                                    UpdateConnectionState(true);
                                }
                                else
                                {
                                    _loggingService.LogWarning($"串口 COM{comPort} 连接失败，错误码: {result}", WaferAligner.EventIds.EventIds.Serial_Connection_Failed);
                                }
                                
                                return success;
                            }
                        }
                        catch (Exception ex)
                        {
                            _loggingService.LogError(ex, "串口连接过程中发生异常", WaferAligner.EventIds.EventIds.Serial_Connection_Failed);
                            return false;
                        }
                    }, linkedCts.Token);
                }
            }
            catch (OperationCanceledException)
            {
                _loggingService.LogWarning("串口连接操作超时", WaferAligner.EventIds.EventIds.Serial_Connection_Failed);
                return false;
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, "串口连接时发生异常", WaferAligner.EventIds.EventIds.Serial_Connection_Failed);
                return false;
            }
        }
        
        /// <inheritdoc/>
        public async Task<bool> DisconnectAsync(CancellationToken cancellationToken = default)
        {
            if (_disposed) return false;
            
            try
            {
                return await Task.Run(() => {
                    lock(_lockObj)
                    {
                        if (!_isConnected) return true;
                        
                        _loggingService.LogDebug("开始断开串口连接", WaferAligner.EventIds.EventIds.Serial_Connection_Started);
                        
                        int result = _serialComWrapper.CloseComPort();
                        bool success = result == 1;
                        
                        if (success)
                        {
                            _loggingService.LogInformation("串口连接已断开", WaferAligner.EventIds.EventIds.Serial_Connection_Succeeded);
                            UpdateConnectionState(false);
                        }
                        else
                        {
                            _loggingService.LogWarning($"串口断开连接失败，错误码: {result}", WaferAligner.EventIds.EventIds.Serial_Connection_Failed);
                        }
                        
                        return success;
                    }
                }, cancellationToken);
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, "串口断开连接时发生异常", WaferAligner.EventIds.EventIds.Serial_Connection_Failed);
                return false;
            }
        }
        
        /// <summary>
        /// 保活定时器事件处理
        /// </summary>
        private void OnKeepAliveTimer(object sender, EventArgs e)
        {
            // 执行保活检测
            _ = KeepAliveAsync();
        }
        
        /// <summary>
        /// 保活检测
        /// </summary>
        private async Task<bool> KeepAliveAsync()
        {
            if (_disposed || !_isConnected || CheckDevelopmentMode())
                return true;
                
            try
            {
                bool isAlive = await Task.Run(() => {
                    try
                    {
                        // 使用读取版本号作为保活检测
                        int version = _serialComWrapper.GetVersion();
                        return version > 0;
                    }
                    catch
                    {
                        return false;
                    }
                });
                
                if (!isAlive)
                {
                    _loggingService.LogWarning("串口连接已断开，尝试重新连接", WaferAligner.EventIds.EventIds.Serial_Connection_Failed);
                    UpdateConnectionState(false);
                    
                    // 尝试重新连接
                    if (_serialConfig != null)
                    {
                        await ConnectAsync(_serialConfig.ComPort, _serialConfig.BaudRate);
                    }
                }
                
                return isAlive;
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, "串口保活检测异常", WaferAligner.EventIds.EventIds.Serial_Connection_Failed);
                return false;
            }
        }
        
        /// <summary>
        /// 更新连接状态
        /// </summary>
        /// <param name="connected">是否已连接</param>
        private void UpdateConnectionState(bool connected)
        {
            if (_isConnected != connected)
            {
                _isConnected = connected;
                ConnectionStateChanged?.Invoke(this, new SerialConnectionEventArgs 
                {
                    IsConnected = connected
                });
            }
        }
        
        /// <summary>
        /// 检查是否为开发模式
        /// </summary>
        /// <returns>是否为开发模式</returns>
        private bool CheckDevelopmentMode()
        {
            return WaferAligner.Infrastructure.Common.DevelopmentModeHelper.IsDevelopmentMode();
        }

        /// <inheritdoc/>
        public void Dispose()
        {
            if (_disposed)
                return;

            _disposed = true;
            
            try
            {
                // 停止定时器
                if (_keepAliveTimer != null)
                {
                    _keepAliveTimer.Stop();
                    _keepAliveTimer.RemoveElapsedHandler(OnKeepAliveTimer);
                }
                
                // 断开连接
                if (_isConnected)
                {
                    _serialComWrapper.CloseComPort();
                }
                
                _loggingService.LogDebug("释放SerialConnectionManager资源", WaferAligner.EventIds.EventIds.Serial_Resource_Released);
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, "释放SerialConnectionManager资源时发生错误", WaferAligner.EventIds.EventIds.Serial_Resource_Released);
            }
        }
    }
}
