using WaferAligner.Services.UserManagement;
using WaferAligner.Services.Extensions;
using WaferAligner.Services.Abstractions;
using Microsoft.Extensions.Logging;
using WaferAligner.EventIds;
using WaferAligner.Interfaces;
using System;
using System.Linq;

namespace WaferAligner.Services
{
    /// <summary>
    /// 用户上下文服务，实现IUserContext接口
    /// </summary>
    public class UserContextService : IUserContext
    {
        private readonly IUserManagement _userManagement;
        private readonly ILoggingService _loggingService;
        private UserInfo? _currentUser;
        
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="userManagement">用户管理服务</param>
        /// <param name="loggingService">日志服务</param>
        public UserContextService(IUserManagement userManagement, ILoggingService loggingService)
        {
            _userManagement = userManagement ?? throw new ArgumentNullException(nameof(userManagement));
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
        }
        
        /// <summary>
        /// 当前登录用户
        /// </summary>
        public UserInfo? CurrentUser => _currentUser;

        /// <summary>
        /// 获取用户管理服务
        /// </summary>
        public IUserManagement UserManagement => _userManagement;

        /// <summary>
        /// 设置当前用户
        /// </summary>
        /// <param name="user">用户信息</param>
        public void SetCurrentUser(UserInfo? user)
        {
            _currentUser = user;
            _loggingService.LogInformation($"用户上下文更新: {user?.Username ?? "未登录"}", WaferAligner.EventIds.EventIds.User_Login_Succeeded);
        }

        /// <summary>
        /// 是否已经登录
        /// </summary>
        public bool IsAuthenticated => _currentUser != null;

        /// <summary>
        /// 检查当前用户是否有指定权限
        /// </summary>
        /// <param name="permission">权限名称</param>
        /// <returns>是否有权限</returns>
        public bool HasPermission(string permission)
        {
            if (_currentUser == null || _userManagement == null) return false;
            
            return _userManagement.CheckPermission(_currentUser.Username, permission);
        }

        /// <summary>
        /// 检查当前用户是否有指定角色
        /// </summary>
        /// <param name="roleName">角色名称</param>
        /// <returns>是否有角色</returns>
        public bool HasRole(string roleName)
        {
            if (_currentUser == null) return false;
            return _currentUser.Roles.Contains(roleName, StringComparer.OrdinalIgnoreCase);
        }

        /// <summary>
        /// 检查当前用户是否是管理员
        /// </summary>
        /// <returns>是否是管理员</returns>
        public bool IsAdmin()
        {
            return HasRole("Admin");
        }

        /// <summary>
        /// 检查当前用户是否可以管理用户
        /// </summary>
        /// <returns>是否可以管理用户</returns>
        public bool CanManageUsers()
        {
            return IsAdmin();
        }

        /// <summary>
        /// 检查当前用户是否是操作员
        /// </summary>
        /// <returns>是否是操作员</returns>
        public bool IsOperator()
        {
            return HasRole("Operator");
        }

        /// <summary>
        /// 检查当前用户是否可以配置对准参数
        /// </summary>
        /// <returns>是否可以配置对准参数</returns>
        public bool CanConfigParameters()
        {
            return IsAdmin() || HasRole("Engineer");
        }

        /// <summary>
        /// 检查当前用户是否可以配置运动参数
        /// </summary>
        /// <returns>是否可以配置运动参数</returns>
        public bool CanConfigMotion()
        {
            return IsAdmin() || HasRole("Engineer");
        }

        /// <summary>
        /// 获取当前用户名
        /// </summary>
        /// <returns>用户名，如果未登录则返回空字符串</returns>
        public string GetUsername()
        {
            return _currentUser?.Username ?? string.Empty;
        }

        /// <summary>
        /// 检查当前用户是否可以访问键合对准页面
        /// </summary>
        /// <returns>是否可以访问</returns>
        public bool CanAccessAlignmentPage()
        {
            // 所有登录用户都可以访问键合对准页面
            return _currentUser != null;
        }

        /// <summary>
        /// 检查当前用户是否可以访问对准参数页面
        /// </summary>
        /// <returns>是否可以访问</returns>
        public bool CanAccessParameterPage()
        {
            return IsAdmin() || HasRole("Engineer");
        }

        /// <summary>
        /// 检查当前用户是否可以访问运动参数页面
        /// </summary>
        /// <returns>是否可以访问</returns>
        public bool CanAccessMotionPage()
        {
            return IsAdmin() || HasRole("Engineer");
        }

        /// <summary>
        /// 检查当前用户是否可以访问用户管理页面
        /// </summary>
        /// <returns>是否可以访问</returns>
        public bool CanAccessUserManagementPage()
        {
            return IsAdmin();
        }

        /// <summary>
        /// 获取用户角色显示名称
        /// </summary>
        /// <returns>角色显示名称</returns>
        public string GetRoleDisplayName()
        {
            if (_currentUser == null) return "未登录";
            
            if (HasRole("Admin")) return "管理员";
            if (HasRole("Engineer")) return "工程师";
            if (HasRole("Operator")) return "操作员";
            
            return string.Join(", ", _currentUser.Roles);
        }

        /// <summary>
        /// 注销当前用户
        /// </summary>
        public void Logout()
        {
            var oldUsername = _currentUser?.Username ?? "未知用户";
            _currentUser = null;
            _loggingService.LogInformation($"用户已注销: {oldUsername}", WaferAligner.EventIds.EventIds.User_Login_Disabled);
        }
    }
} 