﻿using System;
using System.Threading;
using System.Timers;
using Microsoft.Extensions.Logging;
using WaferAligner.Services.Abstractions;
using WaferAligner.Services.Extensions;
using System.Diagnostics;
using System.Collections.Generic;

namespace WaferAligner.Common
{
    /// <summary>
    /// Timer包装器，提供安全的Timer操作，避免ObjectDisposedException异常
    /// </summary>
    public class TimerWrapper : IDisposable
    {
        private System.Timers.Timer _timer;
        private readonly object _lockObj = new object();
        private readonly ILoggingService _logger;
        private volatile bool _isDisposed = false;
        private volatile bool _isEnabled = false;
        private ElapsedEventHandler _elapsedHandler;
        private double _interval;
        private string _name;
        
        // 用于重试机制
        private int _maxRetries = 0;
        private int _currentRetries = 0;
        private double _retryIntervalMs = 0;
        private Exception _lastException = null;
        private bool _retryRequested = false;
        
        // 用于状态跟踪
        private bool _trackingEnabled = false;
        private DateTime _lastExecutionTime = DateTime.MinValue;
        private int _executionCount = 0;
        private List<double> _executionTimes = new List<double>();
        private Stopwatch _executionStopwatch = new Stopwatch();
        
        /// <summary>
        /// 获取或设置Timer的间隔（毫秒）
        /// </summary>
        public double Interval
        {
            get => _interval;
            set
            {
                _interval = value;
                SafeTimerAction(t => t.Interval = value);
            }
        }
        
        /// <summary>
        /// 获取Timer是否已启用
        /// </summary>
        public bool Enabled => _isEnabled;
        
        /// <summary>
        /// 获取Timer是否已释放
        /// </summary>
        public bool IsDisposed => _isDisposed;
        
        /// <summary>
        /// 获取Timer的名称
        /// </summary>
        public string Name => _name;
        
        /// <summary>
        /// 获取最近一次执行时间
        /// </summary>
        public DateTime LastExecutionTime => _lastExecutionTime;
        
        /// <summary>
        /// 获取执行次数
        /// </summary>
        public int ExecutionCount => _executionCount;
        
        /// <summary>
        /// 获取平均执行时间（毫秒）
        /// </summary>
        public double AverageExecutionTimeMs 
        { 
            get 
            {
                // 创建一个集合的副本，避免在遍历过程中修改集合导致异常
                List<double> timesCopy;
                lock (_lockObj)
                {
                    timesCopy = new List<double>(_executionTimes);
                }
                
                if (timesCopy.Count == 0) 
                {
                    // 没有执行记录时返回-1表示无数据，而不是0
                    return -1;
                }
                
                double sum = 0;
                foreach (var time in timesCopy)
                {
                    sum += time;
                }
                return sum / timesCopy.Count;
            }
        }
        
        /// <summary>
        /// 创建TimerWrapper实例
        /// </summary>
        public TimerWrapper(ILoggingService logger = null)
        {
            _logger = logger;
            _name = $"Timer_{Guid.NewGuid().ToString().Substring(0, 8)}";
            CreateTimer();
        }
        
        /// <summary>
        /// 创建TimerWrapper实例，并设置间隔
        /// </summary>
        public TimerWrapper(double interval, ILoggingService logger = null)
        {
            _interval = interval;
            _logger = logger;
            _name = $"Timer_{Guid.NewGuid().ToString().Substring(0, 8)}";
            CreateTimer();
            _timer.Interval = interval;
        }
        
        /// <summary>
        /// 创建TimerWrapper实例，并设置名称和间隔
        /// </summary>
        public TimerWrapper(string name, double interval, ILoggingService logger = null)
        {
            _name = name;
            _interval = interval;
            _logger = logger;
            CreateTimer();
            _timer.Interval = interval;
        }
        
        /// <summary>
        /// 创建TimerWrapper实例，并设置间隔和事件处理器
        /// </summary>
        public TimerWrapper(double interval, ElapsedEventHandler elapsedHandler, ILoggingService logger = null)
        {
            _interval = interval;
            _elapsedHandler = elapsedHandler;
            _logger = logger;
            _name = $"Timer_{Guid.NewGuid().ToString().Substring(0, 8)}";
            CreateTimer();
            _timer.Interval = interval;
            _timer.Elapsed += OnTimerElapsed;
        }
        
        /// <summary>
        /// 创建TimerWrapper实例，并设置名称、间隔和事件处理器
        /// </summary>
        public TimerWrapper(string name, double interval, ElapsedEventHandler elapsedHandler, ILoggingService logger = null)
        {
            _name = name;
            _interval = interval;
            _elapsedHandler = elapsedHandler;
            _logger = logger;
            CreateTimer();
            _timer.Interval = interval;
            _timer.Elapsed += OnTimerElapsed;
        }
        
        /// <summary>
        /// 创建一次性定时器（执行一次后自动停止）
        /// </summary>
        public static TimerWrapper CreateOneShot(double interval, Action action, string name = null, ILoggingService logger = null)
        {
            if (action == null) throw new ArgumentNullException(nameof(action));
            
            var timerName = name ?? $"OneShot_{Guid.NewGuid().ToString().Substring(0, 8)}";
            var timer = new TimerWrapper(timerName, interval, logger);
            
            timer.AddElapsedHandler((s, e) => 
            {
                try
                {
                    action();
                }
                finally
                {
                    // 一次性执行完后自动停止
                    timer.Stop();
                }
            });
            
            timer._timer.AutoReset = false; // 确保只执行一次
            timer.Start();
            
            return timer;
        }
        
        /// <summary>
        /// 创建内部Timer对象
        /// </summary>
        private void CreateTimer()
        {
            if (_isDisposed)
                return;
                
            lock (_lockObj)
            {
                if (_timer == null)
                {
                    _timer = new System.Timers.Timer();
                    _timer.AutoReset = true;
                    
                    if (_interval > 0)
                        _timer.Interval = _interval;
                        
                    if (_elapsedHandler != null)
                        _timer.Elapsed += OnTimerElapsed;
                }
            }
        }
        
        /// <summary>
        /// 安全地执行Timer操作
        /// </summary>
        private bool SafeTimerAction(Action<System.Timers.Timer> action)
        {
            if (_isDisposed)
                return false;
                
            lock (_lockObj)
            {
                try
                {
                    if (_timer != null && !_isDisposed)
                    {
                        action(_timer);
                        return true;
                    }
                }
                catch (ObjectDisposedException)
                {
                    _logger?.LogDebug("Timer已被释放，操作被忽略", WaferAligner.EventIds.EventIds.Resource_Released);
                }
                catch (Exception ex)
                {
                    _logger?.LogError(ex, $"执行Timer操作时发生错误: {ex.Message}", WaferAligner.EventIds.EventIds.Timer_Error);
                }
            }
            return false;
        }
        
        /// <summary>
        /// Timer.Elapsed事件包装处理器
        /// </summary>
        private void OnTimerElapsed(object sender, ElapsedEventArgs e)
        {
            if (_isDisposed || !_isEnabled)
                return;
                
            try
            {
                // 重置重试标志
                _retryRequested = false;
                
                // 如果启用了状态跟踪，记录执行开始
                if (_trackingEnabled)
                {
                    _lastExecutionTime = DateTime.Now;
                    _executionCount++;
                    _executionStopwatch.Restart();
                }
                
                // 调用实际的事件处理器
                if (_elapsedHandler != null)
                {
                    _elapsedHandler(sender, e);
                }
                
                // 如果启用了状态跟踪，记录执行时间
                if (_trackingEnabled)
                {
                    _executionStopwatch.Stop();
                    double elapsedMs = _executionStopwatch.Elapsed.TotalMilliseconds;
                    
                    lock (_lockObj)
                    {
                        _executionTimes.Add(elapsedMs);
                        
                        // 保持最近100次执行时间记录
                        if (_executionTimes.Count > 100)
                        {
                            _executionTimes.RemoveAt(0);
                        }
                    }
                }
                
                // 重置重试计数
                _currentRetries = 0;
                _lastException = null;
            }
            catch (Exception ex)
            {
                _lastException = ex;
                _logger?.LogError(ex, $"Timer {_name} 执行事件处理器时发生错误: {ex.Message}", WaferAligner.EventIds.EventIds.Timer_Error);
                
                // 如果启用了重试机制并且请求了重试
                if (_maxRetries > 0 && _retryRequested)
                {
                    HandleRetry();
                }
            }
        }
        
        /// <summary>
        /// 处理重试逻辑
        /// </summary>
        private void HandleRetry()
        {
            if (_currentRetries < _maxRetries)
            {
                _currentRetries++;
                _logger?.LogWarning($"Timer {_name} 正在重试操作，第 {_currentRetries}/{_maxRetries} 次", WaferAligner.EventIds.EventIds.Timer_Error);
                
                // 使用重试间隔重新安排定时器
                if (_retryIntervalMs > 0)
                {
                    SafeTimerAction(t => 
                    {
                        // 临时保存当前间隔
                        double originalInterval = t.Interval;
                        
                        // 设置重试间隔
                        t.Interval = _retryIntervalMs;
                        
                        // 确保只执行一次重试
                        bool originalAutoReset = t.AutoReset;
                        t.AutoReset = false;
                        
                        // 启动定时器进行重试
                        t.Start();
                        
                        // 重试后恢复原始设置
                        t.Elapsed += (s, e) => 
                        {
                            SafeTimerAction(timer => 
                            {
                                timer.Interval = originalInterval;
                                timer.AutoReset = originalAutoReset;
                            });
                        };
                    });
                }
            }
            else
            {
                _logger?.LogError(_lastException, $"Timer {_name} 已达到最大重试次数 {_maxRetries}，操作失败", WaferAligner.EventIds.EventIds.Timer_Error);
                _currentRetries = 0;
            }
        }
        
        /// <summary>
        /// 添加Elapsed事件处理器
        /// </summary>
        public void AddElapsedHandler(ElapsedEventHandler handler)
        {
            if (handler == null)
                return;
                
            _elapsedHandler = handler;
            SafeTimerAction(t => t.Elapsed += OnTimerElapsed);
        }
        
        /// <summary>
        /// 移除Elapsed事件处理器
        /// </summary>
        public void RemoveElapsedHandler(ElapsedEventHandler handler)
        {
            if (handler == null)
                return;
                
            if (_elapsedHandler == handler)
            {
                SafeTimerAction(t => t.Elapsed -= OnTimerElapsed);
                _elapsedHandler = null;
            }
        }
        
        /// <summary>
        /// 启动Timer
        /// </summary>
        public bool Start()
        {
            bool result = SafeTimerAction(t => {
                if (!t.Enabled)
                {
                    t.Start();
                    _isEnabled = true;
                }
            });
            return result;
        }
        
        /// <summary>
        /// 停止Timer
        /// </summary>
        public bool Stop()
        {
            bool result = SafeTimerAction(t => {
                if (t.Enabled)
                {
                    t.Stop();
                    _isEnabled = false;
                }
            });
            
            if (!result)
                _isEnabled = false;
                
            return result;
        }
        
        /// <summary>
        /// 配置重试行为
        /// </summary>
        /// <param name="maxRetries">最大重试次数</param>
        /// <param name="retryIntervalMs">重试间隔（毫秒）</param>
        public void SetRetryBehavior(int maxRetries, double retryIntervalMs)
        {
            if (maxRetries < 0) throw new ArgumentOutOfRangeException(nameof(maxRetries), "最大重试次数必须大于或等于0");
            if (retryIntervalMs < 0) throw new ArgumentOutOfRangeException(nameof(retryIntervalMs), "重试间隔必须大于或等于0");
            
            _maxRetries = maxRetries;
            _retryIntervalMs = retryIntervalMs;
        }
        
        /// <summary>
        /// 请求重试当前操作
        /// </summary>
        public void RequestRetry(Exception exception = null)
        {
            _retryRequested = true;
            if (exception != null)
            {
                _lastException = exception;
            }
        }
        
        /// <summary>
        /// 启用执行状态跟踪
        /// </summary>
        public void EnableStateTracking()
        {
            _trackingEnabled = true;
            lock (_lockObj)
            {
                _executionTimes.Clear();
            }
            _executionCount = 0;
        }
        
        /// <summary>
        /// 禁用执行状态跟踪
        /// </summary>
        public void DisableStateTracking()
        {
            _trackingEnabled = false;
        }
        
        /// <summary>
        /// 重置状态跟踪数据
        /// </summary>
        public void ResetStateTracking()
        {
            lock (_lockObj)
            {
                _executionTimes.Clear();
            }
            _executionCount = 0;
            _lastExecutionTime = DateTime.MinValue;
        }
        
        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (_isDisposed)
                return;
                
            lock (_lockObj)
            {
                if (!_isDisposed)
                {
                    _isDisposed = true;
                    _isEnabled = false;
                    
                    try
                    {
                        if (_timer != null)
                        {
                            if (_timer.Enabled)
                                _timer.Stop();
                                
                            if (_elapsedHandler != null)
                                _timer.Elapsed -= OnTimerElapsed;
                                
                            _timer.Dispose();
                            _timer = null;
                        }
                    }
                    catch (ObjectDisposedException)
                    {
                        // 已释放，忽略
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogError(ex, $"释放Timer时发生错误: {ex.Message}", WaferAligner.EventIds.EventIds.Timer_Error);
                    }
                }
            }
        }
    }
} 

