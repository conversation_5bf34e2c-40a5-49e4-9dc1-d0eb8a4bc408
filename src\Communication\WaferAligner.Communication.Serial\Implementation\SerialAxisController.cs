﻿using System;
using System.Threading;
using System.Threading.Tasks;
using WaferAligner.Services.Abstractions;
using WaferAligner.EventIds;
using WaferAligner.Communication.Serial.Interfaces;
using WaferAligner.Services.Extensions;


namespace WaferAligner.Communication.Serial.Implementation
{
    /// <summary>
    /// 串口轴控制器实现
    /// </summary>
    public class SerialAxisController : ISerialAxisController, IDisposable
    {
        private readonly ILoggingService _loggingService;
        private readonly ISerialConnectionManager _serialConnectionManager;
        private readonly ISerialComWrapper _serialComWrapper;
        private readonly string _axisName;
        private readonly uint _axisAddress;
        private bool _disposed = false;
        
        // 添加日志抑制相关字段
        private DateTime _lastConnectionWarningTime = DateTime.MinValue;
        private const int ConnectionWarningIntervalSeconds = 30; // 连接警告间隔时间（秒）
        private int _connectionWarningCount = 0;
        private const int MaxConnectionWarningCount = 5; // 最大连续警告次数
        
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="axisName">轴名称</param>
        /// <param name="loggingService">日志服务</param>
        /// <param name="serialConnectionManager">串口连接管理器</param>
        /// <param name="serialComWrapper">串口通信封装</param>
        public SerialAxisController(
            string axisName, 
            ILoggingService loggingService, 
            ISerialConnectionManager serialConnectionManager,
            ISerialComWrapper serialComWrapper)
        {
            _axisName = axisName ?? throw new ArgumentNullException(nameof(axisName));
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
            _serialConnectionManager = serialConnectionManager ?? throw new ArgumentNullException(nameof(serialConnectionManager));
            _serialComWrapper = serialComWrapper ?? throw new ArgumentNullException(nameof(serialComWrapper));
            
            // 根据轴名称设置地址
            _axisAddress = _axisName switch
            {
                "X" => 2,
                "Y" => 1,
                "R" => 3,
                _ => throw new ArgumentException($"不支持的轴名称: {_axisName}", nameof(axisName))
            };
            
            // 订阅连接状态变化事件
            _serialConnectionManager.ConnectionStateChanged += OnConnectionStateChanged;
            
            _loggingService.LogDebug($"创建{_axisName}轴控制器，地址: {_axisAddress}", WaferAligner.EventIds.EventIds.Serial_Axis_Operation);
        }
        
        /// <inheritdoc/>
        public string AxisName => _axisName;
        
        /// <inheritdoc/>
        public bool IsConnected => _serialConnectionManager.IsConnected;
        
        /// <inheritdoc/>
        public event EventHandler<SerialAxisEventArgs> PositionChanged;
        
        /// <inheritdoc/>
        public event EventHandler<SerialAxisEventArgs> StateChanged;
        
        /// <summary>
        /// 检查连接状态并记录警告日志（带抑制机制）
        /// </summary>
        /// <param name="operationName">操作名称</param>
        /// <returns>是否已连接</returns>
        private bool CheckConnectionWithLogSuppression(string operationName)
        {
            if (IsConnected)
            {
                // 已连接，重置警告计数
                _connectionWarningCount = 0;
                return true;
            }
            
            // 未连接，检查是否需要记录警告日志
            DateTime now = DateTime.Now;
            bool shouldLog = false;
            
            if (_connectionWarningCount < MaxConnectionWarningCount)
            {
                // 未达到最大连续警告次数，记录警告
                shouldLog = true;
                _connectionWarningCount++;
            }
            else if ((now - _lastConnectionWarningTime).TotalSeconds >= ConnectionWarningIntervalSeconds)
            {
                // 已达到最大连续警告次数，但已经过了抑制间隔，记录一条汇总警告
                shouldLog = true;
                // 重置计数，开始新一轮警告
                _connectionWarningCount = 1;
            }
            
            if (shouldLog)
            {
                _lastConnectionWarningTime = now;
                if (_connectionWarningCount == MaxConnectionWarningCount)
                {
                    // 达到最大次数时，记录一条特殊警告，说明后续警告将被抑制
                    _loggingService.LogWarning($"{_axisName}轴未连接，无法{operationName}。后续类似警告将被抑制{ConnectionWarningIntervalSeconds}秒", 
                        WaferAligner.EventIds.EventIds.Serial_Axis_Operation);
                }
                else
                {
                    _loggingService.LogWarning($"{_axisName}轴未连接，无法{operationName}", 
                        WaferAligner.EventIds.EventIds.Serial_Axis_Operation);
                }
            }
            
            return false;
        }
        
        /// <inheritdoc/>
        public async Task<bool> EnableAxisAsync(CancellationToken cancellationToken = default)
        {
            if (!CheckConnectionWithLogSuppression("使能"))
            {
                return false;
            }
            
            try
            {
                return await Task.Run(() => 
                {
                    // 检查当前使能状态
                    int currentState = _serialComWrapper.GetAxisStatus((int)_axisAddress, 1);
                    if (currentState == 1) // 已经使能
                    {
                        _loggingService.LogDebug($"{_axisName}轴已处于使能状态", WaferAligner.EventIds.EventIds.Serial_Axis_Enabled);
                        return true;
                    }
                    
                    // 执行使能
                    int result = _serialComWrapper.AxisEnable(_axisAddress, 'K');
                    if (result != 1)
                    {
                        _loggingService.LogWarning($"{_axisName}轴使能失败，错误码: {result}", WaferAligner.EventIds.EventIds.Serial_Axis_Operation);
                        return false;
                    }
                    
                    // 等待使能完成
                    int retryCount = 40;
                    while (retryCount > 0)
                    {
                        retryCount--;
                        if (_serialComWrapper.GetAxisStatus((int)_axisAddress, 1) == 0)
                        {
                            Thread.Sleep(10);
                        }
                        else
                        {
                            // 发布事件
                            RaiseStateChanged(1);
                            _loggingService.LogInformation($"{_axisName}轴使能成功", WaferAligner.EventIds.EventIds.Serial_Axis_Enabled);
                            return true;
                        }
                    }
                    
                    // 使能超时，尝试取消使能
                    _serialComWrapper.AxisEnable(_axisAddress, 'F');
                    _loggingService.LogError($"{_axisName}轴使能超时", WaferAligner.EventIds.EventIds.Serial_Axis_Operation);
                    return false;
                }, cancellationToken);
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"{_axisName}轴使能操作异常", WaferAligner.EventIds.EventIds.Serial_Axis_Operation);
                return false;
            }
        }
        
        /// <inheritdoc/>
        public async Task<int> GetPositionAsync(CancellationToken cancellationToken = default)
        {
            if (!CheckConnectionWithLogSuppression("读取位置"))
            {
                return 0;
            }
            
            try
            {
                return await Task.Run(() => 
                {
                    int position = _serialComWrapper.ReadPosition(_axisAddress);
                    
                    // 发布位置变化事件
                    RaisePositionChanged(position);
                    
                    return position;
                }, cancellationToken);
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"{_axisName}轴读取位置异常", WaferAligner.EventIds.EventIds.Serial_Read_Error);
                return 0;
            }
        }
        
        /// <inheritdoc/>
        public async Task<bool> MoveToPositionAsync(int position, bool isRelative = false, CancellationToken cancellationToken = default)
        {
            if (!CheckConnectionWithLogSuppression("移动"))
            {
                return false;
            }
            
            try
            {
                return await Task.Run(() => 
                {
                    int result;
                    
                    if (isRelative)
                    {
                        result = _serialComWrapper.PositionRelativeMove(_axisAddress, position);
                    }
                    else
                    {
                        result = _serialComWrapper.PositionAbsoluteMove(_axisAddress, position);
                    }
                    
                    bool success = result == 1;
                    if (success)
                    {
                        _loggingService.LogInformation($"{_axisName}轴开始移动到{(isRelative ? "相对" : "绝对")}位置: {position}", WaferAligner.EventIds.EventIds.Serial_Axis_Moving);
                    }
                    else
                    {
                        _loggingService.LogWarning($"{_axisName}轴移动失败，错误码: {result}", WaferAligner.EventIds.EventIds.Serial_Axis_Operation);
                    }
                    
                    return success;
                }, cancellationToken);
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"{_axisName}轴移动操作异常", WaferAligner.EventIds.EventIds.Serial_Axis_Operation);
                return false;
            }
        }
        
        /// <inheritdoc/>
        public async Task<bool> StopAsync(CancellationToken cancellationToken = default)
        {
            if (!CheckConnectionWithLogSuppression("停止"))
            {
                return false;
            }
            
            try
            {
                return await Task.Run(() => 
                {
                    int result = _serialComWrapper.AxisStop(_axisAddress);
                    bool success = result == 1;
                    
                    if (success)
                    {
                        _loggingService.LogInformation($"{_axisName}轴停止成功", WaferAligner.EventIds.EventIds.Serial_Axis_Stopped);
                    }
                    else
                    {
                        _loggingService.LogWarning($"{_axisName}轴停止失败，错误码: {result}", WaferAligner.EventIds.EventIds.Serial_Axis_Operation);
                    }
                    
                    return success;
                }, cancellationToken);
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"{_axisName}轴停止操作异常", WaferAligner.EventIds.EventIds.Serial_Axis_Stopped);
                return false;
            }
        }
        
        /// <inheritdoc/>
        public async Task<bool> HomeAsync(CancellationToken cancellationToken = default)
        {
            if (!CheckConnectionWithLogSuppression("回零"))
            {
                return false;
            }
            
            try
            {
                return await Task.Run(() => 
                {
                    // 设置零位置
                    int result = _serialComWrapper.SetZeroPosition(_axisAddress);
                    bool success = result == 1;
                    
                    if (success)
                    {
                        _loggingService.LogInformation($"{_axisName}轴回零成功", WaferAligner.EventIds.EventIds.Serial_Axis_Operation);
                    }
                    else
                    {
                        _loggingService.LogWarning($"{_axisName}轴回零失败，错误码: {result}", WaferAligner.EventIds.EventIds.Serial_Axis_Operation);
                    }
                    
                    return success;
                }, cancellationToken);
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"{_axisName}轴回零操作异常", WaferAligner.EventIds.EventIds.Serial_Axis_Operation);
                return false;
            }
        }
        
        /// <inheritdoc/>
        public async Task<bool> ClearErrorAsync(CancellationToken cancellationToken = default)
        {
            if (!CheckConnectionWithLogSuppression("清除错误"))
            {
                return false;
            }
            
            try
            {
                return await Task.Run(() => 
                {
                    int result = _serialComWrapper.ClearMotorError((int)_axisAddress);
                    bool success = result == 1;
                    
                    if (success)
                    {
                        _loggingService.LogInformation($"{_axisName}轴清除错误成功", WaferAligner.EventIds.EventIds.Serial_Axis_Operation);
                    }
                    else
                    {
                        _loggingService.LogWarning($"{_axisName}轴清除错误失败，错误码: {result}", WaferAligner.EventIds.EventIds.Serial_Axis_Operation);
                    }
                    
                    return success;
                }, cancellationToken);
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"{_axisName}轴清除错误操作异常", WaferAligner.EventIds.EventIds.Serial_Axis_Operation);
                return false;
            }
        }
        
        /// <inheritdoc/>
        public async Task<int> GetRunStateAsync(CancellationToken cancellationToken = default)
        {
            if (!CheckConnectionWithLogSuppression("获取运行状态"))
            {
                return -1;
            }
            
            try
            {
                return await Task.Run(() => 
                {
                    return _serialComWrapper.GetAxisStatus((int)_axisAddress, 2);
                }, cancellationToken);
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"{_axisName}轴获取运行状态异常", WaferAligner.EventIds.EventIds.Serial_Read_Error);
                return -1;
            }
        }
        
        /// <inheritdoc/>
        public async Task<int> GetAlarmStateAsync(CancellationToken cancellationToken = default)
        {
            if (!CheckConnectionWithLogSuppression("获取报警状态"))
            {
                return -1;
            }
            
            try
            {
                return await Task.Run(() => 
                {
                    return _serialComWrapper.GetAxisStatus((int)_axisAddress, 3);
                }, cancellationToken);
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"{_axisName}轴获取报警状态异常", WaferAligner.EventIds.EventIds.Serial_Read_Error);
                return -1;
            }
        }
        
        /// <inheritdoc/>
        public async Task<bool> SetRunSpeedAsync(uint speed, CancellationToken cancellationToken = default)
        {
            if (!CheckConnectionWithLogSuppression("设置运行速度"))
            {
                return false;
            }
            
            try
            {
                return await Task.Run(() => 
                {
                    int result = _serialComWrapper.SetSpeed(_axisAddress, 'R', speed);
                    bool success = result == 1;
                    
                    if (success)
                    {
                        _loggingService.LogInformation($"{_axisName}轴设置运行速度为{speed}成功", WaferAligner.EventIds.EventIds.Serial_Axis_Operation);
                    }
                    else
                    {
                        _loggingService.LogWarning($"{_axisName}轴设置运行速度失败，错误码: {result}", WaferAligner.EventIds.EventIds.Serial_Axis_Operation);
                    }
                    
                    return success;
                }, cancellationToken);
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"{_axisName}轴设置运行速度异常", WaferAligner.EventIds.EventIds.Serial_Axis_Operation);
                return false;
            }
        }
        
        /// <inheritdoc/>
        public async Task<bool> SetJogSpeedAsync(uint speed, CancellationToken cancellationToken = default)
        {
            if (!CheckConnectionWithLogSuppression("设置点动速度"))
            {
                return false;
            }
            
            try
            {
                return await Task.Run(() => 
                {
                    int result = _serialComWrapper.SetSpeed(_axisAddress, 'J', speed);
                    bool success = result == 1;
                    
                    if (success)
                    {
                        _loggingService.LogInformation($"{_axisName}轴设置点动速度为{speed}成功", WaferAligner.EventIds.EventIds.Serial_Axis_Operation);
                    }
                    else
                    {
                        _loggingService.LogWarning($"{_axisName}轴设置点动速度失败，错误码: {result}", WaferAligner.EventIds.EventIds.Serial_Axis_Operation);
                    }
                    
                    return success;
                }, cancellationToken);
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"{_axisName}轴设置点动速度异常", WaferAligner.EventIds.EventIds.Serial_Axis_Operation);
                return false;
            }
        }
        
        /// <summary>
        /// 连接状态变化事件处理
        /// </summary>
        private void OnConnectionStateChanged(object sender, SerialConnectionEventArgs e)
        {
            if (e.IsConnected)
            {
                _loggingService.LogDebug($"{_axisName}轴连接状态更新: 已连接", WaferAligner.EventIds.EventIds.Serial_Connection_Succeeded);
            }
            else
            {
                _loggingService.LogDebug($"{_axisName}轴连接状态更新: 已断开", WaferAligner.EventIds.EventIds.Serial_Connection_Failed);
            }
            
            // 发布状态变化事件
            RaiseStateChanged(e.IsConnected ? 1 : 0);
        }
        
        /// <summary>
        /// 发布位置变化事件
        /// </summary>
        /// <param name="position">位置</param>
        private void RaisePositionChanged(int position)
        {
            try
            {
                // 触发传统事件
                PositionChanged?.Invoke(this, new SerialAxisEventArgs 
                { 
                    AxisName = _axisName,
                    Position = position
                });
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"发布{_axisName}轴位置变化事件异常", WaferAligner.EventIds.EventIds.Serial_Axis_Operation);
            }
        }
        
        /// <summary>
        /// 发布状态变化事件
        /// </summary>
        /// <param name="state">状态</param>
        private void RaiseStateChanged(int state)
        {
            try
            {
                // 触发传统事件
                StateChanged?.Invoke(this, new SerialAxisEventArgs 
                { 
                    AxisName = _axisName,
                    State = state
                });
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"发布{_axisName}轴状态变化事件异常", WaferAligner.EventIds.EventIds.Serial_Axis_Operation);
            }
        }
        
        /// <inheritdoc/>
        public void Dispose()
        {
            if (_disposed) return;
            _disposed = true;
            
            try
            {
                // 取消事件订阅
                _serialConnectionManager.ConnectionStateChanged -= OnConnectionStateChanged;
                
                _loggingService.LogDebug($"{_axisName}轴控制器资源已释放", WaferAligner.EventIds.EventIds.Serial_Resource_Released);
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"{_axisName}轴控制器释放资源异常", WaferAligner.EventIds.EventIds.Serial_Resource_Released);
            }
        }
    }
}
