using WaferAligner.Services.Logging.Abstractions;
using Microsoft.Extensions.Logging;
using System;
using System.Text.Json;

namespace WaferAligner.Services.Logging.Extensions
{
    /// <summary>
    /// 日志服务扩展方法 - 重构版本
    /// 移除编译时条件限制，改进默认参数处理，添加新功能方法
    /// </summary>
    public static class LoggerExtensions
    {
        #region 基础日志方法 - 改进版本

        public static void LogTrace(this ILoggingService service, string message, EventId eventId)
        {
            service.Log(LogLevel.Trace, eventId, message);
        }

        public static void LogDebug(this ILoggingService service, string message, EventId eventId)
        {
            // 移除编译时条件，改为运行时检查
            service.Log(LogLevel.Debug, eventId, message);
        }

        public static void LogInformation(this ILoggingService service, string message, EventId eventId)
        {
            service.Log(LogLevel.Information, eventId, message);
        }

        public static void LogWarning(this ILoggingService service, string message, EventId eventId)
        {
            service.Log(LogLevel.Warning, eventId, message);
        }

        public static void LogError(this ILoggingService service, string message, EventId eventId)
        {
            service.Log(LogLevel.Error, eventId, message);
        }

        public static void LogCritical(this ILoggingService service, string message, EventId eventId)
        {
            service.Log(LogLevel.Critical, eventId, message);
        }

        #endregion

        #region 异常日志方法 - 增强版本

        public static void LogWarning(this ILoggingService service, Exception exception, string message, EventId eventId)
        {
            service.Log<string>(LogLevel.Warning, eventId, null, exception, (_, ex) => $"{message}: {ex.Message}");
        }

        public static void LogError(this ILoggingService service, Exception exception, string message, EventId eventId)
        {
            service.Log<string>(LogLevel.Error, eventId, null, exception, (_, ex) => $"{message}: {ex.Message}");
        }

        public static void LogCritical(this ILoggingService service, Exception exception, string message, EventId eventId)
        {
            service.Log<string>(LogLevel.Critical, eventId, null, exception, (_, ex) => $"{message}: {ex.Message}");
        }

        #endregion

        #region 兼容性方法 - 保持现有调用方式

        // 匹配现有代码的调用方式: Logger.LogXxx("message", EventId)
        public static void LogTrace(this ILoggingService service, string message, EventId eventId)
        {
            service.Log(LogLevel.Trace, eventId, message);
        }

        public static void LogDebug(this ILoggingService service, string message, EventId eventId)
        {
            service.Log(LogLevel.Debug, eventId, message);
        }

        public static void LogInformation(this ILoggingService service, string message, EventId eventId)
        {
            service.Log(LogLevel.Information, eventId, message);
        }

        public static void LogWarning(this ILoggingService service, string message, EventId eventId)
        {
            service.Log(LogLevel.Warning, eventId, message);
        }

        public static void LogError(this ILoggingService service, string message, EventId eventId)
        {
            service.Log(LogLevel.Error, eventId, message);
        }



        public static void LogWarning(this ILoggingService service, Exception exception, string message, EventId? id = null)
        {
            var eventId = id ?? new EventId(0, "Warning");
            service.Log<string>(LogLevel.Warning, eventId, null, exception, (_, ex) => $"{message}: {ex.Message}");
        }

        public static void LogError(this ILoggingService service, Exception exception, string message, EventId? id = null)
        {
            var eventId = id ?? new EventId(0, "Error");
            service.Log<string>(LogLevel.Error, eventId, null, exception, (_, ex) => $"{message}: {ex.Message}");
        }

        // 保持与Microsoft.Extensions.Logging.LoggerExtensions相同的签名
        public static void LogWarning(this ILoggingService service, Exception exception, string message)
        {
            service.Log<string>(LogLevel.Warning, new EventId(0, "Warning"), null, exception, (_, ex) => $"{message}: {ex.Message}");
        }

        public static void LogError(this ILoggingService service, Exception exception, string message)
        {
            service.Log<string>(LogLevel.Error, new EventId(0, "Error"), null, exception, (_, ex) => $"{message}: {ex.Message}");
        }

        #endregion

        #region 新增功能方法

        /// <summary>
        /// 记录结构化日志
        /// </summary>
        public static void LogInformationStructured<T>(this ILoggingService service, string message, EventId eventId, T data)
        {
            try
            {
                var serializedData = JsonSerializer.Serialize(data, new JsonSerializerOptions
                {
                    WriteIndented = false
                });
                var structuredMessage = $"{message} | Data: {serializedData}";
                service.Log(LogLevel.Information, eventId, structuredMessage);
            }
            catch (Exception ex)
            {
                // 序列化失败时降级处理
                var fallbackMessage = $"{message} | Data: [序列化失败: {ex.Message}]";
                service.Log(LogLevel.Information, eventId, fallbackMessage);
            }
        }

        /// <summary>
        /// 记录带模块名称的日志
        /// </summary>
        public static void LogInformationWithModule(this ILoggingService service, string moduleName, string message, EventId eventId)
        {
            var moduleMessage = $"[{moduleName}] {message}";
            service.Log(LogLevel.Information, eventId, moduleMessage);
        }

        /// <summary>
        /// 记录性能日志
        /// </summary>
        public static IDisposable LogPerformance(this ILoggingService service, string operationName, EventId eventId)
        {
            // 记录开始日志
            service.Log(LogLevel.Debug, eventId, $"开始操作: {operationName}");
            return new DisposableAction(() =>
                service.Log(LogLevel.Debug, eventId, $"完成操作: {operationName}"));
        }

        #endregion
    }

    /// <summary>
    /// 简单的可释放操作包装器
    /// </summary>
    internal class DisposableAction : IDisposable
    {
        private readonly Action _action;
        private bool _disposed = false;

        public DisposableAction(Action action)
        {
            _action = action;
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                _action?.Invoke();
                _disposed = true;
            }
        }
    }
}
