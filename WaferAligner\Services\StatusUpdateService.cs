using System;
using System.Collections.Generic;
using System.Threading;
using WaferAligner.Services.Logging.Abstractions;
using WaferAligner.Services.Logging.Extensions;

namespace WaferAligner.Services
{
    /// <summary>
    /// 状态更新服务实现，用于替代静态访问模式
    /// </summary>
    public class StatusUpdateService : IStatusUpdateService
    {
        private readonly ILoggingService _loggingService;
        private readonly object _lockObj = new object();
        private string _currentStatus = string.Empty;
        private readonly List<Action<string>> _statusUpdateHandlers = new List<Action<string>>();
        
        public StatusUpdateService(ILoggingService loggingService)
        {
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
        }
        
        /// <summary>
        /// 更新状态文本
        /// </summary>
        /// <param name="status">状态文本</param>
        public void UpdateStatus(string status)
        {
            if (string.IsNullOrEmpty(status))
            {
                _loggingService?.LogWarning("尝试更新空状态文本", WaferAligner.EventIds.EventIds.Status_Update_Warning);
                return;
            }
            
            try
            {
                lock (_lockObj)
                {
                    _currentStatus = status;
                }
                
                // 通知所有注册的处理器
                NotifyStatusUpdateHandlers(status);
                
                _loggingService?.LogDebug($"状态已更新: {status}", WaferAligner.EventIds.EventIds.Status_Updated);
            }
            catch (Exception ex)
            {
                _loggingService?.LogError(ex, $"更新状态时发生错误: {ex.Message}", WaferAligner.EventIds.EventIds.Status_Update_Error);
            }
        }
        
        /// <summary>
        /// 获取当前状态文本
        /// </summary>
        /// <returns>当前状态文本</returns>
        public string GetCurrentStatus()
        {
            lock (_lockObj)
            {
                return _currentStatus;
            }
        }
        
        /// <summary>
        /// 注册状态更新事件处理器
        /// </summary>
        /// <param name="handler">状态更新事件处理器</param>
        public void RegisterStatusUpdateHandler(Action<string> handler)
        {
            if (handler == null)
            {
                _loggingService?.LogWarning("尝试注册空的状态更新处理器", WaferAligner.EventIds.EventIds.Status_Update_Warning);
                return;
            }
            
            lock (_lockObj)
            {
                if (!_statusUpdateHandlers.Contains(handler))
                {
                    _statusUpdateHandlers.Add(handler);
                    _loggingService?.LogDebug("状态更新处理器已注册", WaferAligner.EventIds.EventIds.Status_Update_Handler_Registered);
                }
            }
        }
        
        /// <summary>
        /// 注销状态更新事件处理器
        /// </summary>
        /// <param name="handler">状态更新事件处理器</param>
        public void UnregisterStatusUpdateHandler(Action<string> handler)
        {
            if (handler == null)
            {
                _loggingService?.LogWarning("尝试注销空的状态更新处理器", WaferAligner.EventIds.EventIds.Status_Update_Warning);
                return;
            }
            
            lock (_lockObj)
            {
                if (_statusUpdateHandlers.Remove(handler))
                {
                    _loggingService?.LogDebug("状态更新处理器已注销", WaferAligner.EventIds.EventIds.Status_Update_Handler_Unregistered);
                }
            }
        }
        
        /// <summary>
        /// 通知所有注册的状态更新处理器
        /// </summary>
        /// <param name="status">状态文本</param>
        private void NotifyStatusUpdateHandlers(string status)
        {
            List<Action<string>> handlersCopy;
            lock (_lockObj)
            {
                handlersCopy = new List<Action<string>>(_statusUpdateHandlers);
            }
            
            foreach (var handler in handlersCopy)
            {
                try
                {
                    handler(status);
                }
                catch (Exception ex)
                {
                    _loggingService?.LogError(ex, $"状态更新处理器执行失败: {ex.Message}", WaferAligner.EventIds.EventIds.Status_Update_Handler_Error);
                }
            }
        }
    }
} 