{"version": 3, "targets": {"net6.0-windows7.0": {"Microsoft.Extensions.DependencyInjection/9.0.5": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5"}, "compile": {"lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.targets": {}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.6": {"type": "package", "compile": {"lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.Abstractions.targets": {}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.6": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "System.Buffers": "4.5.1", "System.Diagnostics.DiagnosticSource": "9.0.6", "System.Memory": "4.5.5"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Abstractions.targets": {}}}, "Microsoft.NETCore.Platforms/1.1.0": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Newtonsoft.Json/13.0.3": {"type": "package", "compile": {"lib/net6.0/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"related": ".xml"}}}, "runtime.android-arm.runtime.native.System.IO.Ports/9.0.7": {"type": "package", "runtimeTargets": {"runtimes/android-arm/native/libSystem.IO.Ports.Native.so": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/_._": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/_._": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/_._": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/_._": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/_._": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-bionic-arm64/native/_._": {"assetType": "native", "rid": "linux-bionic-arm64"}, "runtimes/linux-bionic-x64/native/_._": {"assetType": "native", "rid": "linux-bionic-x64"}, "runtimes/linux-musl-arm/native/_._": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/_._": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/_._": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/_._": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/_._": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/_._": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/_._": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/_._": {"assetType": "native", "rid": "osx-x64"}}}, "runtime.android-arm64.runtime.native.System.IO.Ports/9.0.7": {"type": "package", "runtimeTargets": {"runtimes/android-arm/native/_._": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/libSystem.IO.Ports.Native.so": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/_._": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/_._": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/_._": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/_._": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-bionic-arm64/native/_._": {"assetType": "native", "rid": "linux-bionic-arm64"}, "runtimes/linux-bionic-x64/native/_._": {"assetType": "native", "rid": "linux-bionic-x64"}, "runtimes/linux-musl-arm/native/_._": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/_._": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/_._": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/_._": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/_._": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/_._": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/_._": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/_._": {"assetType": "native", "rid": "osx-x64"}}}, "runtime.android-x64.runtime.native.System.IO.Ports/9.0.7": {"type": "package", "runtimeTargets": {"runtimes/android-arm/native/_._": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/_._": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/libSystem.IO.Ports.Native.so": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/_._": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/_._": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/_._": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-bionic-arm64/native/_._": {"assetType": "native", "rid": "linux-bionic-arm64"}, "runtimes/linux-bionic-x64/native/_._": {"assetType": "native", "rid": "linux-bionic-x64"}, "runtimes/linux-musl-arm/native/_._": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/_._": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/_._": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/_._": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/_._": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/_._": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/_._": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/_._": {"assetType": "native", "rid": "osx-x64"}}}, "runtime.android-x86.runtime.native.System.IO.Ports/9.0.7": {"type": "package", "runtimeTargets": {"runtimes/android-arm/native/_._": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/_._": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/_._": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/libSystem.IO.Ports.Native.so": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/_._": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/_._": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-bionic-arm64/native/_._": {"assetType": "native", "rid": "linux-bionic-arm64"}, "runtimes/linux-bionic-x64/native/_._": {"assetType": "native", "rid": "linux-bionic-x64"}, "runtimes/linux-musl-arm/native/_._": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/_._": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/_._": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/_._": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/_._": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/_._": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/_._": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/_._": {"assetType": "native", "rid": "osx-x64"}}}, "runtime.linux-arm.runtime.native.System.IO.Ports/9.0.7": {"type": "package", "runtimeTargets": {"runtimes/android-arm/native/_._": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/_._": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/_._": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/_._": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/libSystem.IO.Ports.Native.so": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/_._": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-bionic-arm64/native/_._": {"assetType": "native", "rid": "linux-bionic-arm64"}, "runtimes/linux-bionic-x64/native/_._": {"assetType": "native", "rid": "linux-bionic-x64"}, "runtimes/linux-musl-arm/native/_._": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/_._": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/_._": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/_._": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/_._": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/_._": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/_._": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/_._": {"assetType": "native", "rid": "osx-x64"}}}, "runtime.linux-arm64.runtime.native.System.IO.Ports/9.0.7": {"type": "package", "runtimeTargets": {"runtimes/android-arm/native/_._": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/_._": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/_._": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/_._": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/_._": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/libSystem.IO.Ports.Native.so": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-bionic-arm64/native/_._": {"assetType": "native", "rid": "linux-bionic-arm64"}, "runtimes/linux-bionic-x64/native/_._": {"assetType": "native", "rid": "linux-bionic-x64"}, "runtimes/linux-musl-arm/native/_._": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/_._": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/_._": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/_._": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/_._": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/_._": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/_._": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/_._": {"assetType": "native", "rid": "osx-x64"}}}, "runtime.linux-bionic-arm64.runtime.native.System.IO.Ports/9.0.7": {"type": "package", "runtimeTargets": {"runtimes/android-arm/native/_._": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/_._": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/_._": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/_._": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/_._": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/_._": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-bionic-arm64/native/libSystem.IO.Ports.Native.so": {"assetType": "native", "rid": "linux-bionic-arm64"}, "runtimes/linux-bionic-x64/native/_._": {"assetType": "native", "rid": "linux-bionic-x64"}, "runtimes/linux-musl-arm/native/_._": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/_._": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/_._": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/_._": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/_._": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/_._": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/_._": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/_._": {"assetType": "native", "rid": "osx-x64"}}}, "runtime.linux-bionic-x64.runtime.native.System.IO.Ports/9.0.7": {"type": "package", "runtimeTargets": {"runtimes/android-arm/native/_._": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/_._": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/_._": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/_._": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/_._": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/_._": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-bionic-arm64/native/_._": {"assetType": "native", "rid": "linux-bionic-arm64"}, "runtimes/linux-bionic-x64/native/libSystem.IO.Ports.Native.so": {"assetType": "native", "rid": "linux-bionic-x64"}, "runtimes/linux-musl-arm/native/_._": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/_._": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/_._": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/_._": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/_._": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/_._": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/_._": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/_._": {"assetType": "native", "rid": "osx-x64"}}}, "runtime.linux-musl-arm.runtime.native.System.IO.Ports/9.0.7": {"type": "package", "runtimeTargets": {"runtimes/android-arm/native/_._": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/_._": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/_._": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/_._": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/_._": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/_._": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-bionic-arm64/native/_._": {"assetType": "native", "rid": "linux-bionic-arm64"}, "runtimes/linux-bionic-x64/native/_._": {"assetType": "native", "rid": "linux-bionic-x64"}, "runtimes/linux-musl-arm/native/libSystem.IO.Ports.Native.so": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/_._": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/_._": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/_._": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/_._": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/_._": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/_._": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/_._": {"assetType": "native", "rid": "osx-x64"}}}, "runtime.linux-musl-arm64.runtime.native.System.IO.Ports/9.0.7": {"type": "package", "runtimeTargets": {"runtimes/android-arm/native/_._": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/_._": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/_._": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/_._": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/_._": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/_._": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-bionic-arm64/native/_._": {"assetType": "native", "rid": "linux-bionic-arm64"}, "runtimes/linux-bionic-x64/native/_._": {"assetType": "native", "rid": "linux-bionic-x64"}, "runtimes/linux-musl-arm/native/_._": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/libSystem.IO.Ports.Native.so": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/_._": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/_._": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/_._": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/_._": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/_._": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/_._": {"assetType": "native", "rid": "osx-x64"}}}, "runtime.linux-musl-x64.runtime.native.System.IO.Ports/9.0.7": {"type": "package", "runtimeTargets": {"runtimes/android-arm/native/_._": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/_._": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/_._": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/_._": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/_._": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/_._": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-bionic-arm64/native/_._": {"assetType": "native", "rid": "linux-bionic-arm64"}, "runtimes/linux-bionic-x64/native/_._": {"assetType": "native", "rid": "linux-bionic-x64"}, "runtimes/linux-musl-arm/native/_._": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/_._": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/libSystem.IO.Ports.Native.so": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/_._": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/_._": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/_._": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/_._": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/_._": {"assetType": "native", "rid": "osx-x64"}}}, "runtime.linux-x64.runtime.native.System.IO.Ports/9.0.7": {"type": "package", "runtimeTargets": {"runtimes/android-arm/native/_._": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/_._": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/_._": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/_._": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/_._": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/_._": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-bionic-arm64/native/_._": {"assetType": "native", "rid": "linux-bionic-arm64"}, "runtimes/linux-bionic-x64/native/_._": {"assetType": "native", "rid": "linux-bionic-x64"}, "runtimes/linux-musl-arm/native/_._": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/_._": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/_._": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/libSystem.IO.Ports.Native.so": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/_._": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/_._": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/_._": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/_._": {"assetType": "native", "rid": "osx-x64"}}}, "runtime.maccatalyst-arm64.runtime.native.System.IO.Ports/9.0.7": {"type": "package", "runtimeTargets": {"runtimes/android-arm/native/_._": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/_._": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/_._": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/_._": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/_._": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/_._": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-bionic-arm64/native/_._": {"assetType": "native", "rid": "linux-bionic-arm64"}, "runtimes/linux-bionic-x64/native/_._": {"assetType": "native", "rid": "linux-bionic-x64"}, "runtimes/linux-musl-arm/native/_._": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/_._": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/_._": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/_._": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/libSystem.IO.Ports.Native.dylib": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/_._": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/_._": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/_._": {"assetType": "native", "rid": "osx-x64"}}}, "runtime.maccatalyst-x64.runtime.native.System.IO.Ports/9.0.7": {"type": "package", "runtimeTargets": {"runtimes/android-arm/native/_._": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/_._": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/_._": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/_._": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/_._": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/_._": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-bionic-arm64/native/_._": {"assetType": "native", "rid": "linux-bionic-arm64"}, "runtimes/linux-bionic-x64/native/_._": {"assetType": "native", "rid": "linux-bionic-x64"}, "runtimes/linux-musl-arm/native/_._": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/_._": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/_._": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/_._": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/_._": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/libSystem.IO.Ports.Native.dylib": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/_._": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/_._": {"assetType": "native", "rid": "osx-x64"}}}, "runtime.native.System.IO.Ports/9.0.7": {"type": "package", "dependencies": {"runtime.android-arm.runtime.native.System.IO.Ports": "9.0.7", "runtime.android-arm64.runtime.native.System.IO.Ports": "9.0.7", "runtime.android-x64.runtime.native.System.IO.Ports": "9.0.7", "runtime.android-x86.runtime.native.System.IO.Ports": "9.0.7", "runtime.linux-arm.runtime.native.System.IO.Ports": "9.0.7", "runtime.linux-arm64.runtime.native.System.IO.Ports": "9.0.7", "runtime.linux-bionic-arm64.runtime.native.System.IO.Ports": "9.0.7", "runtime.linux-bionic-x64.runtime.native.System.IO.Ports": "9.0.7", "runtime.linux-musl-arm.runtime.native.System.IO.Ports": "9.0.7", "runtime.linux-musl-arm64.runtime.native.System.IO.Ports": "9.0.7", "runtime.linux-musl-x64.runtime.native.System.IO.Ports": "9.0.7", "runtime.linux-x64.runtime.native.System.IO.Ports": "9.0.7", "runtime.maccatalyst-arm64.runtime.native.System.IO.Ports": "9.0.7", "runtime.maccatalyst-x64.runtime.native.System.IO.Ports": "9.0.7", "runtime.osx-arm64.runtime.native.System.IO.Ports": "9.0.7", "runtime.osx-x64.runtime.native.System.IO.Ports": "9.0.7"}}, "runtime.osx-arm64.runtime.native.System.IO.Ports/9.0.7": {"type": "package", "runtimeTargets": {"runtimes/android-arm/native/_._": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/_._": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/_._": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/_._": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/_._": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/_._": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-bionic-arm64/native/_._": {"assetType": "native", "rid": "linux-bionic-arm64"}, "runtimes/linux-bionic-x64/native/_._": {"assetType": "native", "rid": "linux-bionic-x64"}, "runtimes/linux-musl-arm/native/_._": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/_._": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/_._": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/_._": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/_._": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/_._": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/libSystem.IO.Ports.Native.dylib": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/_._": {"assetType": "native", "rid": "osx-x64"}}}, "runtime.osx-x64.runtime.native.System.IO.Ports/9.0.7": {"type": "package", "runtimeTargets": {"runtimes/android-arm/native/_._": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/_._": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/_._": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/_._": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/_._": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/_._": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-bionic-arm64/native/_._": {"assetType": "native", "rid": "linux-bionic-arm64"}, "runtimes/linux-bionic-x64/native/_._": {"assetType": "native", "rid": "linux-bionic-x64"}, "runtimes/linux-musl-arm/native/_._": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/_._": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/_._": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/_._": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/_._": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/_._": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/_._": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/libSystem.IO.Ports.Native.dylib": {"assetType": "native", "rid": "osx-x64"}}}, "System.Buffers/4.5.1": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "System.Diagnostics.DiagnosticSource/9.0.6": {"type": "package", "dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/netstandard2.0/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp2.0/System.Diagnostics.DiagnosticSource.targets": {}}}, "System.IO/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "compile": {"ref/netstandard1.5/System.IO.dll": {"related": ".xml"}}}, "System.IO.Ports/9.0.7": {"type": "package", "dependencies": {"runtime.native.System.IO.Ports": "9.0.7"}, "compile": {"lib/netstandard2.0/System.IO.Ports.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.IO.Ports.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp2.0/System.IO.Ports.targets": {}}}, "System.Memory/4.5.5": {"type": "package", "compile": {"ref/netcoreapp2.1/_._": {}}, "runtime": {"lib/netcoreapp2.1/_._": {}}}, "System.Reactive/5.0.0": {"type": "package", "compile": {"lib/net5.0/System.Reactive.dll": {"related": ".xml"}}, "runtime": {"lib/net5.0/System.Reactive.dll": {"related": ".xml"}}, "build": {"buildTransitive/net5.0/_._": {}}}, "System.Reflection/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.5/System.Reflection.dll": {"related": ".xml"}}}, "System.Reflection.Primitives/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/System.Reflection.Primitives.dll": {"related": ".xml"}}}, "System.Runtime/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}, "compile": {"ref/netstandard1.5/System.Runtime.dll": {"related": ".xml"}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "compile": {"lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Text.Encoding/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Text.Encoding.dll": {"related": ".xml"}}}, "System.Threading.Tasks/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Threading.Tasks.dll": {"related": ".xml"}}}, "WaferAligner.EventIds/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v6.0", "dependencies": {"Microsoft.Extensions.Logging.Abstractions": "9.0.6"}, "compile": {"bin/placeholder/WaferAligner.EventIds.dll": {}}, "runtime": {"bin/placeholder/WaferAligner.EventIds.dll": {}}}, "WaferAligner.Infrastructure.Common/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v6.0", "dependencies": {"Microsoft.Extensions.Logging.Abstractions": "9.0.6", "System.Reactive": "5.0.0", "System.Reflection": "4.3.0", "WaferAligner.EventIds": "1.0.0", "WaferAligner.Services.Logging": "1.0.0"}, "compile": {"bin/placeholder/WaferAligner.Infrastructure.Common.dll": {}}, "runtime": {"bin/placeholder/WaferAligner.Infrastructure.Common.dll": {}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WindowsForms"]}, "WaferAligner.Services.Logging/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v6.0", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Newtonsoft.Json": "13.0.3", "System.Diagnostics.DiagnosticSource": "9.0.6", "System.Reactive": "5.0.0", "WaferAligner.EventIds": "1.0.0"}, "compile": {"bin/placeholder/WaferAligner.Services.Logging.dll": {}}, "runtime": {"bin/placeholder/WaferAligner.Services.Logging.dll": {}}}}}, "libraries": {"Microsoft.Extensions.DependencyInjection/9.0.5": {"sha512": "N1Mn0T/tUBPoLL+Fzsp+VCEtneUhhxc1//Dx3BeuQ8AX+XrMlYCfnp2zgpEXnTCB7053CLdiqVWPZ7mEX6MPjg==", "type": "package", "path": "microsoft.extensions.dependencyinjection/9.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.xml", "lib/net9.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net9.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.xml", "microsoft.extensions.dependencyinjection.9.0.5.nupkg.sha512", "microsoft.extensions.dependencyinjection.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.6": {"sha512": "0Zn6nR/6g+90MxskZyOOMPQvnPnrrGu6bytPwkV+azDcTtCSuQ1+GJUrg8Klmnrjk1i6zMpw2lXijl+tw7Q3kA==", "type": "package", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "microsoft.extensions.dependencyinjection.abstractions.9.0.6.nupkg.sha512", "microsoft.extensions.dependencyinjection.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Abstractions/9.0.6": {"sha512": "LFnyBNK7WtFmKdnHu3v0HOYQ8BcjYuy0jdC9pgCJ/rbLKoJEG9/dBzSKMEeeWDbDeoWS0TIxOC8a9CM5ufca3A==", "type": "package", "path": "microsoft.extensions.logging.abstractions/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn3.11/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.0/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.4/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "buildTransitive/net461/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net462/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net8.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.targets", "lib/net462/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net462/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.xml", "microsoft.extensions.logging.abstractions.9.0.6.nupkg.sha512", "microsoft.extensions.logging.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.NETCore.Platforms/1.1.0": {"sha512": "kz0PEW2lhqygehI/d6XsPCQzD7ff7gUJaVGPVETX611eadGsA3A877GdSlU0LRVMCTH/+P3o2iDTak+S08V2+A==", "type": "package", "path": "microsoft.netcore.platforms/1.1.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/_._", "microsoft.netcore.platforms.1.1.0.nupkg.sha512", "microsoft.netcore.platforms.nuspec", "runtime.json"]}, "Microsoft.NETCore.Targets/1.1.0": {"sha512": "aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "type": "package", "path": "microsoft.netcore.targets/1.1.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/_._", "microsoft.netcore.targets.1.1.0.nupkg.sha512", "microsoft.netcore.targets.nuspec", "runtime.json"]}, "Newtonsoft.Json/13.0.3": {"sha512": "HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "type": "package", "path": "newtonsoft.json/13.0.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "README.md", "lib/net20/Newtonsoft.Json.dll", "lib/net20/Newtonsoft.Json.xml", "lib/net35/Newtonsoft.Json.dll", "lib/net35/Newtonsoft.Json.xml", "lib/net40/Newtonsoft.Json.dll", "lib/net40/Newtonsoft.Json.xml", "lib/net45/Newtonsoft.Json.dll", "lib/net45/Newtonsoft.Json.xml", "lib/net6.0/Newtonsoft.Json.dll", "lib/net6.0/Newtonsoft.Json.xml", "lib/netstandard1.0/Newtonsoft.Json.dll", "lib/netstandard1.0/Newtonsoft.Json.xml", "lib/netstandard1.3/Newtonsoft.Json.dll", "lib/netstandard1.3/Newtonsoft.Json.xml", "lib/netstandard2.0/Newtonsoft.Json.dll", "lib/netstandard2.0/Newtonsoft.Json.xml", "newtonsoft.json.13.0.3.nupkg.sha512", "newtonsoft.json.nuspec", "packageIcon.png"]}, "runtime.android-arm.runtime.native.System.IO.Ports/9.0.7": {"sha512": "xtOeUEMiUqYbYCbH6LuU7jH0KD0fAdMPjC2OmcXn9bTmZYiBazZBiVKQlekCmeXrBf39siRbx8TNnVI8U9RWVA==", "type": "package", "path": "runtime.android-arm.runtime.native.system.io.ports/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.android-arm.runtime.native.system.io.ports.9.0.7.nupkg.sha512", "runtime.android-arm.runtime.native.system.io.ports.nuspec", "runtimes/android-arm/native/libSystem.IO.Ports.Native.so", "runtimes/android-arm64/native/_._", "runtimes/android-x64/native/_._", "runtimes/android-x86/native/_._", "runtimes/linux-arm/native/_._", "runtimes/linux-arm64/native/_._", "runtimes/linux-bionic-arm64/native/_._", "runtimes/linux-bionic-x64/native/_._", "runtimes/linux-musl-arm/native/_._", "runtimes/linux-musl-arm64/native/_._", "runtimes/linux-musl-x64/native/_._", "runtimes/linux-x64/native/_._", "runtimes/maccatalyst-arm64/native/_._", "runtimes/maccatalyst-x64/native/_._", "runtimes/osx-arm64/native/_._", "runtimes/osx-x64/native/_._", "useSharedDesignerContext.txt"]}, "runtime.android-arm64.runtime.native.System.IO.Ports/9.0.7": {"sha512": "F3SHKwakrr9YenXhjk73bn5W8IQGXpBKTl3qmHUwdmkEKtc8J/30D6OA0/0M1oG3w/YvdgBiWIuy4fnId/98uQ==", "type": "package", "path": "runtime.android-arm64.runtime.native.system.io.ports/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.android-arm64.runtime.native.system.io.ports.9.0.7.nupkg.sha512", "runtime.android-arm64.runtime.native.system.io.ports.nuspec", "runtimes/android-arm/native/_._", "runtimes/android-arm64/native/libSystem.IO.Ports.Native.so", "runtimes/android-x64/native/_._", "runtimes/android-x86/native/_._", "runtimes/linux-arm/native/_._", "runtimes/linux-arm64/native/_._", "runtimes/linux-bionic-arm64/native/_._", "runtimes/linux-bionic-x64/native/_._", "runtimes/linux-musl-arm/native/_._", "runtimes/linux-musl-arm64/native/_._", "runtimes/linux-musl-x64/native/_._", "runtimes/linux-x64/native/_._", "runtimes/maccatalyst-arm64/native/_._", "runtimes/maccatalyst-x64/native/_._", "runtimes/osx-arm64/native/_._", "runtimes/osx-x64/native/_._", "useSharedDesignerContext.txt"]}, "runtime.android-x64.runtime.native.System.IO.Ports/9.0.7": {"sha512": "uRt8zM34o9NRn9PT8/IxPf4eR4wMNeSLoCTtixekALpwg6GKdGn2JxuWoyzRYK513vUgEBl9TXVF0lPEgJnASg==", "type": "package", "path": "runtime.android-x64.runtime.native.system.io.ports/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.android-x64.runtime.native.system.io.ports.9.0.7.nupkg.sha512", "runtime.android-x64.runtime.native.system.io.ports.nuspec", "runtimes/android-arm/native/_._", "runtimes/android-arm64/native/_._", "runtimes/android-x64/native/libSystem.IO.Ports.Native.so", "runtimes/android-x86/native/_._", "runtimes/linux-arm/native/_._", "runtimes/linux-arm64/native/_._", "runtimes/linux-bionic-arm64/native/_._", "runtimes/linux-bionic-x64/native/_._", "runtimes/linux-musl-arm/native/_._", "runtimes/linux-musl-arm64/native/_._", "runtimes/linux-musl-x64/native/_._", "runtimes/linux-x64/native/_._", "runtimes/maccatalyst-arm64/native/_._", "runtimes/maccatalyst-x64/native/_._", "runtimes/osx-arm64/native/_._", "runtimes/osx-x64/native/_._", "useSharedDesignerContext.txt"]}, "runtime.android-x86.runtime.native.System.IO.Ports/9.0.7": {"sha512": "az6Dxw9DZ2I4ypY0cgML6XXKvi3yK1Tevlsv4AdeV0qaD/7hpepuJr2XpPleWF744QHlVIhMlLX1fMcQM0Zr4w==", "type": "package", "path": "runtime.android-x86.runtime.native.system.io.ports/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.android-x86.runtime.native.system.io.ports.9.0.7.nupkg.sha512", "runtime.android-x86.runtime.native.system.io.ports.nuspec", "runtimes/android-arm/native/_._", "runtimes/android-arm64/native/_._", "runtimes/android-x64/native/_._", "runtimes/android-x86/native/libSystem.IO.Ports.Native.so", "runtimes/linux-arm/native/_._", "runtimes/linux-arm64/native/_._", "runtimes/linux-bionic-arm64/native/_._", "runtimes/linux-bionic-x64/native/_._", "runtimes/linux-musl-arm/native/_._", "runtimes/linux-musl-arm64/native/_._", "runtimes/linux-musl-x64/native/_._", "runtimes/linux-x64/native/_._", "runtimes/maccatalyst-arm64/native/_._", "runtimes/maccatalyst-x64/native/_._", "runtimes/osx-arm64/native/_._", "runtimes/osx-x64/native/_._", "useSharedDesignerContext.txt"]}, "runtime.linux-arm.runtime.native.System.IO.Ports/9.0.7": {"sha512": "uGkizTYesvArtyzD50BlIY4dlbs36OT0KNsPuwG9wEQvpz4WGiKbUD494Y6nfGJoIFkW0hdqOBljqE234tk26A==", "type": "package", "path": "runtime.linux-arm.runtime.native.system.io.ports/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.linux-arm.runtime.native.system.io.ports.9.0.7.nupkg.sha512", "runtime.linux-arm.runtime.native.system.io.ports.nuspec", "runtimes/android-arm/native/_._", "runtimes/android-arm64/native/_._", "runtimes/android-x64/native/_._", "runtimes/android-x86/native/_._", "runtimes/linux-arm/native/libSystem.IO.Ports.Native.so", "runtimes/linux-arm64/native/_._", "runtimes/linux-bionic-arm64/native/_._", "runtimes/linux-bionic-x64/native/_._", "runtimes/linux-musl-arm/native/_._", "runtimes/linux-musl-arm64/native/_._", "runtimes/linux-musl-x64/native/_._", "runtimes/linux-x64/native/_._", "runtimes/maccatalyst-arm64/native/_._", "runtimes/maccatalyst-x64/native/_._", "runtimes/osx-arm64/native/_._", "runtimes/osx-x64/native/_._", "useSharedDesignerContext.txt"]}, "runtime.linux-arm64.runtime.native.System.IO.Ports/9.0.7": {"sha512": "HTyktEebT2q6Et9XSG5hTWqt69n7hI+0UBHBOPeSlMZ6cMSR/MW5bO1yFqaR/+U6nM08zRCpaBOGOw2GPTq1eg==", "type": "package", "path": "runtime.linux-arm64.runtime.native.system.io.ports/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.linux-arm64.runtime.native.system.io.ports.9.0.7.nupkg.sha512", "runtime.linux-arm64.runtime.native.system.io.ports.nuspec", "runtimes/android-arm/native/_._", "runtimes/android-arm64/native/_._", "runtimes/android-x64/native/_._", "runtimes/android-x86/native/_._", "runtimes/linux-arm/native/_._", "runtimes/linux-arm64/native/libSystem.IO.Ports.Native.so", "runtimes/linux-bionic-arm64/native/_._", "runtimes/linux-bionic-x64/native/_._", "runtimes/linux-musl-arm/native/_._", "runtimes/linux-musl-arm64/native/_._", "runtimes/linux-musl-x64/native/_._", "runtimes/linux-x64/native/_._", "runtimes/maccatalyst-arm64/native/_._", "runtimes/maccatalyst-x64/native/_._", "runtimes/osx-arm64/native/_._", "runtimes/osx-x64/native/_._", "useSharedDesignerContext.txt"]}, "runtime.linux-bionic-arm64.runtime.native.System.IO.Ports/9.0.7": {"sha512": "N/aKWbFSvESYOqj5IYfzN9GcRXlTsaeJ899XCwiLGoMZIfLZTF1ejaR6Ikq3VnGdv4vilTSlvsDcxvskXwfKWA==", "type": "package", "path": "runtime.linux-bionic-arm64.runtime.native.system.io.ports/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.linux-bionic-arm64.runtime.native.system.io.ports.9.0.7.nupkg.sha512", "runtime.linux-bionic-arm64.runtime.native.system.io.ports.nuspec", "runtimes/android-arm/native/_._", "runtimes/android-arm64/native/_._", "runtimes/android-x64/native/_._", "runtimes/android-x86/native/_._", "runtimes/linux-arm/native/_._", "runtimes/linux-arm64/native/_._", "runtimes/linux-bionic-arm64/native/libSystem.IO.Ports.Native.so", "runtimes/linux-bionic-x64/native/_._", "runtimes/linux-musl-arm/native/_._", "runtimes/linux-musl-arm64/native/_._", "runtimes/linux-musl-x64/native/_._", "runtimes/linux-x64/native/_._", "runtimes/maccatalyst-arm64/native/_._", "runtimes/maccatalyst-x64/native/_._", "runtimes/osx-arm64/native/_._", "runtimes/osx-x64/native/_._", "useSharedDesignerContext.txt"]}, "runtime.linux-bionic-x64.runtime.native.System.IO.Ports/9.0.7": {"sha512": "OyEZXmsoXPGu/kC/JFlVVZiVdVQmFxYwVJPR8CjX3SZTru2cTfDmX7v1d+kbuSrgUjrvLjk77PwbFL6Iz4KkPA==", "type": "package", "path": "runtime.linux-bionic-x64.runtime.native.system.io.ports/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.linux-bionic-x64.runtime.native.system.io.ports.9.0.7.nupkg.sha512", "runtime.linux-bionic-x64.runtime.native.system.io.ports.nuspec", "runtimes/android-arm/native/_._", "runtimes/android-arm64/native/_._", "runtimes/android-x64/native/_._", "runtimes/android-x86/native/_._", "runtimes/linux-arm/native/_._", "runtimes/linux-arm64/native/_._", "runtimes/linux-bionic-arm64/native/_._", "runtimes/linux-bionic-x64/native/libSystem.IO.Ports.Native.so", "runtimes/linux-musl-arm/native/_._", "runtimes/linux-musl-arm64/native/_._", "runtimes/linux-musl-x64/native/_._", "runtimes/linux-x64/native/_._", "runtimes/maccatalyst-arm64/native/_._", "runtimes/maccatalyst-x64/native/_._", "runtimes/osx-arm64/native/_._", "runtimes/osx-x64/native/_._", "useSharedDesignerContext.txt"]}, "runtime.linux-musl-arm.runtime.native.System.IO.Ports/9.0.7": {"sha512": "AC42my/QpwBBxRGKVqed1k0Kn1iZ5ZGNN/R6VLByKDHaDlUO7W6VUimle3JN9imC5R8aq9or6VpqFu9mCss0Ew==", "type": "package", "path": "runtime.linux-musl-arm.runtime.native.system.io.ports/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.linux-musl-arm.runtime.native.system.io.ports.9.0.7.nupkg.sha512", "runtime.linux-musl-arm.runtime.native.system.io.ports.nuspec", "runtimes/android-arm/native/_._", "runtimes/android-arm64/native/_._", "runtimes/android-x64/native/_._", "runtimes/android-x86/native/_._", "runtimes/linux-arm/native/_._", "runtimes/linux-arm64/native/_._", "runtimes/linux-bionic-arm64/native/_._", "runtimes/linux-bionic-x64/native/_._", "runtimes/linux-musl-arm/native/libSystem.IO.Ports.Native.so", "runtimes/linux-musl-arm64/native/_._", "runtimes/linux-musl-x64/native/_._", "runtimes/linux-x64/native/_._", "runtimes/maccatalyst-arm64/native/_._", "runtimes/maccatalyst-x64/native/_._", "runtimes/osx-arm64/native/_._", "runtimes/osx-x64/native/_._", "useSharedDesignerContext.txt"]}, "runtime.linux-musl-arm64.runtime.native.System.IO.Ports/9.0.7": {"sha512": "xQhCGVeC555KtzeyQnPzWbifovO3mGLiGGUZppluvqAGvygAhiXqrigSMgu0xTowE2u5WwFCIj4Trco2DU9uxg==", "type": "package", "path": "runtime.linux-musl-arm64.runtime.native.system.io.ports/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.linux-musl-arm64.runtime.native.system.io.ports.9.0.7.nupkg.sha512", "runtime.linux-musl-arm64.runtime.native.system.io.ports.nuspec", "runtimes/android-arm/native/_._", "runtimes/android-arm64/native/_._", "runtimes/android-x64/native/_._", "runtimes/android-x86/native/_._", "runtimes/linux-arm/native/_._", "runtimes/linux-arm64/native/_._", "runtimes/linux-bionic-arm64/native/_._", "runtimes/linux-bionic-x64/native/_._", "runtimes/linux-musl-arm/native/_._", "runtimes/linux-musl-arm64/native/libSystem.IO.Ports.Native.so", "runtimes/linux-musl-x64/native/_._", "runtimes/linux-x64/native/_._", "runtimes/maccatalyst-arm64/native/_._", "runtimes/maccatalyst-x64/native/_._", "runtimes/osx-arm64/native/_._", "runtimes/osx-x64/native/_._", "useSharedDesignerContext.txt"]}, "runtime.linux-musl-x64.runtime.native.System.IO.Ports/9.0.7": {"sha512": "NQElKMyNNzssg+JFQ1LjPuRng5uoVXbt2aftpUaztDxyBhCBRm/9mZjbWvXS5I5E9dhfNUdFWU40dxpo5bITMw==", "type": "package", "path": "runtime.linux-musl-x64.runtime.native.system.io.ports/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.linux-musl-x64.runtime.native.system.io.ports.9.0.7.nupkg.sha512", "runtime.linux-musl-x64.runtime.native.system.io.ports.nuspec", "runtimes/android-arm/native/_._", "runtimes/android-arm64/native/_._", "runtimes/android-x64/native/_._", "runtimes/android-x86/native/_._", "runtimes/linux-arm/native/_._", "runtimes/linux-arm64/native/_._", "runtimes/linux-bionic-arm64/native/_._", "runtimes/linux-bionic-x64/native/_._", "runtimes/linux-musl-arm/native/_._", "runtimes/linux-musl-arm64/native/_._", "runtimes/linux-musl-x64/native/libSystem.IO.Ports.Native.so", "runtimes/linux-x64/native/_._", "runtimes/maccatalyst-arm64/native/_._", "runtimes/maccatalyst-x64/native/_._", "runtimes/osx-arm64/native/_._", "runtimes/osx-x64/native/_._", "useSharedDesignerContext.txt"]}, "runtime.linux-x64.runtime.native.System.IO.Ports/9.0.7": {"sha512": "wO5758mD+mKBI2V8U/FdfbmcSEW7lLaRohozF72i8x4Ly4o2tmFzYTcjocfJJpTxS8DnK6X9qxP9pQakTwZk7w==", "type": "package", "path": "runtime.linux-x64.runtime.native.system.io.ports/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.linux-x64.runtime.native.system.io.ports.9.0.7.nupkg.sha512", "runtime.linux-x64.runtime.native.system.io.ports.nuspec", "runtimes/android-arm/native/_._", "runtimes/android-arm64/native/_._", "runtimes/android-x64/native/_._", "runtimes/android-x86/native/_._", "runtimes/linux-arm/native/_._", "runtimes/linux-arm64/native/_._", "runtimes/linux-bionic-arm64/native/_._", "runtimes/linux-bionic-x64/native/_._", "runtimes/linux-musl-arm/native/_._", "runtimes/linux-musl-arm64/native/_._", "runtimes/linux-musl-x64/native/_._", "runtimes/linux-x64/native/libSystem.IO.Ports.Native.so", "runtimes/maccatalyst-arm64/native/_._", "runtimes/maccatalyst-x64/native/_._", "runtimes/osx-arm64/native/_._", "runtimes/osx-x64/native/_._", "useSharedDesignerContext.txt"]}, "runtime.maccatalyst-arm64.runtime.native.System.IO.Ports/9.0.7": {"sha512": "8tpVCB289sH5/w/lrztQaVWi6s6V0Ptrpmm6YJR670YswgUPauxeEbN4wiUp7lxC00bTTJq6tRfBntQ1ryyALw==", "type": "package", "path": "runtime.maccatalyst-arm64.runtime.native.system.io.ports/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.maccatalyst-arm64.runtime.native.system.io.ports.9.0.7.nupkg.sha512", "runtime.maccatalyst-arm64.runtime.native.system.io.ports.nuspec", "runtimes/android-arm/native/_._", "runtimes/android-arm64/native/_._", "runtimes/android-x64/native/_._", "runtimes/android-x86/native/_._", "runtimes/linux-arm/native/_._", "runtimes/linux-arm64/native/_._", "runtimes/linux-bionic-arm64/native/_._", "runtimes/linux-bionic-x64/native/_._", "runtimes/linux-musl-arm/native/_._", "runtimes/linux-musl-arm64/native/_._", "runtimes/linux-musl-x64/native/_._", "runtimes/linux-x64/native/_._", "runtimes/maccatalyst-arm64/native/libSystem.IO.Ports.Native.dylib", "runtimes/maccatalyst-x64/native/_._", "runtimes/osx-arm64/native/_._", "runtimes/osx-x64/native/_._", "useSharedDesignerContext.txt"]}, "runtime.maccatalyst-x64.runtime.native.System.IO.Ports/9.0.7": {"sha512": "qfdGPrRoZcKLSXNO1vGIWJ0ovTh23Zy2cf0MfnNpQK58Dp27eALuVk0p5kpB4X/bf7I9KO+XorezUSFQZ6MHdw==", "type": "package", "path": "runtime.maccatalyst-x64.runtime.native.system.io.ports/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.maccatalyst-x64.runtime.native.system.io.ports.9.0.7.nupkg.sha512", "runtime.maccatalyst-x64.runtime.native.system.io.ports.nuspec", "runtimes/android-arm/native/_._", "runtimes/android-arm64/native/_._", "runtimes/android-x64/native/_._", "runtimes/android-x86/native/_._", "runtimes/linux-arm/native/_._", "runtimes/linux-arm64/native/_._", "runtimes/linux-bionic-arm64/native/_._", "runtimes/linux-bionic-x64/native/_._", "runtimes/linux-musl-arm/native/_._", "runtimes/linux-musl-arm64/native/_._", "runtimes/linux-musl-x64/native/_._", "runtimes/linux-x64/native/_._", "runtimes/maccatalyst-arm64/native/_._", "runtimes/maccatalyst-x64/native/libSystem.IO.Ports.Native.dylib", "runtimes/osx-arm64/native/_._", "runtimes/osx-x64/native/_._", "useSharedDesignerContext.txt"]}, "runtime.native.System.IO.Ports/9.0.7": {"sha512": "4IJb6UpOGat7IqKl2QAahMYMc1rMWRf9BWeiNDZBsyNViFOtSftgSjUKzFLtnSlXrbc7qqPCh1M9wOE+WNgcOQ==", "type": "package", "path": "runtime.native.system.io.ports/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.native.system.io.ports.9.0.7.nupkg.sha512", "runtime.native.system.io.ports.nuspec", "useSharedDesignerContext.txt"]}, "runtime.osx-arm64.runtime.native.System.IO.Ports/9.0.7": {"sha512": "pOZCQaB07EYIWxT5zMgoQr0QLbokVWmItEIOoCqbhY7m8ozV21Jxy5u6ZEL8gMO7QWD+zGbRZQYEQpCNAd+J4g==", "type": "package", "path": "runtime.osx-arm64.runtime.native.system.io.ports/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.osx-arm64.runtime.native.system.io.ports.9.0.7.nupkg.sha512", "runtime.osx-arm64.runtime.native.system.io.ports.nuspec", "runtimes/android-arm/native/_._", "runtimes/android-arm64/native/_._", "runtimes/android-x64/native/_._", "runtimes/android-x86/native/_._", "runtimes/linux-arm/native/_._", "runtimes/linux-arm64/native/_._", "runtimes/linux-bionic-arm64/native/_._", "runtimes/linux-bionic-x64/native/_._", "runtimes/linux-musl-arm/native/_._", "runtimes/linux-musl-arm64/native/_._", "runtimes/linux-musl-x64/native/_._", "runtimes/linux-x64/native/_._", "runtimes/maccatalyst-arm64/native/_._", "runtimes/maccatalyst-x64/native/_._", "runtimes/osx-arm64/native/libSystem.IO.Ports.Native.dylib", "runtimes/osx-x64/native/_._", "useSharedDesignerContext.txt"]}, "runtime.osx-x64.runtime.native.System.IO.Ports/9.0.7": {"sha512": "aZT8Go/6NcnKce+xwMbrAxNc9rA6K3SKXPiK5A+A/wiUrlZK6CQwYueKDmU3zdm/oSrUfHSzk0YuCBknRRp5wA==", "type": "package", "path": "runtime.osx-x64.runtime.native.system.io.ports/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.osx-x64.runtime.native.system.io.ports.9.0.7.nupkg.sha512", "runtime.osx-x64.runtime.native.system.io.ports.nuspec", "runtimes/android-arm/native/_._", "runtimes/android-arm64/native/_._", "runtimes/android-x64/native/_._", "runtimes/android-x86/native/_._", "runtimes/linux-arm/native/_._", "runtimes/linux-arm64/native/_._", "runtimes/linux-bionic-arm64/native/_._", "runtimes/linux-bionic-x64/native/_._", "runtimes/linux-musl-arm/native/_._", "runtimes/linux-musl-arm64/native/_._", "runtimes/linux-musl-x64/native/_._", "runtimes/linux-x64/native/_._", "runtimes/maccatalyst-arm64/native/_._", "runtimes/maccatalyst-x64/native/_._", "runtimes/osx-arm64/native/_._", "runtimes/osx-x64/native/libSystem.IO.Ports.Native.dylib", "useSharedDesignerContext.txt"]}, "System.Buffers/4.5.1": {"sha512": "Rw7ijyl1qqRS0YQD/WycNst8hUUMgrMH4FCn1nNm27M4VxchZ1js3fVjQaANHO5f3sN4isvP4a+Met9Y4YomAg==", "type": "package", "path": "system.buffers/4.5.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Buffers.dll", "lib/net461/System.Buffers.xml", "lib/netcoreapp2.0/_._", "lib/netstandard1.1/System.Buffers.dll", "lib/netstandard1.1/System.Buffers.xml", "lib/netstandard2.0/System.Buffers.dll", "lib/netstandard2.0/System.Buffers.xml", "lib/uap10.0.16299/_._", "ref/net45/System.Buffers.dll", "ref/net45/System.Buffers.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.1/System.Buffers.dll", "ref/netstandard1.1/System.Buffers.xml", "ref/netstandard2.0/System.Buffers.dll", "ref/netstandard2.0/System.Buffers.xml", "ref/uap10.0.16299/_._", "system.buffers.4.5.1.nupkg.sha512", "system.buffers.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Diagnostics.DiagnosticSource/9.0.6": {"sha512": "nikkwAKqpwWUvV5J8S9fnOPYg8k75Lf9fAI4bd6pyhyqNma0Py9kt+zcqXbe4TjJ4sTPcdYpPg81shYTrXnUZQ==", "type": "package", "path": "system.diagnostics.diagnosticsource/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Diagnostics.DiagnosticSource.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Diagnostics.DiagnosticSource.targets", "lib/net462/System.Diagnostics.DiagnosticSource.dll", "lib/net462/System.Diagnostics.DiagnosticSource.xml", "lib/net8.0/System.Diagnostics.DiagnosticSource.dll", "lib/net8.0/System.Diagnostics.DiagnosticSource.xml", "lib/net9.0/System.Diagnostics.DiagnosticSource.dll", "lib/net9.0/System.Diagnostics.DiagnosticSource.xml", "lib/netstandard2.0/System.Diagnostics.DiagnosticSource.dll", "lib/netstandard2.0/System.Diagnostics.DiagnosticSource.xml", "system.diagnostics.diagnosticsource.9.0.6.nupkg.sha512", "system.diagnostics.diagnosticsource.nuspec", "useSharedDesignerContext.txt"]}, "System.IO/4.3.0": {"sha512": "3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "type": "package", "path": "system.io/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.IO.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.IO.dll", "ref/netcore50/System.IO.dll", "ref/netcore50/System.IO.xml", "ref/netcore50/de/System.IO.xml", "ref/netcore50/es/System.IO.xml", "ref/netcore50/fr/System.IO.xml", "ref/netcore50/it/System.IO.xml", "ref/netcore50/ja/System.IO.xml", "ref/netcore50/ko/System.IO.xml", "ref/netcore50/ru/System.IO.xml", "ref/netcore50/zh-hans/System.IO.xml", "ref/netcore50/zh-hant/System.IO.xml", "ref/netstandard1.0/System.IO.dll", "ref/netstandard1.0/System.IO.xml", "ref/netstandard1.0/de/System.IO.xml", "ref/netstandard1.0/es/System.IO.xml", "ref/netstandard1.0/fr/System.IO.xml", "ref/netstandard1.0/it/System.IO.xml", "ref/netstandard1.0/ja/System.IO.xml", "ref/netstandard1.0/ko/System.IO.xml", "ref/netstandard1.0/ru/System.IO.xml", "ref/netstandard1.0/zh-hans/System.IO.xml", "ref/netstandard1.0/zh-hant/System.IO.xml", "ref/netstandard1.3/System.IO.dll", "ref/netstandard1.3/System.IO.xml", "ref/netstandard1.3/de/System.IO.xml", "ref/netstandard1.3/es/System.IO.xml", "ref/netstandard1.3/fr/System.IO.xml", "ref/netstandard1.3/it/System.IO.xml", "ref/netstandard1.3/ja/System.IO.xml", "ref/netstandard1.3/ko/System.IO.xml", "ref/netstandard1.3/ru/System.IO.xml", "ref/netstandard1.3/zh-hans/System.IO.xml", "ref/netstandard1.3/zh-hant/System.IO.xml", "ref/netstandard1.5/System.IO.dll", "ref/netstandard1.5/System.IO.xml", "ref/netstandard1.5/de/System.IO.xml", "ref/netstandard1.5/es/System.IO.xml", "ref/netstandard1.5/fr/System.IO.xml", "ref/netstandard1.5/it/System.IO.xml", "ref/netstandard1.5/ja/System.IO.xml", "ref/netstandard1.5/ko/System.IO.xml", "ref/netstandard1.5/ru/System.IO.xml", "ref/netstandard1.5/zh-hans/System.IO.xml", "ref/netstandard1.5/zh-hant/System.IO.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.io.4.3.0.nupkg.sha512", "system.io.nuspec"]}, "System.IO.Ports/9.0.7": {"sha512": "IqnvqsPwKmMBffVpm0PtyxHDQ13b7iN7V4BL/uoQiRhsYnBMoWnX5lpEQNJFzP9SsyqqnkWbsM+z/MkQ9EhUxA==", "type": "package", "path": "system.io.ports/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.IO.Ports.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.IO.Ports.targets", "lib/net462/System.IO.Ports.dll", "lib/net462/System.IO.Ports.xml", "lib/net8.0/System.IO.Ports.dll", "lib/net8.0/System.IO.Ports.xml", "lib/net9.0/System.IO.Ports.dll", "lib/net9.0/System.IO.Ports.xml", "lib/netstandard2.0/System.IO.Ports.dll", "lib/netstandard2.0/System.IO.Ports.xml", "runtimes/unix/lib/net8.0/System.IO.Ports.dll", "runtimes/unix/lib/net8.0/System.IO.Ports.xml", "runtimes/unix/lib/net9.0/System.IO.Ports.dll", "runtimes/unix/lib/net9.0/System.IO.Ports.xml", "runtimes/win/lib/net8.0/System.IO.Ports.dll", "runtimes/win/lib/net8.0/System.IO.Ports.xml", "runtimes/win/lib/net9.0/System.IO.Ports.dll", "runtimes/win/lib/net9.0/System.IO.Ports.xml", "system.io.ports.9.0.7.nupkg.sha512", "system.io.ports.nuspec", "useSharedDesignerContext.txt"]}, "System.Memory/4.5.5": {"sha512": "XIWiDvKPXaTveaB7HVganDlOCRoj03l+jrwNvcge/t8vhGYKvqV+dMv6G4SAX2NoNmN0wZfVPTAlFwZcZvVOUw==", "type": "package", "path": "system.memory/4.5.5", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Memory.dll", "lib/net461/System.Memory.xml", "lib/netcoreapp2.1/_._", "lib/netstandard1.1/System.Memory.dll", "lib/netstandard1.1/System.Memory.xml", "lib/netstandard2.0/System.Memory.dll", "lib/netstandard2.0/System.Memory.xml", "ref/netcoreapp2.1/_._", "system.memory.4.5.5.nupkg.sha512", "system.memory.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Reactive/5.0.0": {"sha512": "erBZjkQHWL9jpasCE/0qKAryzVBJFxGHVBAvgRN1bzM0q2s1S4oYREEEL0Vb+1kA/6BKb5FjUZMp5VXmy+gzkQ==", "type": "package", "path": "system.reactive/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "build/net5.0/_._", "build/netcoreapp3.1/System.Reactive.dll", "build/netcoreapp3.1/System.Reactive.targets", "build/netcoreapp3.1/System.Reactive.xml", "buildTransitive/net5.0/_._", "buildTransitive/netcoreapp3.1/System.Reactive.targets", "lib/net472/System.Reactive.dll", "lib/net472/System.Reactive.xml", "lib/net5.0-windows10.0.19041/System.Reactive.dll", "lib/net5.0-windows10.0.19041/System.Reactive.xml", "lib/net5.0/System.Reactive.dll", "lib/net5.0/System.Reactive.xml", "lib/netcoreapp3.1/_._", "lib/netstandard2.0/System.Reactive.dll", "lib/netstandard2.0/System.Reactive.xml", "lib/uap10.0.16299/System.Reactive.dll", "lib/uap10.0.16299/System.Reactive.pri", "lib/uap10.0.16299/System.Reactive.xml", "system.reactive.5.0.0.nupkg.sha512", "system.reactive.nuspec"]}, "System.Reflection/4.3.0": {"sha512": "KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "type": "package", "path": "system.reflection/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Reflection.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Reflection.dll", "ref/netcore50/System.Reflection.dll", "ref/netcore50/System.Reflection.xml", "ref/netcore50/de/System.Reflection.xml", "ref/netcore50/es/System.Reflection.xml", "ref/netcore50/fr/System.Reflection.xml", "ref/netcore50/it/System.Reflection.xml", "ref/netcore50/ja/System.Reflection.xml", "ref/netcore50/ko/System.Reflection.xml", "ref/netcore50/ru/System.Reflection.xml", "ref/netcore50/zh-hans/System.Reflection.xml", "ref/netcore50/zh-hant/System.Reflection.xml", "ref/netstandard1.0/System.Reflection.dll", "ref/netstandard1.0/System.Reflection.xml", "ref/netstandard1.0/de/System.Reflection.xml", "ref/netstandard1.0/es/System.Reflection.xml", "ref/netstandard1.0/fr/System.Reflection.xml", "ref/netstandard1.0/it/System.Reflection.xml", "ref/netstandard1.0/ja/System.Reflection.xml", "ref/netstandard1.0/ko/System.Reflection.xml", "ref/netstandard1.0/ru/System.Reflection.xml", "ref/netstandard1.0/zh-hans/System.Reflection.xml", "ref/netstandard1.0/zh-hant/System.Reflection.xml", "ref/netstandard1.3/System.Reflection.dll", "ref/netstandard1.3/System.Reflection.xml", "ref/netstandard1.3/de/System.Reflection.xml", "ref/netstandard1.3/es/System.Reflection.xml", "ref/netstandard1.3/fr/System.Reflection.xml", "ref/netstandard1.3/it/System.Reflection.xml", "ref/netstandard1.3/ja/System.Reflection.xml", "ref/netstandard1.3/ko/System.Reflection.xml", "ref/netstandard1.3/ru/System.Reflection.xml", "ref/netstandard1.3/zh-hans/System.Reflection.xml", "ref/netstandard1.3/zh-hant/System.Reflection.xml", "ref/netstandard1.5/System.Reflection.dll", "ref/netstandard1.5/System.Reflection.xml", "ref/netstandard1.5/de/System.Reflection.xml", "ref/netstandard1.5/es/System.Reflection.xml", "ref/netstandard1.5/fr/System.Reflection.xml", "ref/netstandard1.5/it/System.Reflection.xml", "ref/netstandard1.5/ja/System.Reflection.xml", "ref/netstandard1.5/ko/System.Reflection.xml", "ref/netstandard1.5/ru/System.Reflection.xml", "ref/netstandard1.5/zh-hans/System.Reflection.xml", "ref/netstandard1.5/zh-hant/System.Reflection.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.reflection.4.3.0.nupkg.sha512", "system.reflection.nuspec"]}, "System.Reflection.Primitives/4.3.0": {"sha512": "5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "type": "package", "path": "system.reflection.primitives/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Reflection.Primitives.dll", "ref/netcore50/System.Reflection.Primitives.xml", "ref/netcore50/de/System.Reflection.Primitives.xml", "ref/netcore50/es/System.Reflection.Primitives.xml", "ref/netcore50/fr/System.Reflection.Primitives.xml", "ref/netcore50/it/System.Reflection.Primitives.xml", "ref/netcore50/ja/System.Reflection.Primitives.xml", "ref/netcore50/ko/System.Reflection.Primitives.xml", "ref/netcore50/ru/System.Reflection.Primitives.xml", "ref/netcore50/zh-hans/System.Reflection.Primitives.xml", "ref/netcore50/zh-hant/System.Reflection.Primitives.xml", "ref/netstandard1.0/System.Reflection.Primitives.dll", "ref/netstandard1.0/System.Reflection.Primitives.xml", "ref/netstandard1.0/de/System.Reflection.Primitives.xml", "ref/netstandard1.0/es/System.Reflection.Primitives.xml", "ref/netstandard1.0/fr/System.Reflection.Primitives.xml", "ref/netstandard1.0/it/System.Reflection.Primitives.xml", "ref/netstandard1.0/ja/System.Reflection.Primitives.xml", "ref/netstandard1.0/ko/System.Reflection.Primitives.xml", "ref/netstandard1.0/ru/System.Reflection.Primitives.xml", "ref/netstandard1.0/zh-hans/System.Reflection.Primitives.xml", "ref/netstandard1.0/zh-hant/System.Reflection.Primitives.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.reflection.primitives.4.3.0.nupkg.sha512", "system.reflection.primitives.nuspec"]}, "System.Runtime/4.3.0": {"sha512": "JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "type": "package", "path": "system.runtime/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Runtime.dll", "lib/portable-net45+win8+wp80+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Runtime.dll", "ref/netcore50/System.Runtime.dll", "ref/netcore50/System.Runtime.xml", "ref/netcore50/de/System.Runtime.xml", "ref/netcore50/es/System.Runtime.xml", "ref/netcore50/fr/System.Runtime.xml", "ref/netcore50/it/System.Runtime.xml", "ref/netcore50/ja/System.Runtime.xml", "ref/netcore50/ko/System.Runtime.xml", "ref/netcore50/ru/System.Runtime.xml", "ref/netcore50/zh-hans/System.Runtime.xml", "ref/netcore50/zh-hant/System.Runtime.xml", "ref/netstandard1.0/System.Runtime.dll", "ref/netstandard1.0/System.Runtime.xml", "ref/netstandard1.0/de/System.Runtime.xml", "ref/netstandard1.0/es/System.Runtime.xml", "ref/netstandard1.0/fr/System.Runtime.xml", "ref/netstandard1.0/it/System.Runtime.xml", "ref/netstandard1.0/ja/System.Runtime.xml", "ref/netstandard1.0/ko/System.Runtime.xml", "ref/netstandard1.0/ru/System.Runtime.xml", "ref/netstandard1.0/zh-hans/System.Runtime.xml", "ref/netstandard1.0/zh-hant/System.Runtime.xml", "ref/netstandard1.2/System.Runtime.dll", "ref/netstandard1.2/System.Runtime.xml", "ref/netstandard1.2/de/System.Runtime.xml", "ref/netstandard1.2/es/System.Runtime.xml", "ref/netstandard1.2/fr/System.Runtime.xml", "ref/netstandard1.2/it/System.Runtime.xml", "ref/netstandard1.2/ja/System.Runtime.xml", "ref/netstandard1.2/ko/System.Runtime.xml", "ref/netstandard1.2/ru/System.Runtime.xml", "ref/netstandard1.2/zh-hans/System.Runtime.xml", "ref/netstandard1.2/zh-hant/System.Runtime.xml", "ref/netstandard1.3/System.Runtime.dll", "ref/netstandard1.3/System.Runtime.xml", "ref/netstandard1.3/de/System.Runtime.xml", "ref/netstandard1.3/es/System.Runtime.xml", "ref/netstandard1.3/fr/System.Runtime.xml", "ref/netstandard1.3/it/System.Runtime.xml", "ref/netstandard1.3/ja/System.Runtime.xml", "ref/netstandard1.3/ko/System.Runtime.xml", "ref/netstandard1.3/ru/System.Runtime.xml", "ref/netstandard1.3/zh-hans/System.Runtime.xml", "ref/netstandard1.3/zh-hant/System.Runtime.xml", "ref/netstandard1.5/System.Runtime.dll", "ref/netstandard1.5/System.Runtime.xml", "ref/netstandard1.5/de/System.Runtime.xml", "ref/netstandard1.5/es/System.Runtime.xml", "ref/netstandard1.5/fr/System.Runtime.xml", "ref/netstandard1.5/it/System.Runtime.xml", "ref/netstandard1.5/ja/System.Runtime.xml", "ref/netstandard1.5/ko/System.Runtime.xml", "ref/netstandard1.5/ru/System.Runtime.xml", "ref/netstandard1.5/zh-hans/System.Runtime.xml", "ref/netstandard1.5/zh-hant/System.Runtime.xml", "ref/portable-net45+win8+wp80+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.runtime.4.3.0.nupkg.sha512", "system.runtime.nuspec"]}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"sha512": "/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "type": "package", "path": "system.runtime.compilerservices.unsafe/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Runtime.CompilerServices.Unsafe.dll", "lib/net461/System.Runtime.CompilerServices.Unsafe.xml", "lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/net6.0/System.Runtime.CompilerServices.Unsafe.xml", "lib/netcoreapp3.1/System.Runtime.CompilerServices.Unsafe.dll", "lib/netcoreapp3.1/System.Runtime.CompilerServices.Unsafe.xml", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml", "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512", "system.runtime.compilerservices.unsafe.nuspec", "useSharedDesignerContext.txt"]}, "System.Text.Encoding/4.3.0": {"sha512": "BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "type": "package", "path": "system.text.encoding/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Text.Encoding.dll", "ref/netcore50/System.Text.Encoding.xml", "ref/netcore50/de/System.Text.Encoding.xml", "ref/netcore50/es/System.Text.Encoding.xml", "ref/netcore50/fr/System.Text.Encoding.xml", "ref/netcore50/it/System.Text.Encoding.xml", "ref/netcore50/ja/System.Text.Encoding.xml", "ref/netcore50/ko/System.Text.Encoding.xml", "ref/netcore50/ru/System.Text.Encoding.xml", "ref/netcore50/zh-hans/System.Text.Encoding.xml", "ref/netcore50/zh-hant/System.Text.Encoding.xml", "ref/netstandard1.0/System.Text.Encoding.dll", "ref/netstandard1.0/System.Text.Encoding.xml", "ref/netstandard1.0/de/System.Text.Encoding.xml", "ref/netstandard1.0/es/System.Text.Encoding.xml", "ref/netstandard1.0/fr/System.Text.Encoding.xml", "ref/netstandard1.0/it/System.Text.Encoding.xml", "ref/netstandard1.0/ja/System.Text.Encoding.xml", "ref/netstandard1.0/ko/System.Text.Encoding.xml", "ref/netstandard1.0/ru/System.Text.Encoding.xml", "ref/netstandard1.0/zh-hans/System.Text.Encoding.xml", "ref/netstandard1.0/zh-hant/System.Text.Encoding.xml", "ref/netstandard1.3/System.Text.Encoding.dll", "ref/netstandard1.3/System.Text.Encoding.xml", "ref/netstandard1.3/de/System.Text.Encoding.xml", "ref/netstandard1.3/es/System.Text.Encoding.xml", "ref/netstandard1.3/fr/System.Text.Encoding.xml", "ref/netstandard1.3/it/System.Text.Encoding.xml", "ref/netstandard1.3/ja/System.Text.Encoding.xml", "ref/netstandard1.3/ko/System.Text.Encoding.xml", "ref/netstandard1.3/ru/System.Text.Encoding.xml", "ref/netstandard1.3/zh-hans/System.Text.Encoding.xml", "ref/netstandard1.3/zh-hant/System.Text.Encoding.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.text.encoding.4.3.0.nupkg.sha512", "system.text.encoding.nuspec"]}, "System.Threading.Tasks/4.3.0": {"sha512": "LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "type": "package", "path": "system.threading.tasks/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Threading.Tasks.dll", "ref/netcore50/System.Threading.Tasks.xml", "ref/netcore50/de/System.Threading.Tasks.xml", "ref/netcore50/es/System.Threading.Tasks.xml", "ref/netcore50/fr/System.Threading.Tasks.xml", "ref/netcore50/it/System.Threading.Tasks.xml", "ref/netcore50/ja/System.Threading.Tasks.xml", "ref/netcore50/ko/System.Threading.Tasks.xml", "ref/netcore50/ru/System.Threading.Tasks.xml", "ref/netcore50/zh-hans/System.Threading.Tasks.xml", "ref/netcore50/zh-hant/System.Threading.Tasks.xml", "ref/netstandard1.0/System.Threading.Tasks.dll", "ref/netstandard1.0/System.Threading.Tasks.xml", "ref/netstandard1.0/de/System.Threading.Tasks.xml", "ref/netstandard1.0/es/System.Threading.Tasks.xml", "ref/netstandard1.0/fr/System.Threading.Tasks.xml", "ref/netstandard1.0/it/System.Threading.Tasks.xml", "ref/netstandard1.0/ja/System.Threading.Tasks.xml", "ref/netstandard1.0/ko/System.Threading.Tasks.xml", "ref/netstandard1.0/ru/System.Threading.Tasks.xml", "ref/netstandard1.0/zh-hans/System.Threading.Tasks.xml", "ref/netstandard1.0/zh-hant/System.Threading.Tasks.xml", "ref/netstandard1.3/System.Threading.Tasks.dll", "ref/netstandard1.3/System.Threading.Tasks.xml", "ref/netstandard1.3/de/System.Threading.Tasks.xml", "ref/netstandard1.3/es/System.Threading.Tasks.xml", "ref/netstandard1.3/fr/System.Threading.Tasks.xml", "ref/netstandard1.3/it/System.Threading.Tasks.xml", "ref/netstandard1.3/ja/System.Threading.Tasks.xml", "ref/netstandard1.3/ko/System.Threading.Tasks.xml", "ref/netstandard1.3/ru/System.Threading.Tasks.xml", "ref/netstandard1.3/zh-hans/System.Threading.Tasks.xml", "ref/netstandard1.3/zh-hant/System.Threading.Tasks.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.threading.tasks.4.3.0.nupkg.sha512", "system.threading.tasks.nuspec"]}, "WaferAligner.EventIds/1.0.0": {"type": "project", "path": "../../Core/WaferAligner.EventIds/WaferAligner.EventIds.csproj", "msbuildProject": "../../Core/WaferAligner.EventIds/WaferAligner.EventIds.csproj"}, "WaferAligner.Infrastructure.Common/1.0.0": {"type": "project", "path": "../../Infrastructure/WaferAligner.Infrastructure.Common/WaferAligner.Infrastructure.Common.csproj", "msbuildProject": "../../Infrastructure/WaferAligner.Infrastructure.Common/WaferAligner.Infrastructure.Common.csproj"}, "WaferAligner.Services.Logging/1.0.0": {"type": "project", "path": "../../Services/WaferAligner.Services.Logging/WaferAligner.Services.Logging.csproj", "msbuildProject": "../../Services/WaferAligner.Services.Logging/WaferAligner.Services.Logging.csproj"}}, "projectFileDependencyGroups": {"net6.0-windows7.0": ["C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\Business\\WaferAligner.Core.Business\\WaferAligner.Core.Business.csproj", "Microsoft.Extensions.DependencyInjection >= 9.0.5", "System.IO.Ports >= 9.0.7", "WaferAligner.EventIds >= 1.0.0", "WaferAligner.Infrastructure.Common >= 1.0.0", "WaferAligner.Services.Logging >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\src\\Communication\\WaferAligner.Communication.Serial\\WaferAligner.Communication.Serial.csproj", "projectName": "WaferAligner.Communication.Serial", "projectPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\src\\Communication\\WaferAligner.Communication.Serial\\WaferAligner.Communication.Serial.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\src\\Communication\\WaferAligner.Communication.Serial\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "projectReferences": {"C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\Business\\WaferAligner.Core.Business\\WaferAligner.Core.Business.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\Business\\WaferAligner.Core.Business\\WaferAligner.Core.Business.csproj"}, "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\src\\Core\\WaferAligner.EventIds\\WaferAligner.EventIds.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\src\\Core\\WaferAligner.EventIds\\WaferAligner.EventIds.csproj"}, "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\src\\Infrastructure\\WaferAligner.Infrastructure.Common\\WaferAligner.Infrastructure.Common.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\src\\Infrastructure\\WaferAligner.Infrastructure.Common\\WaferAligner.Infrastructure.Common.csproj"}, "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\src\\Services\\WaferAligner.Services.Logging\\WaferAligner.Services.Logging.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\src\\Services\\WaferAligner.Services.Logging\\WaferAligner.Services.Logging.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "dependencies": {"Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[9.0.5, )"}, "System.IO.Ports": {"target": "Package", "version": "[9.0.7, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WindowsForms": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "logs": [{"code": "NU1104", "level": "Error", "message": "找不到项目“C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\Business\\WaferAligner.Core.Business\\WaferAligner.Core.Business.csproj”。请检查项目引用是否有效以及项目文件是否存在。", "libraryId": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\Business\\WaferAligner.Core.Business\\WaferAligner.Core.Business.csproj", "targetGraphs": ["net6.0-windows7.0"]}]}