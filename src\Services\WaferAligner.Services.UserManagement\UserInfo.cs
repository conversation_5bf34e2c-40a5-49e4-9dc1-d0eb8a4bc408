using System;
using System.ComponentModel.DataAnnotations;

namespace WaferAligner.Services.UserManagement
{
    public class UserInfo
    {
        public string Name;
        public UserAuth? Auth;

        // 扩展属性用于用户管理
        [Required]
        public string Username { get; set; } = string.Empty;

        [Required]
        public string PasswordHash { get; set; } = string.Empty;

        public string[] Roles { get; set; } = Array.Empty<string>();

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        public DateTime? LastLoginDate { get; set; }

        public bool IsActive { get; set; } = true;

        public string? Email { get; set; }

        public string? Description { get; set; }
    }
}
