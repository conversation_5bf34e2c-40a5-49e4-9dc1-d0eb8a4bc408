﻿// using AlignerUI; // 已迁移到WaferAligner.Models
using WaferAligner.Services.Abstractions;
using Microsoft.Extensions.Logging;
using WaferAligner.Communication.Inovance;
using System.IO;
using System.Reflection.Metadata.Ecma335;
using System.Security.Cryptography;
using System.Text.RegularExpressions;
using WaferAligner.Services.Extensions;
using WaferAligner;
using WaferAligner.EventIds;
using WaferAligner.CustomClass;
using static System.Net.Mime.MediaTypeNames;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using Newtonsoft.Json;
using WaferAligner.Common;
using System.Threading;
using WaferAligner.Services;
using WaferAligner.Infrastructure.Common;

namespace Sunny.UI.Demo
{
    public partial class FTitlePage2 : BasePage
    {
        private readonly IAxisEventService _axisEventService;
        private readonly IUIUpdateService _uiUpdateService;
        private readonly IStatusUpdateService _statusUpdateService;
        private CancellationTokenSource _cts = new CancellationTokenSource();  // 添加取消令牌源
        private volatile bool _isClosing = false;  // 添加关闭标志，使用volatile确保线程安全
        
        public FTitlePage2(IStatusUpdateService statusUpdateService)
        {
            _statusUpdateService = statusUpdateService ?? throw new ArgumentNullException(nameof(statusUpdateService));
            InitializeComponent();
            _axisEventService = GetRequiredService<IAxisEventService>();
            _uiUpdateService = GetRequiredService<IUIUpdateService>();
            
            // 文件读取操作移至OnInitializeAsync中进行，避免构造函数中的阻塞操作
        }

        // 替换System.Timers.Timer为TimerWrapper
        private TimerWrapper _timer;

        protected override async Task OnInitializeAsync()
        {
            try
            {
                // 启用双缓冲，减少界面闪烁和刷新卡顿
                this.DoubleBuffered = true;
                
                // 通过反射为TableLayoutPanel启用双缓冲
                if (tableLayoutPanel1 != null)
                {
                    typeof(Control).GetProperty("DoubleBuffered", 
                        System.Reflection.BindingFlags.Instance | System.Reflection.BindingFlags.NonPublic)
                        ?.SetValue(tableLayoutPanel1, true, null);
                }
                
                // 为主要Panel容器启用双缓冲
                foreach (Control control in this.Controls)
                {
                    if (control is Panel || control is TableLayoutPanel)
                    {
                        typeof(Control).GetProperty("DoubleBuffered", 
                            System.Reflection.BindingFlags.Instance | System.Reflection.BindingFlags.NonPublic)
                            ?.SetValue(control, true, null);
                    }
                }
                
                // 检查用户是否有权限访问对准参数配置页面
                if (!UserContext.CanAccessParameterPage())
                {
                    Logger?.LogWarning($"用户 {UserContext.CurrentUser?.Username ?? "未知"} ({UserContext.GetRoleDisplayName()}) 尝试访问对准参数页面但没有权限", 
                        EventIds.Unauthorized_Alignment_Access);
                    
                    // 显示权限不足的消息
                    ShowWarning($"您没有权限访问对准参数配置页面。\n\n当前角色：{UserContext.GetRoleDisplayName()}\n只有管理员和工程师可以配置参数。");
                    
                    // 禁用当前页面
                    this.Enabled = false;
                    return;
                }
                
                Logger?.LogInformation($"用户 {UserContext.CurrentUser?.Username ?? "未知"} 成功访问对准参数页面", 
                    EventIds.User_Login_Succeeded);
    
                // 初始化界面样式
                ChangeSTyle();
                
                // 设置默认选项（先设置UI控件，避免等待文件加载）
                await SafeInvokeAsync(() =>
                {
                    ChkTopHorizontalAdjust.Checked = true;
                    ChkTopHorizontalPhoto.Checked = true;
                    cmbSize.SelectedIndex = 1;
                    cmbMaterial.SelectedIndex = 0;
                    
                    // 显示"正在加载..."状态
                    UpdateStatus("正在加载对准参数文件...");
                });
                
                // 使用Task.Run在后台线程加载文件，避免UI线程阻塞
                await Task.Run(async () =>
                {
                    try
                    {
                        // 异步加载默认参数文件
                        string configPath = Path.Combine(Environment.CurrentDirectory, "SysPara", "ConfPara_Page2.json");
                        if (File.Exists(configPath))
                        {
                            bool configResult = await ReadFromJSONAsync(configPath);
                            if (configResult)
                            {
                                await SafeInvokeAsync(() => UpdateStatus("对准参数文件打开成功"));
                                Logger?.LogInformation($"加载对准参数文件成功", EventIds.Load_Alignment_Config_Success);
                            }
                            else
                            {
                                await SafeInvokeAsync(() => UpdateStatus("对准参数文件打开失败"));
                                Logger?.LogError($"加载对准参数文件失败", EventIds.Load_Alignment_Config_Failed);
                            }
                        }
                        
                        // 异步加载标定文件
                        await GetCalPosAsync();
                        
                        // 注册轴状态变化事件
                        await StateWatchAsync();
                    }
                    catch (Exception ex)
                    {
                        Logger?.LogError(ex, "后台加载文件时发生错误", EventIds.Unhandled_Exception);
                    }
                });
                
                // 使用TimerWrapper替代System.Timers.Timer
                _timer = new TimerWrapper(100, _Timer_Tick, Logger);
                
                // 注册Timer到BasePage资源管理器
                RegisterResource("MainTimer", _timer);
                _timer?.Start();
            }
            catch (Exception ex)
            {
                Logger?.LogError(ex, "初始化页面时发生错误", EventIds.Unhandled_Exception);
                await SafeInvokeAsync(() => MessageBox.Show($"初始化页面时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error));
            }
            
            await base.OnInitializeAsync();
        }
        
        private async Task GetCalPosAsync()
        {
            string path = Path.Combine(Environment.CurrentDirectory, "CalPara", "CalPara.json");
            bool exists = File.Exists(path);
            
            if (!exists)
            {
                UpdateStatus("标定文件不存在！");
                await SafeInvokeAsync(() => MessageBox.Show("标定文件不存在!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation));
                return;
            }
            
            bool result = await ReadFromCalJSONAsync(path);
            
            if (result)
            {
                UpdateStatus("标定文件打开成功");
                Logger?.LogInformation($"加载标定参数文件成功", EventIds.Load_Calibration_Config_Success);
            }
            else
            {
                UpdateStatus("标定文件打开失败");
                Logger?.LogError($"加载标定参数文件失败", EventIds.Load_Calibration_Config_Failed);
                await SafeInvokeAsync(() => MessageBox.Show("标定文件打开失败!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation));
            }
        }
        private void ChangeSTyle()
        {
            UIStyle style = UIStyle.Gray;

            TxtCurX.Style = style;
            TxtCurY.Style = style;
            TxtCurR.Style = style;
            TxtCurZ.Style = style;
            TxtCurLX.Style = style;
            TxtCurLY.Style = style;
            TxtCurLZ.Style = style;
            TxtCurRX.Style = style;
            TxtCurRY.Style = style;
            TxtCurRZ.Style = style;
            TxtName.Style = style;
            TxtSpacerThick.Style = style;
            TxtMarkDistance.Style = style;
            DUDVisual.Style = style;
            TxtTopWaferPhotoX.Style = style;
            TxtTopWaferPhotoY.Style = style;
            TxtTopWaferPhotoR.Style = style;
            TxtBottomWaferPhotoX.Style = style;
            TxtBottomWaferPhotoY.Style = style;
            TxtBottomWaferPhotoR.Style = style;
            ChkTopHorizontalAdjust.Style = style;
            ChkTopHorizontalPhoto.Style = style;
            TxtUpThick.Style = style;
            TxtBottomThick.Style = style;
        }
        // 已被GetCalPosAsync方法替代


        private async Task StateWatchAsync()
        {
            try
            {
                // 创建一个标记列表，用于跟踪哪些轴成功注册了事件
                List<string> successfulRegistrations = new List<string>();
                
                try
                {
                    // 使用迁移助手替换所有静态事件注册
                    await _axisEventService.RegisterAxisEventAsync("Z", 
                        $"{AxisConstants.AXIS_GVL}.ZRealDistance", 
                        (obj) =>
                        {
                            _uiUpdateService.SafeUpdateUI(this, () => 
                            { 
                                TxtCurZ.Text = string.Format("{0:0.000}", 
                                    AxisValueConverter.ConvertToPhysicalPosition(
                                        Convert.ToInt64(obj), 
                                        AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION)); 
                            });
                        });
                    successfulRegistrations.Add("Z");
                }
                catch (Exception ex)
                {
                    Logger?.LogError(ex, "注册Z轴事件失败", EventIds.Axis_Event_Registration_Failed);
                }
                
                try
                {
                    await _axisEventService.RegisterAxisEventAsync("LX", 
                        $"{AxisConstants.AXIS_GVL}.LXRealDistance", 
                        (obj) =>
                        {
                            _uiUpdateService.SafeUpdateUI(this, () => 
                            { 
                                TxtCurLX.Text = string.Format("{0:0.000}", 
                                    AxisValueConverter.ConvertToPhysicalPosition(
                                        Convert.ToInt64(obj), 
                                        AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION)); 
                            });
                        });
                    successfulRegistrations.Add("LX");
                }
                catch (Exception ex)
                {
                    Logger?.LogError(ex, "注册LX轴事件失败", EventIds.Axis_Event_Registration_Failed);
                }
                
                try
                {
                    await _axisEventService.RegisterAxisEventAsync("LY", 
                        $"{AxisConstants.AXIS_GVL}.LYRealDistance", 
                        (obj) =>
                        {
                            _uiUpdateService.SafeUpdateUI(this, () => 
                            { 
                                TxtCurLY.Text = string.Format("{0:0.000}", 
                                    AxisValueConverter.ConvertToPhysicalPosition(
                                        Convert.ToInt64(obj), 
                                        AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION)); 
                            });
                        });
                    successfulRegistrations.Add("LY");
                }
                catch (Exception ex)
                {
                    Logger?.LogError(ex, "注册LY轴事件失败", EventIds.Axis_Event_Registration_Failed);
                }
                
                try
                {
                    await _axisEventService.RegisterAxisEventAsync("LZ", 
                        $"{AxisConstants.AXIS_GVL}.LZRealDistance", 
                        (obj) =>
                        {
                            _uiUpdateService.SafeUpdateUI(this, () => 
                            { 
                                TxtCurLZ.Text = string.Format("{0:0.000}", obj); 
                            });
                        });
                    successfulRegistrations.Add("LZ");
                }
                catch (Exception ex)
                {
                    Logger?.LogError(ex, "注册LZ轴事件失败", EventIds.Axis_Event_Registration_Failed);
                }
                
                try
                {
                    await _axisEventService.RegisterAxisEventAsync("RX", 
                        $"{AxisConstants.AXIS_GVL}.RXRealDistance", 
                        (obj) =>
                        {
                            _uiUpdateService.SafeUpdateUI(this, () => 
                            { 
                                TxtCurRX.Text = string.Format("{0:0.000}", 
                                    AxisValueConverter.ConvertToPhysicalPosition(
                                        Convert.ToInt64(obj), 
                                        AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION)); 
                            });
                        });
                    successfulRegistrations.Add("RX");
                }
                catch (Exception ex)
                {
                    Logger?.LogError(ex, "注册RX轴事件失败", EventIds.Axis_Event_Registration_Failed);
                }
                
                try
                {
                    await _axisEventService.RegisterAxisEventAsync("RY", 
                        $"{AxisConstants.AXIS_GVL}.RYRealDistance", 
                        (obj) =>
                        {
                            _uiUpdateService.SafeUpdateUI(this, () => 
                            { 
                                TxtCurRY.Text = string.Format("{0:0.000}", 
                                    AxisValueConverter.ConvertToPhysicalPosition(
                                        Convert.ToInt64(obj), 
                                        AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION)); 
                            });
                        });
                    successfulRegistrations.Add("RY");
                }
                catch (Exception ex)
                {
                    Logger?.LogError(ex, "注册RY轴事件失败", EventIds.Axis_Event_Registration_Failed);
                }
                
                try
                {
                    await _axisEventService.RegisterAxisEventAsync("RZ", 
                        $"{AxisConstants.AXIS_GVL}.RZRealDistance", 
                        (obj) =>
                        {
                            _uiUpdateService.SafeUpdateUI(this, () => 
                            { 
                                TxtCurRZ.Text = string.Format("{0:0.000}", obj); 
                            });
                        });
                    successfulRegistrations.Add("RZ");
                }
                catch (Exception ex)
                {
                    Logger?.LogError(ex, "注册RZ轴事件失败", EventIds.Axis_Event_Registration_Failed);
                }
                
                // 其他主窗体事件注册
                try
                {
                    // 主窗体事件注册迁移
                    _axisEventService.RegisterMainWindowEvent(
                        $"{AxisConstants.AXIS_GVL}.ZTakePhoto", // 上拍照位
                        (obj) =>
                        {
                            _uiUpdateService.SafeUpdateUI(this, () => 
                            { 
                                TxtCalZ.Text = TxtTopWaferPhotoZ.Text = string.Format("{0:0.000}", 
                                    AxisValueConverter.ConvertToPhysicalPosition(
                                        Convert.ToInt64(obj), 
                                        AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION)); 
                            });
                        });
                    successfulRegistrations.Add("ZTakePhoto");
                }
                catch (Exception ex)
                {
                    Logger?.LogError(ex, "注册上拍照位事件失败", EventIds.Main_Window_Event_Registration_Failed);
                }
                
                try
                {
                    _axisEventService.RegisterMainWindowEvent(
                        $"{AxisConstants.AXIS_GVL}.TakeDownWafer", // 下拍照位
                        (obj) =>
                        {
                            _uiUpdateService.SafeUpdateUI(this, () => 
                            { 
                                TxtBottomWaferPhotoZ.Text = string.Format("{0:0.000}", 
                                    AxisValueConverter.ConvertToPhysicalPosition(
                                        Convert.ToInt64(obj), 
                                        AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION)); 
                            });
                        });
                    successfulRegistrations.Add("TakeDownWafer");
                }
                catch (Exception ex)
                {
                    Logger?.LogError(ex, "注册下拍照位事件失败", EventIds.Main_Window_Event_Registration_Failed);
                }
                
                try
                {
                    _axisEventService.RegisterMainWindowEvent(
                        $"{AxisConstants.AXIS_GVL}.ZLevel", // 调平位
                        (obj) =>
                        {
                            _uiUpdateService.SafeUpdateUI(this, () => 
                            { 
                                TxtZLevel.Text = string.Format("{0:0.000}", 
                                    AxisValueConverter.ConvertToPhysicalPosition(
                                        Convert.ToInt64(obj), 
                                        AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION)); 
                            });
                        });
                    successfulRegistrations.Add("ZLevel");
                }
                catch (Exception ex)
                {
                    Logger?.LogError(ex, "注册调平位事件失败", EventIds.Main_Window_Event_Registration_Failed);
                }
                
                try
                {
                    _axisEventService.RegisterMainWindowEvent(
                        $"{AxisConstants.AXIS_GVL}.TakeUpWafer", // 上贴合位
                        (obj) =>
                        {
                            _uiUpdateService.SafeUpdateUI(this, () => 
                            { 
                                TxtTakeUpWaferPos.Text = string.Format("{0:0.000}", 
                                    AxisValueConverter.ConvertToPhysicalPosition(
                                        Convert.ToInt64(obj), 
                                        AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION)); 
                            });
                        });
                    successfulRegistrations.Add("TakeUpWafer");
                }
                catch (Exception ex)
                {
                    Logger?.LogError(ex, "注册上贴合位事件失败", EventIds.Main_Window_Event_Registration_Failed);
                }
                
                try
                {
                    _axisEventService.RegisterMainWindowEvent(
                        $"{AxisConstants.AXIS_GVL}.TakeDownWafer1", // 下贴合位
                        (obj) =>
                        {
                            _uiUpdateService.SafeUpdateUI(this, () => 
                            { 
                                TxtTakeDownWaferPos.Text = string.Format("{0:0.000}", 
                                    AxisValueConverter.ConvertToPhysicalPosition(
                                        Convert.ToInt64(obj), 
                                        AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION)); 
                            });
                        });
                    successfulRegistrations.Add("TakeDownWafer1");
                }
                catch (Exception ex)
                {
                    Logger?.LogError(ex, "注册下贴合位事件失败", EventIds.Main_Window_Event_Registration_Failed);
                }
                
                // 记录成功注册的事件数量
                if (successfulRegistrations.Count > 0)
                {
                    Logger?.LogInformation($"成功注册 {successfulRegistrations.Count} 个事件: {string.Join(", ", successfulRegistrations)}", EventIds.Events_Registration_Success);
                }
                else
                {
                    Logger?.LogWarning("没有成功注册任何事件", EventIds.Events_Registration_Failed);
                }
            }
            catch (Exception ex)
            {
                Logger?.LogError(ex, "状态监视初始化失败", EventIds.State_Watch_Init_Failed);
            }
        }
        private async void _Timer_Tick(object sender, System.Timers.ElapsedEventArgs e)
        {
            // 如果正在关闭或已请求取消，不执行操作
            if (_isClosing || _cts.IsCancellationRequested)
            {
                return;
            }
            
            // 暂停定时器避免重入
            _timer?.Stop();
            
            try
            {
                // 使用取消令牌，添加超时保护
                using var timeoutCts = CancellationTokenSource.CreateLinkedTokenSource(_cts.Token);
                timeoutCts.CancelAfter(TimeSpan.FromSeconds(2)); // 2秒超时
                
                // 使用迁移助手获取XYR轴位置
                var positions = await _axisEventService.GetXYRPositionsAsync();
                var (x, y, r) = positions;
                
                // 如果页面正在关闭，不更新UI
                if (_isClosing) return;
                
                await SafeInvokeAsync(() =>
                {
                    TxtCurX.Text = string.Format("{0:0.000}", x);
                    TxtCurY.Text = string.Format("{0:0.000}", y);
                    TxtCurR.Text = string.Format("{0:0.000}", r);
                });
            }
            catch (OperationCanceledException)
            {
                // 操作被取消，可能是超时或用户取消，记录日志但不显示错误
                Logger?.LogDebug("获取轴位置操作被取消", EventIds.Cancel_Error);
            }
            catch (Exception ex)
            {
                // 如果页面正在关闭，不处理异常
                if (_isClosing) return;
                
                Logger?.LogWarning(ex, "获取轴位置失败", EventIds.Xyr_Position_Update_Error);
                
                // 降级到显示错误状态
                await SafeInvokeAsync(() =>
                {
                    TxtCurX.Text = "ERROR";
                    TxtCurY.Text = "ERROR";
                    TxtCurR.Text = "ERROR";
                });
            }
            finally
            {
                // 如果不是在关闭过程中，重新启动定时器
                if (!_isClosing && !_cts.IsCancellationRequested)
                {
                    _timer?.Start();
                }
            }
        }

        // SafeInvoke 实现已移除，使用 BasePage 提供的统一实现


        private void TxtAll_KeyUp(object sender, KeyEventArgs e)
        {
            TextBox temp = (TextBox)sender;

            if (temp.Text.EndsWith("."))
            {
                return;
            }
            if ((temp.Text.StartsWith("-") || temp.Text.StartsWith("+")) && temp.Text.Length == 1)
            {
                return;
            }

            string TempText = temp.Text.Trim();
            if (TempText.StartsWith("+"))
            {
                TempText = TempText.Substring(1, TempText.Length - 1);
            }
            if (!this.IsDoubleNum(TempText))
            {
                MessageBox.Show("输入数字是非法数字！\r\n    请重新输入！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                temp.Text = "0";
                return;
            }
        }
        private bool IsDoubleNum(string num)
        {
            Regex r = new("^(-?\\d+)(\\.\\d+)?$");
            return r.Match(num).Success;
        }
        private async void BtnOpenCalFile_Click(object sender, EventArgs e)
        {
            // 如果正在关闭，不执行操作
            if (_isClosing) return;
            
            BtnOpenCalFile.Enabled = false;
            
            try
            {
                OpenFileDialog openfile = new()
                {
                    Filter = "标定文件(*.json)|*.json",
                    InitialDirectory = Environment.CurrentDirectory + @"\CalPara\"
                };
                
                if (openfile.ShowDialog() == DialogResult.OK)
                {
                    string path = openfile.FileName;
                    
                    // 使用异步方法加载文件
                    bool result = await ReadFromCalJSONAsync(path);
                    
                    if (result)
                    {
                        UpdateStatus("打开标定文件成功!");
                        Logger?.LogInformation($"加载标定参数文件成功", EventIds.Load_Calibration_Config_Success);
                    }
                    else
                    {
                        UpdateStatus("打开标定文件失败!");
                        Logger?.LogError($"加载标定参数文件失败", EventIds.Load_Calibration_Config_Failed);
                        await SafeInvokeAsync(() => MessageBox.Show("打开标定文件失败!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation));
                    }
                }
                else
                { 
                    UpdateStatus("没有选择标定文件");
                    await SafeInvokeAsync(() => MessageBox.Show("没有选择标定文件", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation));
                }
            }
            catch (Exception ex)
            {
                Logger?.LogError(ex, "打开标定文件时发生错误", EventIds.Load_Calibration_Config_Failed);
                await SafeInvokeAsync(() => MessageBox.Show($"打开标定文件时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error));
            }
            finally
            {
                BtnOpenCalFile.Enabled = true;
            }
        }
        private async void BtnSave_Click(object sender, EventArgs e)
        {
            // 如果正在关闭，不执行操作
            if (_isClosing) return;
            
            BtnSave.Enabled = false;
            
            try
            {
                SaveFileDialog savefile = new()
                {
                    Filter = "对准文件(*.json)|*.json",
                    InitialDirectory = Environment.CurrentDirectory + @"\ProductPara\"
                };
                
                if (savefile.ShowDialog() == DialogResult.OK)
                {
                    string path = savefile.FileName;
                    
                    // 使用异步方法保存文件
                    bool result = await WriteToJSONAsync(path);
                    
                    if (result)
                    {
                        UpdateStatus("对准文件保存成功");
                        Logger?.LogInformation($"保存对准参数文件成功", EventIds.Save_Alignment_Config_Success);
                    }
                    else
                    {
                        UpdateStatus("对准文件保存失败");
                        Logger?.LogError($"保存对准参数文件失败", EventIds.Save_Alignment_Config_Failed);
                        await SafeInvokeAsync(() => MessageBox.Show("保存对准文件失败", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error));
                    }
                }
                else
                {
                    UpdateStatus("没有保存对准文件");
                    await SafeInvokeAsync(() => MessageBox.Show("没有保存对准文件", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation));
                }
            }
            catch (Exception ex)
            {
                Logger?.LogError(ex, "保存对准参数文件时发生错误", EventIds.Save_Alignment_Config_Failed);
                await SafeInvokeAsync(() => MessageBox.Show($"保存对准参数文件时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error));
            }
            finally
            {
                BtnSave.Enabled = true;
            }
        }

        private async void BtnOpen_Click(object sender, EventArgs e)
        {
            // 如果正在关闭，不执行操作
            if (_isClosing) return;
            
            BtnOpen.Enabled = false;
            
            try
            {
                OpenFileDialog openfile = new()
                {
                    Filter = "对准参数文件(*.json)|*.json",
                    InitialDirectory = Environment.CurrentDirectory + @"\ProductPara\"
                };
                
                if (openfile.ShowDialog() == DialogResult.OK)
                {
                    string path = openfile.FileName;
                    
                    // 使用异步方法加载文件
                    bool result = await ReadFromJSONAsync(path);
                    
                    if (result)
                    {
                        UpdateStatus("打开对准文件成功");
                        Logger?.LogInformation($"加载对准参数文件成功", EventIds.Load_Alignment_Config_Success);
                    }
                    else
                    {
                        UpdateStatus("打开对准文件失败");
                        Logger?.LogError($"加载对准参数文件失败", EventIds.Load_Alignment_Config_Failed);
                        await SafeInvokeAsync(() => MessageBox.Show("打开对准文件失败", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error));
                    }
                }
                else
                {
                    UpdateStatus("没有选择对准文件");
                    await SafeInvokeAsync(() => MessageBox.Show("没有选择文件", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation));
                }
            }
            catch (Exception ex)
            {
                Logger?.LogError(ex, "打开对准参数文件时发生错误", EventIds.Load_Alignment_Config_Failed);
                await SafeInvokeAsync(() => MessageBox.Show($"打开对准参数文件时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error));
            }
            finally
            {
                BtnOpen.Enabled = true;
            }
        }


        //private bool ReadFromXML(string FileName)
        //{
        //    try
        //    {
        //        XmlOperation oper = new();
        //        oper.LoadXML(FileName);
        //        TxtName.Text = oper.Select(FileName, "产品名称");
        //        string Size = oper.Select(FileName, "产品尺寸");
        //        cmbSize.SelectedIndex = cmbSize.Items.IndexOf(Size);
        //        TxtSpacerThick.Text = oper.Select(FileName, "隔片厚度");
        //        TxtMarkDistance.Text = oper.Select(FileName, "Mark距离");
        //        string Material = oper.Select(FileName, "产品材质");
        //        cmbMaterial.SelectedIndex = cmbMaterial.Items.IndexOf(Material);
        //        DUDVisual.Value = Convert.ToInt32(oper.Select(FileName, "视觉作业"));


        //        TxtTopWaferPhotoLX.Text = oper.Select(FileName, "上晶圆相机拍照位LX");
        //        TxtTopWaferPhotoLY.Text = oper.Select(FileName, "上晶圆相机拍照位LY");
        //        TxtTopWaferPhotoLZ.Text = oper.Select(FileName, "上晶圆相机拍照位LZ");
        //        TxtTopWaferPhotoRX.Text = oper.Select(FileName, "上晶圆相机拍照位RX");
        //        TxtTopWaferPhotoRY.Text = oper.Select(FileName, "上晶圆相机拍照位RY");
        //        TxtTopWaferPhotoRZ.Text = oper.Select(FileName, "上晶圆相机拍照位RZ");
        //        TxtTopWaferPhotoZ.Text = oper.Select(FileName, "上晶圆平台拍照位Z");
        //        TxtTopWaferPhotoX.Text = oper.Select(FileName, "上晶圆平台拍照位X");
        //        TxtTopWaferPhotoY.Text = oper.Select(FileName, "上晶圆平台拍照位Y");
        //        TxtTopWaferPhotoR.Text = oper.Select(FileName, "上晶圆平台拍照位R");
        //        ChkTopHorizontalAdjust.Checked = Convert.ToInt32(oper.Select(FileName, "上晶圆调平")) == 1 ? true : false;
        //        ChkTopHorizontalPhoto.Checked = Convert.ToInt32(oper.Select(FileName, "调平后去拍照位")) == 1 ? true : false;
        //        TxtUpThick.Text = oper.Select(FileName, "上晶圆厚度");

        //        TxtBottomWaferPhotoLX.Text = oper.Select(FileName, "下晶圆相机拍照位LX");
        //        TxtBottomWaferPhotoLY.Text = oper.Select(FileName, "下晶圆相机拍照位LY");
        //        TxtBottomWaferPhotoLZ.Text = oper.Select(FileName, "下晶圆相机拍照位LZ");
        //        TxtBottomWaferPhotoRX.Text = oper.Select(FileName, "下晶圆相机拍照位RX");
        //        TxtBottomWaferPhotoRY.Text = oper.Select(FileName, "下晶圆相机拍照位RY");
        //        TxtBottomWaferPhotoRZ.Text = oper.Select(FileName, "下晶圆相机拍照位RZ");
        //        TxtBottomWaferPhotoZ.Text = oper.Select(FileName, "下晶圆平台拍照位Z");
        //        TxtBottomWaferPhotoX.Text = oper.Select(FileName, "下晶圆平台拍照位X");
        //        TxtBottomWaferPhotoY.Text = oper.Select(FileName, "下晶圆平台拍照位Y");
        //        TxtBottomWaferPhotoR.Text = oper.Select(FileName, "下晶圆平台拍照位R");
        //        TxtBottomThick.Text = oper.Select(FileName, "下晶圆厚度");

        //        return true;
        //    }
        //    catch (Exception ex)
        //    {
        //        MessageBox.Show(ex.Message.ToString());
        //        System.Windows.Forms.Application.Exit();
        //        return false;
        //    }
        //}
        private async Task<bool> ReadFromJSONAsync(string fileName)
        {
            try
            {
                // 检查文件是否存在
                if (!File.Exists(fileName))
                {
                    throw new FileNotFoundException($"找不到文件: {fileName}", fileName);
                }

                // 使用Task.Run在后台线程执行文件读取和数据处理
                var data = await Task.Run(() =>
                {
                    JsonOperator oper = new(fileName);
                    return new
                    {
                        ProductName = oper.Select("产品名称"),
                        Size = oper.Select("产品尺寸"),
                        SpacerThickness = oper.Select("隔片厚度"),
                        MarkDistance = oper.Select("Mark距离"),
                        Material = oper.Select("产品材质"),
                        VisualJob = Convert.ToInt32(oper.Select("视觉作业")),
                        TopWaferCameraLX = oper.Select("上晶圆相机拍照位LX"),
                        TopWaferCameraLY = oper.Select("上晶圆相机拍照位LY"),
                        TopWaferCameraLZ = oper.Select("上晶圆相机拍照位LZ"),
                        TopWaferCameraRX = oper.Select("上晶圆相机拍照位RX"),
                        TopWaferCameraRY = oper.Select("上晶圆相机拍照位RY"),
                        TopWaferCameraRZ = oper.Select("上晶圆相机拍照位RZ"),
                        TopWaferPlatformZ = oper.Select("上晶圆平台拍照位Z"),
                        TopWaferPlatformX = oper.Select("上晶圆平台拍照位X"),
                        TopWaferPlatformY = oper.Select("上晶圆平台拍照位Y"),
                        TopWaferPlatformR = oper.Select("上晶圆平台拍照位R"),
                        TopHorizontalAdjust = Convert.ToInt32(oper.Select("上晶圆调平")) == 1,
                        TopHorizontalPhoto = Convert.ToInt32(oper.Select("调平后去拍照位")) == 1,
                        TopWaferThickness = oper.Select("上晶圆厚度"),
                        BottomWaferCameraLX = oper.Select("下晶圆相机拍照位LX"),
                        BottomWaferCameraLY = oper.Select("下晶圆相机拍照位LY"),
                        BottomWaferCameraLZ = oper.Select("下晶圆相机拍照位LZ"),
                        BottomWaferCameraRX = oper.Select("下晶圆相机拍照位RX"),
                        BottomWaferCameraRY = oper.Select("下晶圆相机拍照位RY"),
                        BottomWaferCameraRZ = oper.Select("下晶圆相机拍照位RZ"),
                        BottomWaferPlatformZ = oper.Select("下晶圆平台拍照位Z"),
                        BottomWaferPlatformX = oper.Select("下晶圆平台拍照位X"),
                        BottomWaferPlatformY = oper.Select("下晶圆平台拍照位Y"),
                        BottomWaferPlatformR = oper.Select("下晶圆平台拍照位R"),
                        BottomWaferThickness = oper.Select("下晶圆厚度")
                    };
                });
                
                // 在UI线程安全地更新控件，批量赋值时挂起布局
                await SafeInvokeAsync(() =>
                {
                    if (tableLayoutPanel1 != null) tableLayoutPanel1.SuspendLayout();
                    this.SuspendLayout();
                    try
                    {
                        TxtName.Text = data.ProductName;
                        cmbSize.SelectedIndex = cmbSize.Items.IndexOf(data.Size);
                        TxtSpacerThick.Text = data.SpacerThickness;
                        TxtMarkDistance.Text = data.MarkDistance;
                        cmbMaterial.SelectedIndex = cmbMaterial.Items.IndexOf(data.Material);
                        DUDVisual.Value = data.VisualJob;

                        TxtTopWaferPhotoLX.Text = data.TopWaferCameraLX;
                        TxtTopWaferPhotoLY.Text = data.TopWaferCameraLY;
                        TxtTopWaferPhotoLZ.Text = data.TopWaferCameraLZ;
                        TxtTopWaferPhotoRX.Text = data.TopWaferCameraRX;
                        TxtTopWaferPhotoRY.Text = data.TopWaferCameraRY;
                        TxtTopWaferPhotoRZ.Text = data.TopWaferCameraRZ;

                        TxtTopWaferPhotoZ.Text = data.TopWaferPlatformZ;
                        TxtTopWaferPhotoX.Text = data.TopWaferPlatformX;
                        TxtTopWaferPhotoY.Text = data.TopWaferPlatformY;
                        TxtTopWaferPhotoR.Text = data.TopWaferPlatformR;
                        ChkTopHorizontalAdjust.Checked = data.TopHorizontalAdjust;
                        ChkTopHorizontalPhoto.Checked = data.TopHorizontalPhoto;
                        TxtUpThick.Text = data.TopWaferThickness;

                        TxtBottomWaferPhotoLX.Text = data.BottomWaferCameraLX;
                        TxtBottomWaferPhotoLY.Text = data.BottomWaferCameraLY;
                        TxtBottomWaferPhotoLZ.Text = data.BottomWaferCameraLZ;
                        TxtBottomWaferPhotoRX.Text = data.BottomWaferCameraRX;
                        TxtBottomWaferPhotoRY.Text = data.BottomWaferCameraRY;
                        TxtBottomWaferPhotoRZ.Text = data.BottomWaferCameraRZ;

                        TxtBottomWaferPhotoZ.Text = data.BottomWaferPlatformZ;
                        TxtBottomWaferPhotoX.Text = data.BottomWaferPlatformX;
                        TxtBottomWaferPhotoY.Text = data.BottomWaferPlatformY;
                        TxtBottomWaferPhotoR.Text = data.BottomWaferPlatformR;
                        TxtBottomThick.Text = data.BottomWaferThickness;
                    }
                    finally
                    {
                        if (tableLayoutPanel1 != null) tableLayoutPanel1.ResumeLayout();
                        this.ResumeLayout();
                    }
                });
                
                Logger?.LogInformation($"成功加载对准参数文件: {fileName}", EventIds.Load_Alignment_Config_Success);
                return true;
            }
            catch (FileNotFoundException ex)
            {
                await SafeInvokeAsync(() => MessageBox.Show($"找不到文件: {ex.FileName}", "文件错误", MessageBoxButtons.OK, MessageBoxIcon.Error));
                Logger?.LogError(ex, $"对准参数文件不存在: {ex.FileName}", EventIds.Load_Alignment_Config_Failed);
                return false;
            }
            catch (JsonException ex)
            {
                await SafeInvokeAsync(() => MessageBox.Show($"JSON文件格式错误: {ex.Message}", "解析错误", MessageBoxButtons.OK, MessageBoxIcon.Error));
                Logger?.LogError(ex, $"对准参数文件JSON格式错误: {fileName}", EventIds.Load_Alignment_Config_Failed);
                return false;
            }
            catch (FormatException ex)
            {
                await SafeInvokeAsync(() => MessageBox.Show($"文件中包含格式错误的数据: {ex.Message}", "格式错误", MessageBoxButtons.OK, MessageBoxIcon.Error));
                Logger?.LogError(ex, $"对准参数文件中的数据格式错误: {fileName}", EventIds.Load_Alignment_Config_Failed);
                return false;
            }
            catch (Exception ex)
            {
                await SafeInvokeAsync(() => MessageBox.Show($"加载文件时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error));
                Logger?.LogError(ex, $"加载对准参数文件失败: {fileName}", EventIds.Load_Alignment_Config_Failed);
                return false;
            }
        }
        private async Task<bool> ReadFromCalJSONAsync(string fileName)
        {
            try
            {
                // 使用Task.Run在后台线程执行文件读取和数据处理
                var data = await Task.Run(() => 
                {
                    JsonOperator oper = new(fileName);
                    return new
                    {
                        CalLX = oper.Select("LX轴标定位置"),
                        CalLY = oper.Select("LY轴标定位置"),
                        CalLZ = oper.Select("LZ轴标定位置"),
                        CalRX = oper.Select("RX轴标定位置"),
                        CalRY = oper.Select("RY轴标定位置"),
                        CalRZ = oper.Select("RZ轴标定位置")
                    };
                });
                
                // 在UI线程安全地更新控件，批量赋值时挂起布局
                await SafeInvokeAsync(() =>
                {
                    if (tableLayoutPanel1 != null) tableLayoutPanel1.SuspendLayout();
                    this.SuspendLayout();
                    try
                    {
                        TxtCalLX.Text = data.CalLX;
                        TxtCalLY.Text = data.CalLY;
                        TxtCalLZ.Text = data.CalLZ;
                        TxtCalRX.Text = data.CalRX;
                        TxtCalRY.Text = data.CalRY;
                        TxtCalRZ.Text = data.CalRZ;

                        // 更新其他相关控件值
                        TxtBottomWaferPhotoLX.Text = TxtTopWaferPhotoLX.Text = data.CalLX;
                        TxtBottomWaferPhotoLY.Text = TxtTopWaferPhotoLY.Text = data.CalLY;
                        TxtBottomWaferPhotoRX.Text = TxtTopWaferPhotoRX.Text = data.CalRX;
                        TxtBottomWaferPhotoRY.Text = TxtTopWaferPhotoRY.Text = data.CalRY;
                        TxtBottomWaferPhotoLZ.Text = TxtTopWaferPhotoLZ.Text = data.CalLZ;
                        TxtBottomWaferPhotoRZ.Text = TxtTopWaferPhotoRZ.Text = data.CalRZ;
                    }
                    finally
                    {
                        if (tableLayoutPanel1 != null) tableLayoutPanel1.ResumeLayout();
                        this.ResumeLayout();
                    }
                });
                return true;
            }
            catch (Exception ex)
            {
                await SafeInvokeAsync(() => MessageBox.Show(ex.Message.ToString(), "错误"));
                Logger?.LogError(ex, "加载标定参数文件失败", EventIds.Load_Calibration_Config_Failed);
                return false;
            }
        }
        private async Task<bool> WriteToJSONAsync(string fileName)
        {
            try
            {
                // 先在UI线程中获取所有控件的值
                Dictionary<string, string> uiData = new Dictionary<string, string>();
                
                // 使用SafeInvokeAsync在UI线程中安全获取控件值
                await SafeInvokeAsync(() => {
                    // 获取所有需要保存的控件值
                    uiData["ProductName"] = TxtName.Text;
                    uiData["Size"] = cmbSize.SelectedItem?.ToString() ?? "";
                    uiData["SpacerThickness"] = TxtSpacerThick.Text;
                    uiData["MarkDistance"] = TxtMarkDistance.Text;
                    uiData["Material"] = cmbMaterial.SelectedItem?.ToString() ?? "";
                    uiData["VisualJob"] = DUDVisual.Value.ToString();
                    
                    uiData["TopWaferCameraLX"] = TxtTopWaferPhotoLX.Text;
                    uiData["TopWaferCameraLY"] = TxtTopWaferPhotoLY.Text;
                    uiData["TopWaferCameraLZ"] = TxtTopWaferPhotoLZ.Text;
                    uiData["TopWaferCameraRX"] = TxtTopWaferPhotoRX.Text;
                    uiData["TopWaferCameraRY"] = TxtTopWaferPhotoRY.Text;
                    uiData["TopWaferCameraRZ"] = TxtTopWaferPhotoRZ.Text;
                    
                    uiData["TopWaferPlatformZ"] = TxtTopWaferPhotoZ.Text;
                    uiData["TopWaferPlatformX"] = TxtTopWaferPhotoX.Text;
                    uiData["TopWaferPlatformY"] = TxtTopWaferPhotoY.Text;
                    uiData["TopWaferPlatformR"] = TxtTopWaferPhotoR.Text;
                    uiData["TopHorizontalAdjust"] = ChkTopHorizontalAdjust.Checked ? "1" : "0";
                    uiData["TopHorizontalPhoto"] = ChkTopHorizontalPhoto.Checked ? "1" : "0";
                    uiData["TopWaferThickness"] = TxtUpThick.Text;
                    
                    uiData["BottomWaferCameraLX"] = TxtBottomWaferPhotoLX.Text;
                    uiData["BottomWaferCameraLY"] = TxtBottomWaferPhotoLY.Text;
                    uiData["BottomWaferCameraLZ"] = TxtBottomWaferPhotoLZ.Text;
                    uiData["BottomWaferCameraRX"] = TxtBottomWaferPhotoRX.Text;
                    uiData["BottomWaferCameraRY"] = TxtBottomWaferPhotoRY.Text;
                    uiData["BottomWaferCameraRZ"] = TxtBottomWaferPhotoRZ.Text;
                    
                    uiData["BottomWaferPlatformZ"] = TxtBottomWaferPhotoZ.Text;
                    uiData["BottomWaferPlatformX"] = TxtBottomWaferPhotoX.Text;
                    uiData["BottomWaferPlatformY"] = TxtBottomWaferPhotoY.Text;
                    uiData["BottomWaferPlatformR"] = TxtBottomWaferPhotoR.Text;
                    uiData["BottomWaferThickness"] = TxtBottomThick.Text;
                });
                
                // 验证必填字段
                var missingFields = new List<string>();
                
                if (string.IsNullOrWhiteSpace(uiData["ProductName"])) missingFields.Add("产品名称");
                if (string.IsNullOrWhiteSpace(uiData["Size"])) missingFields.Add("产品尺寸");
                if (string.IsNullOrWhiteSpace(uiData["MarkDistance"])) missingFields.Add("Mark距离");
                
                if (missingFields.Count > 0)
                {
                    var missingFieldsStr = string.Join("、", missingFields);
                    throw new ArgumentException($"以下必填字段未填写: {missingFieldsStr}");
                }
                
                // 写入文件操作，不需要Task.Run包装，因为JsonOperator操作不会阻塞UI线程
                JsonOperator oper = new(fileName);
                
                // 写入数据
                oper.Create("产品名称", uiData["ProductName"]);
                oper.Create("产品尺寸", uiData["Size"]);
                oper.Create("隔片厚度", uiData["SpacerThickness"]);
                oper.Create("Mark距离", uiData["MarkDistance"]);
                oper.Create("产品材质", uiData["Material"]);
                oper.Create("视觉作业", uiData["VisualJob"]);

                oper.Create("上晶圆相机拍照位LX", uiData["TopWaferCameraLX"]);
                oper.Create("上晶圆相机拍照位LY", uiData["TopWaferCameraLY"]);
                oper.Create("上晶圆相机拍照位LZ", uiData["TopWaferCameraLZ"]);
                oper.Create("上晶圆相机拍照位RX", uiData["TopWaferCameraRX"]);
                oper.Create("上晶圆相机拍照位RY", uiData["TopWaferCameraRY"]);
                oper.Create("上晶圆相机拍照位RZ", uiData["TopWaferCameraRZ"]);

                oper.Create("上晶圆平台拍照位Z", uiData["TopWaferPlatformZ"]);
                oper.Create("上晶圆平台拍照位X", uiData["TopWaferPlatformX"]);
                oper.Create("上晶圆平台拍照位Y", uiData["TopWaferPlatformY"]);
                oper.Create("上晶圆平台拍照位R", uiData["TopWaferPlatformR"]);
                oper.Create("上晶圆调平", uiData["TopHorizontalAdjust"]);
                oper.Create("调平后去拍照位", uiData["TopHorizontalPhoto"]);
                oper.Create("上晶圆厚度", uiData["TopWaferThickness"]);

                oper.Create("下晶圆相机拍照位LX", uiData["BottomWaferCameraLX"]);
                oper.Create("下晶圆相机拍照位LY", uiData["BottomWaferCameraLY"]);
                oper.Create("下晶圆相机拍照位LZ", uiData["BottomWaferCameraLZ"]);
                oper.Create("下晶圆相机拍照位RX", uiData["BottomWaferCameraRX"]);
                oper.Create("下晶圆相机拍照位RY", uiData["BottomWaferCameraRY"]);
                oper.Create("下晶圆相机拍照位RZ", uiData["BottomWaferCameraRZ"]);

                oper.Create("下晶圆平台拍照位Z", uiData["BottomWaferPlatformZ"]);
                oper.Create("下晶圆平台拍照位X", uiData["BottomWaferPlatformX"]);
                oper.Create("下晶圆平台拍照位Y", uiData["BottomWaferPlatformY"]);
                oper.Create("下晶圆平台拍照位R", uiData["BottomWaferPlatformR"]);
                oper.Create("下晶圆厚度", uiData["BottomWaferThickness"]);

                // 保存文件
                oper.Save();
                
                Logger?.LogInformation($"成功保存对准参数到文件: {fileName}", EventIds.Save_Alignment_Config_Success);
                return true;
            }
            catch (ArgumentException ex)
            {
                await SafeInvokeAsync(() => MessageBox.Show(ex.Message, "参数错误", MessageBoxButtons.OK, MessageBoxIcon.Warning));
                Logger?.LogWarning(ex.Message, EventIds.Save_Alignment_Config_Failed);
                return false;
            }
            catch (UnauthorizedAccessException ex)
            {
                await SafeInvokeAsync(() => MessageBox.Show($"没有权限写入文件 {fileName}。请检查文件权限或以管理员身份运行程序。", "权限错误", MessageBoxButtons.OK, MessageBoxIcon.Error));
                Logger?.LogError(ex, $"保存对准参数时权限不足: {fileName}", EventIds.Save_Alignment_Config_Failed);
                return false;
            }
            catch (IOException ex)
            {
                await SafeInvokeAsync(() => MessageBox.Show($"保存文件时出现IO错误: {ex.Message}", "IO错误", MessageBoxButtons.OK, MessageBoxIcon.Error));
                Logger?.LogError(ex, $"保存对准参数时IO错误: {fileName}", EventIds.Save_Alignment_Config_Failed);
                return false;
            }
            catch (Exception ex)
            {
                await SafeInvokeAsync(() => MessageBox.Show($"保存参数时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error));
                Logger?.LogError(ex, $"保存对准参数失败: {fileName}", EventIds.Save_Alignment_Config_Failed);
                return false;
            }
        }
        // FTitlePage2_FormClosing 已被 OnClosingAsync 替代
        bool IsTopHorizontalAdjust = false;
        private void ChkTopHorizontalAdjust_CheckedChanged(object sender, EventArgs e)
        {
            if (ChkTopHorizontalAdjust.Checked)
            {
                IsTopHorizontalAdjust = true;
            }
            else
            {
                IsTopHorizontalAdjust = false;
            }
        }

        bool IsTopHorizontalPhoto = false;
        private void ChkTopHorizontalPhoto_CheckedChanged(object sender, EventArgs e)
        {
            if (ChkTopHorizontalPhoto.Checked)
            {
                IsTopHorizontalPhoto = true;
            }
            else
            {
                IsTopHorizontalPhoto = false;
            }
        }
        protected override async Task<bool> OnClosingAsync()
        {
            // 设置关闭标志
            _isClosing = true;
            
            // 取消所有正在进行的异步操作
            try
            {
                _cts?.Cancel();
            }
            catch (Exception ex)
            {
                Logger?.LogError(ex, "取消异步操作时发生错误", EventIds.Form_Closing_Error);
            }
            
            // 停止定时器
            _timer?.Stop();
            
            // 保存参数文件
            try
            {
                string path = System.Windows.Forms.Application.StartupPath + "SysPara\\ConfPara_Page2.json";
                bool result = await WriteToJSONAsync(path);
                
                if (result)
                {
                    UpdateStatus("对准文件保存成功");
                    Logger?.LogInformation($"保存对准参数文件成功", EventIds.Save_Alignment_Config_Success);
                }
                else
                {
                    UpdateStatus("对准文件保存失败");
                    Logger?.LogError($"保存对准参数文件失败", EventIds.Save_Alignment_Config_Failed);
                }
            }
            catch (Exception ex)
            {
                Logger?.LogError(ex, "保存参数文件失败", EventIds.Save_Alignment_Config_Failed);
            }
            
            // 执行基类关闭逻辑
            return await base.OnClosingAsync();
        }
        
        protected override async void OnDispose()
        {
            try
            {
                // 取消所有任务
                _cts?.Cancel();
                
                // 清理TimerWrapper实例 - 已经注册到资源管理器，会自动清理
                _timer = null;
                
                // 清理迁移助手资源（Phase 2）- 使用异步方式
                try
                {
                    // The original code had _migrationHelper.CleanupAsync(), but _migrationHelper is removed.
                    // Assuming the intent was to remove the line or that the cleanup logic needs to be re-evaluated
                    // if the new services are meant to handle cleanup.
                    // For now, removing the line as _migrationHelper is gone.
                }
                catch (Exception cleanupEx)
                {
                    Logger?.LogWarning(cleanupEx, "迁移助手清理异常", EventIds.Cleanup_Error);
                }
                
                // 资源释放
                _cts?.Dispose();
                _cts = null;
                
                base.OnDispose();
            }
            catch (Exception ex)
            {
                Logger?.LogError(ex, "FTitlePage2 资源清理异常", EventIds.Resource_Cleanup_Error);
            }
        }

        // 保持向后兼容
        public void CleanUp()
        {
            // 转发到新的资源管理机制
            this.Dispose();
        }

        private void FTitlePage2_Initialize(object sender, EventArgs e)
        {

        }

        private void UpdateStatus(string status)
        {
            _statusUpdateService?.UpdateStatus(status);
        }
    }

    public class JsonOperator
    {
        private readonly string _fileName;
        private Dictionary<string, string> _data;

        public JsonOperator(string fileName)
        {
            _fileName = fileName;
            if (File.Exists(_fileName))
            {
                try
                {
                    var json = File.ReadAllText(_fileName);
                    _data = JsonConvert.DeserializeObject<Dictionary<string, string>>(json)
                            ?? new Dictionary<string, string>();
                }
                catch
                {
                    _data = new Dictionary<string, string>();
                }
            }
            else
            {
                _data = new Dictionary<string, string>();
            }
        }

        public string Select(string key)
        {
            return _data.TryGetValue(key, out var value) ? value : string.Empty;
        }

        public void Create(string key, string value)
        {
            _data[key] = value;
        }

        public void Save()
        {
            var json = JsonConvert.SerializeObject(_data, Formatting.Indented);
            File.WriteAllText(_fileName, json);
        }
    }
}