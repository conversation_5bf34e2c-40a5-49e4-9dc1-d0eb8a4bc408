using System;
using System.Threading.Tasks;
using AlignerUI;
using WaferAligner.Communication.Inovance.Abstractions;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;
using System.Collections.Concurrent;
using WaferAligner.Services.Abstractions;
using WaferAligner.Services.Extensions;
using WaferAligner.EventIds;


namespace WaferAligner.Services
{
    /// <summary>
    /// 主窗体ViewModel服务实现
    /// 实现IMainWindowViewModel接口
    /// </summary>
    public class MainWindowViewModelService : IMainWindowViewModel
    {
        private readonly IPlcConnectionManager _plcManager;
        private readonly ILoggingService _loggingService;
        private readonly IServiceProvider _serviceProvider;
        private readonly ConcurrentDictionary<string, Action<object>> _registeredActions;
        private MainWindowViewModel _implementation;
        private volatile bool _disposed = false;

        public MainWindowViewModelService(IPlcConnectionManager plcManager, ILoggingService loggingService, IServiceProvider serviceProvider)
        {
            _plcManager = plcManager ?? throw new ArgumentNullException(nameof(plcManager));
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
            _registeredActions = new ConcurrentDictionary<string, Action<object>>();
        }

        #region 系统状态
        public bool IsSystemReady { get; private set; }
        public bool IsConnected => _plcManager?.IsConnected("Main") ?? false;
        public string SystemStatus { get; private set; } = "初始化中";
        #endregion

        #region 气缸状态属性
        public UInt16 TopWaferState 
        { 
            get => _implementation?.TopWaferState ?? 0; 
            set { if (_implementation != null) _implementation.TopWaferState = value; }
        }
        
        public UInt16 TrayWaferOuterState 
        { 
            get => _implementation?.TrayWaferOuterState ?? 0; 
            set { if (_implementation != null) _implementation.TrayWaferOuterState = value; }
        }
        
        public UInt16 TrayWaferInnerState 
        { 
            get => _implementation?.TrayWaferInnerState ?? 0; 
            set { if (_implementation != null) _implementation.TrayWaferInnerState = value; }
        }
        
        public UInt16 TrayState 
        { 
            get => _implementation?.TrayState ?? 0; 
            set { if (_implementation != null) _implementation.TrayState = value; }
        }
        
        public UInt16 ChuckLockState 
        { 
            get => _implementation?.ChuckLockState ?? 0; 
            set { if (_implementation != null) _implementation.ChuckLockState = value; }
        }
        
        public UInt16 HorizontalAdjustState 
        { 
            get => _implementation?.HorizontalAdjustState ?? 0; 
            set { if (_implementation != null) _implementation.HorizontalAdjustState = value; }
        }
        #endregion

        #region 位置参数属性
        public double TopWaferPhotoZ 
        { 
            get => _implementation?.TopWaferPhotoZ ?? 0; 
            set { if (_implementation != null) _implementation.TopWaferPhotoZ = value; }
        }
        
        public double BottomWaferPhotoZ 
        { 
            get => _implementation?.BottomWaferPhotoZ ?? 0; 
            set { if (_implementation != null) _implementation.BottomWaferPhotoZ = value; }
        }
        
        public double TopWaferTakeUpPos 
        { 
            get => _implementation?.TopWaferTakeUpPos ?? 0; 
            set { if (_implementation != null) _implementation.TopWaferTakeUpPos = value; }
        }
        
        public double BottomWaferTakeDownPos 
        { 
            get => _implementation?.BottomWaferTakeDownPos ?? 0; 
            set { if (_implementation != null) _implementation.BottomWaferTakeDownPos = value; }
        }
        
        public double TopWaferZLevel 
        { 
            get => _implementation?.TopWaferZLevel ?? 0; 
            set { if (_implementation != null) _implementation.TopWaferZLevel = value; }
        }
        #endregion

        #region PLC相关
        public async Task<bool> ConnectPLCAsync()
        {
            try
            {
                var success = await _plcManager.ConnectAsync("XYRAxis", "192.168.1.87", 502);
                if (success)
                {
                    SystemStatus = "PLC连接成功";
                    IsSystemReady = true;
                    _loggingService?.LogInformation("PLC连接成功", WaferAligner.EventIds.EventIds.Plc_Connection_Succeeded);
                }
                else
                {
                    SystemStatus = "PLC连接失败";
                    IsSystemReady = false;
                    _loggingService?.LogError("PLC连接失败", WaferAligner.EventIds.EventIds.Plc_Connection_Failed);
                }
                return success;
            }
            catch (Exception ex)
            {
                _loggingService?.LogError(ex, "连接PLC时发生异常", WaferAligner.EventIds.EventIds.Plc_Connection_Failed);
                SystemStatus = $"PLC连接异常: {ex.Message}";
                IsSystemReady = false;
                return false;
            }
        }

        public async Task DisconnectPLCAsync()
        {
            try
            {
                await _plcManager.DisconnectAsync("XYRAxis");
                SystemStatus = "PLC已断开";
                IsSystemReady = false;
                _loggingService?.LogInformation("PLC连接已断开", WaferAligner.EventIds.EventIds.Plc_Connection_Failed);
            }
            catch (Exception ex)
            {
                _loggingService?.LogError(ex, "断开PLC连接时发生异常", WaferAligner.EventIds.EventIds.Plc_Connection_Failed);
            }
        }

        public async Task<bool> WritePLCVariableAsync(string variableName, object value)
        {
            try
            {
                if (!IsConnected)
                {
                    _loggingService?.LogWarning("PLC未连接，无法写入变量", WaferAligner.EventIds.EventIds.Plc_Connection_Failed);
                    return false;
                }

                if (_implementation != null)
                {
                    // 通过反射调用私有方法WritePLCVariable
                    var method = typeof(MainWindowViewModel).GetMethod("WritePLCVariable", 
                        System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                    if (method != null)
                    {
                        var result = await (Task<bool>)method.Invoke(_implementation, new object[] { variableName, value });
                        return result;
                    }
                }

                _loggingService?.LogWarning($"无法写入PLC变量 {variableName}={value}", WaferAligner.EventIds.EventIds.Plc_Variable_Write_Failed);
                return false;
            }
            catch (Exception ex)
            {
                _loggingService?.LogError(ex, $"写入PLC变量 {variableName}={value} 时发生异常", WaferAligner.EventIds.EventIds.Plc_Variable_Write_Failed);
                return false;
            }
        }

        public async Task<object> ReadPLCVariableAsync(string variableName, Type expectedType)
        {
            try
            {
                if (!IsConnected)
                {
                    _loggingService?.LogWarning("PLC未连接，无法读取变量", WaferAligner.EventIds.EventIds.Plc_Connection_Failed);
                    return null;
                }

                _loggingService?.LogDebug($"读取PLC变量: {variableName}");
                
                // 使用_plcManager获取PLC实例进行读取
                if (_plcManager != null && _plcManager.IsConnected("Main"))
                {
                    try
                    {
                        var plcInstance = _plcManager.GetPlcInstance("Main");
                        if (plcInstance != null)
                        {
                            var readInfo = new PLCVarReadInfo { Name = variableName, Type = expectedType };
                            var result = await plcInstance.ReadVariableAsync(readInfo, CancellationToken.None);
                            
                            // 根据expectedType进行类型转换
                            if (result != null)
                            {
                                return Convert.ChangeType(result, expectedType);
                            }
                            else
                            {
                                _loggingService?.LogWarning($"PLC变量 {variableName} 读取结果为空", WaferAligner.EventIds.EventIds.Plc_Variable_Read_Failed);
                                return null;
                            }
                        }
                        else
                        {
                            _loggingService?.LogWarning("无法获取PLC实例", WaferAligner.EventIds.EventIds.Plc_Connection_Failed);
                            return null;
                        }
                    }
                    catch (Exception ex)
                    {
                        _loggingService?.LogError(ex, $"通过PLC实例读取变量 {variableName} 失败", WaferAligner.EventIds.EventIds.Plc_Variable_Read_Failed);
                        return null;
                    }
                }
                else
                {
                    _loggingService?.LogWarning("PLC管理器未初始化或未连接，无法读取PLC变量", WaferAligner.EventIds.EventIds.Plc_Connection_Failed);
                    return null;
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError(ex, $"读取PLC变量 {variableName} 时发生异常", WaferAligner.EventIds.EventIds.Plc_Variable_Read_Failed);
                return null;
            }
        }
        #endregion

        #region 系统参数
        public async Task<bool> SystemParameterExecuteAsync(double cameraOffset, double bottomPhotoOffset, double topGap, double bottomGap)
        {
            try
            {
                _loggingService.LogInformation($"执行系统参数: CameraOffset={cameraOffset}, BottomPhotoOffset={bottomPhotoOffset}, TopGap={topGap}, BottomGap={bottomGap}", WaferAligner.EventIds.EventIds.Configuration_Loaded);
                return true;
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, "执行系统参数时发生异常", WaferAligner.EventIds.EventIds.Configuration_Error);
                return false;
            }
        }

        public async Task<bool> SystemPareExcute(double cameraOffset, double bottomPhoto, double TopGap, double BottomGap)
        {
            try
            {
                if (_implementation != null)
                {
                    return await _implementation.SystemPareExcute(cameraOffset, bottomPhoto, TopGap, BottomGap);
                }
                _loggingService?.LogWarning("MainWindowViewModel未初始化，无法执行系统参数", WaferAligner.EventIds.EventIds.Resource_Released);
                return false;
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"执行系统参数失败: CameraOffset={cameraOffset}, BottomPhoto={bottomPhoto}, TopGap={TopGap}, BottomGap={BottomGap}", WaferAligner.EventIds.EventIds.Configuration_Error);
                return false;
            }
        }

        public async Task<bool> SystemPareExcuteAsync(double cameraOffset, double bottomPhoto, double TopGap, double BottomGap)
        {
            try
            {
                _loggingService.LogInformation($"执行系统参数配置: CameraOffset={cameraOffset}, BottomPhoto={bottomPhoto}, TopGap={TopGap}, BottomGap={BottomGap}", WaferAligner.EventIds.EventIds.Configuration_Loaded);
                if (_implementation != null)
                {
                    return await _implementation.SystemPareExcute(cameraOffset, bottomPhoto, TopGap, BottomGap);
                }
                _loggingService.LogWarning("MainWindowViewModel未初始化，无法执行系统参数配置", WaferAligner.EventIds.EventIds.UI_Update_Failed);
                return false;
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"执行系统参数配置失败", WaferAligner.EventIds.EventIds.Configuration_Error);
                return false;
            }
        }

        public async Task<bool> RecipePareExcuteAsync()
        {
            try
            {
                _loggingService.LogInformation("执行配方参数配置", WaferAligner.EventIds.EventIds.Configuration_Loaded);
                if (_implementation != null)
                {
                    return await _implementation.RecipePareExcute();
                }
                _loggingService.LogWarning("MainWindowViewModel未初始化，无法执行配方参数配置", WaferAligner.EventIds.EventIds.UI_Update_Failed);
                return false;
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, "执行配方参数配置失败", WaferAligner.EventIds.EventIds.Configuration_Error);
                return false;
            }
        }

        // 系统参数设置方法 - 用于单独配置各参数
        public async Task<bool> TopGapSystemParaAsync(double value)
        {
            try
            {
                _loggingService.LogDebug($"设置上间隙参数: {value}", WaferAligner.EventIds.EventIds.Configuration_Loaded);
                if (_implementation != null)
                {
                    return await _implementation.TopGapSystemPara(value);
                }
                _loggingService.LogWarning("MainWindowViewModel未初始化，无法设置上间隙参数", WaferAligner.EventIds.EventIds.UI_Update_Failed);
                return false;
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"设置上间隙参数失败: {value}", WaferAligner.EventIds.EventIds.Configuration_Error);
                return false;
            }
        }

        public async Task<bool> BottomGapSystemParaAsync(double value)
        {
            try
            {
                _loggingService.LogDebug($"设置下间隙参数: {value}", WaferAligner.EventIds.EventIds.Configuration_Loaded);
                if (_implementation != null)
                {
                    return await _implementation.BottomGapSystemPara(value);
                }
                _loggingService.LogWarning("MainWindowViewModel未初始化，无法设置下间隙参数", WaferAligner.EventIds.EventIds.UI_Update_Failed);
                return false;
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"设置下间隙参数失败: {value}", WaferAligner.EventIds.EventIds.Configuration_Error);
                return false;
            }
        }

        public async Task<bool> BottomPhotoSystemParaAsync(double value)
        {
            try
            {
                _loggingService.LogDebug($"设置下拍照参数: {value}", WaferAligner.EventIds.EventIds.Configuration_Loaded);
                if (_implementation != null)
                {
                    return await _implementation.BottomPhotoSystemPara(value);
                }
                _loggingService.LogWarning("MainWindowViewModel未初始化，无法设置下拍照参数", WaferAligner.EventIds.EventIds.UI_Update_Failed);
                return false;
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"设置下拍照参数失败: {value}", WaferAligner.EventIds.EventIds.Configuration_Error);
                return false;
            }
        }

        public async Task<bool> CameraOffsetAsync(double value)
        {
            try
            {
                _loggingService.LogDebug($"设置相机偏移参数: {value}", WaferAligner.EventIds.EventIds.Configuration_Loaded);
                if (_implementation != null)
                {
                    return await _implementation.CameraOffset(value);
                }
                _loggingService.LogWarning("MainWindowViewModel未初始化，无法设置相机偏移参数", WaferAligner.EventIds.EventIds.UI_Update_Failed);
                return false;
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"设置相机偏移参数失败: {value}", WaferAligner.EventIds.EventIds.Configuration_Error);
                return false;
            }
        }
        #endregion

        #region 真空控制
        public async Task<bool> SetVacuumStateAsync(string vacuumType, bool state)
        {
            try
            {
                var variableName = vacuumType switch
                {
                    "Chuck" => $"{AxisConstants.AXIS_GVL}.hb_真空卡盘",
                    "TopWafer" => $"{AxisConstants.AXIS_GVL}.hb_真空上晶圆",
                    "BottomWafer" => $"{AxisConstants.AXIS_GVL}.hb_真空下晶圆",
                    _ => throw new ArgumentException($"未知的真空类型: {vacuumType}")
                };

                return await WritePLCVariableAsync(variableName, state);
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"设置真空状态失败: {vacuumType}={state}", WaferAligner.EventIds.EventIds.Plc_Variable_Write_Failed);
                return false;
            }
        }

        public async Task<bool> GetVacuumStateAsync(string vacuumType)
        {
            try
            {
                var variableName = vacuumType switch
                {
                    "Chuck" => $"{AxisConstants.AXIS_GVL}.hb_真空卡盘",
                    "TopWafer" => $"{AxisConstants.AXIS_GVL}.hb_真空上晶圆",
                    "BottomWafer" => $"{AxisConstants.AXIS_GVL}.hb_真空下晶圆",
                    _ => throw new ArgumentException($"未知的真空类型: {vacuumType}")
                };

                var result = await ReadPLCVariableAsync(variableName, typeof(bool));
                return result is bool boolResult && boolResult;
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"获取真空状态失败: {vacuumType}", WaferAligner.EventIds.EventIds.Plc_Variable_Read_Failed);
                return false;
            }
        }
        #endregion

        #region 位置控制
        public async Task<bool> MoveToPositionAsync(string positionName, double value)
        {
            try
            {
                var variableName = positionName switch
                {
                    "ZTakePhoto" => $"{AxisConstants.AXIS_GVL}.ZTakePhoto",
                    "TakeDownWafer" => $"{AxisConstants.AXIS_GVL}.TakeDownWafer",
                    "ZLevel" => $"{AxisConstants.AXIS_GVL}.ZLevel",
                    "TakeUpWafer" => $"{AxisConstants.AXIS_GVL}.TakeUpWafer",
                    "TakeDownWafer1" => $"{AxisConstants.AXIS_GVL}.TakeDownWafer1",
                    _ => throw new ArgumentException($"未知的位置名称: {positionName}")
                };

                var convertedValue = value * AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION;
                return await WritePLCVariableAsync(variableName, convertedValue);
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"移动到位置失败: {positionName}={value}", WaferAligner.EventIds.EventIds.Axis_Move_Error);
                return false;
            }
        }

        public async Task<double> GetPositionAsync(string positionName)
        {
            try
            {
                var variableName = positionName switch
                {
                    "ZTakePhoto" => $"{AxisConstants.AXIS_GVL}.ZTakePhoto",
                    "TakeDownWafer" => $"{AxisConstants.AXIS_GVL}.TakeDownWafer",
                    "ZLevel" => $"{AxisConstants.AXIS_GVL}.ZLevel",
                    "TakeUpWafer" => $"{AxisConstants.AXIS_GVL}.TakeUpWafer",
                    "TakeDownWafer1" => $"{AxisConstants.AXIS_GVL}.TakeDownWafer1",
                    _ => throw new ArgumentException($"未知的位置名称: {positionName}")
                };

                var result = await ReadPLCVariableAsync(variableName, typeof(long));
                if (result is long longResult)
                {
                    return longResult / AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION;
                }
                return 0;
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"获取位置失败: {positionName}", WaferAligner.EventIds. EventIds.Axis_Move_Error);
                return 0;
            }
        }
        #endregion

        #region 事件注册
        public void RegisterAction(string variableName, Action<object> action)
        {
            try
            {
                if (_disposed)
                {
                    _loggingService.LogWarning("服务已销毁，无法注册事件", WaferAligner.EventIds.EventIds.Resource_Released);
                    return;
                }

                _registeredActions.TryAdd(variableName, action);
                _implementation?.RegistryAction(variableName, action);
                _loggingService.LogDebug($"注册事件处理器: {variableName}", WaferAligner.EventIds.EventIds.Resource_Registered);
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"注册事件处理器失败: {variableName}", WaferAligner.EventIds.EventIds.Unhandled_Exception);
            }
        }

        public void RegistryAction(string variableName, Action<object> action)
        {
            // 兼容性方法，直接调用RegisterAction
            RegisterAction(variableName, action);
        }

        public void UnregisterAction(string variableName)
        {
            try
            {
                _registeredActions.TryRemove(variableName, out _);
                _implementation?.UnRegistryAction(variableName);
                _loggingService.LogDebug($"注销变量动作: {variableName}", WaferAligner.EventIds.EventIds.Resource_Released);
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"注销变量动作失败: {variableName}", WaferAligner.EventIds.EventIds.Unhandled_Exception);
            }
        }

        public void UnregisterAllActions()
        {
            try
            {
                _registeredActions.Clear();
                _loggingService.LogDebug("注销所有变量动作", WaferAligner.EventIds.EventIds.Resource_Released);
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, "注销所有变量动作失败", WaferAligner.EventIds.EventIds.Unhandled_Exception);
            }
        }
        #endregion

        #region 资源管理
        public async Task InitializeAsync()
        {
            try
            {
                _loggingService.LogInformation("初始化MainWindowViewModel服务", WaferAligner.EventIds.EventIds.Application_Started);
                
                // 连接PLC
                await ConnectPLCAsync();
                
                // 创建原始实现实例（如果需要）
                var plcInstance = _plcManager.GetPlcInstance("Main");
                if (plcInstance != null)
                {
                    // 获取其他必要的服务
                    var axisEventService = _serviceProvider.GetService(typeof(IAxisEventService)) as IAxisEventService;
                    var plcVariableService = _serviceProvider.GetService(typeof(IPlcVariableService)) as IPlcVariableService;
                    var recipeService = _serviceProvider.GetService(typeof(IRecipeService)) as IRecipeService;
                    var alignerParaService = _serviceProvider.GetService(typeof(IAlignerParaService)) as IAlignerParaService;
                    var statusUpdateService = _serviceProvider.GetService(typeof(IStatusUpdateService)) as IStatusUpdateService;
                    
                    _implementation = new MainWindowViewModel(
                        plcInstance,
                        _loggingService,
                        axisEventService,
                        plcVariableService,
                        recipeService,
                        alignerParaService,
                        statusUpdateService);
                }
                
                SystemStatus = "初始化完成";
                _loggingService.LogInformation("MainWindowViewModel服务初始化完成", WaferAligner.EventIds.EventIds.Application_Started);
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, "初始化MainWindowViewModel服务失败", WaferAligner.EventIds.EventIds.Unhandled_Exception);
                SystemStatus = $"初始化失败: {ex.Message}";
                throw;
            }
        }

        public async Task CleanupAsync()
        {
            if (_disposed) return;
            _disposed = true;

            try
            {
                _loggingService.LogInformation("清理MainWindowViewModel服务", WaferAligner.EventIds.EventIds.Application_Stopped);
                
                // 注销所有动作
                UnregisterAllActions();
                
                // 断开PLC连接
                await DisconnectPLCAsync();
                
                // 清理原始实现
                _implementation?.CleanUp();
                _implementation = null;
                
                SystemStatus = "已清理";
                _loggingService.LogInformation("MainWindowViewModel服务清理完成", WaferAligner.EventIds.EventIds.Application_Stopped);
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, "清理MainWindowViewModel服务时发生异常", WaferAligner.EventIds.EventIds.Unhandled_Exception);
            }
        }
        #endregion

        #region 气缸控制执行方法
        public async Task TopWaferExecute()
        {
            try
            {
                if (_implementation != null)
                {
                    await _implementation.TopWaferExecute();
                }
                else
                {
                    _loggingService.LogWarning("MainWindowViewModel未初始化，无法执行TopWaferExecute", WaferAligner.EventIds.EventIds.Top_Wafer_Control_Failed);
                }
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, "执行TopWaferExecute失败", WaferAligner.EventIds.EventIds.Top_Wafer_Control_Failed);
            }
        }

        public async Task TrayWaferOuterExecute()
        {
            try
            {
                if (_implementation != null)
                {
                    await _implementation.TrayWaferOuterExecute();
                }
                else
                {
                    _loggingService.LogWarning("MainWindowViewModel未初始化，无法执行TrayWaferOuterExecute", WaferAligner.EventIds.EventIds.Tray_Wafer_Outer_Control_Failed);
                }
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, "执行TrayWaferOuterExecute失败", WaferAligner.EventIds.EventIds.Tray_Wafer_Outer_Control_Failed);
            }
        }

        public async Task TrayWaferInnerExecute()
        {
            try
            {
                if (_implementation != null)
                {
                    await _implementation.TrayWaferInnerExecute();
                }
                else
                {
                    _loggingService.LogWarning("MainWindowViewModel未初始化，无法执行TrayWaferInnerExecute", WaferAligner.EventIds.EventIds.Tray_Wafer_Inner_Control_Failed);
                }
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, "执行TrayWaferInnerExecute失败", WaferAligner.EventIds.EventIds.Tray_Wafer_Inner_Control_Failed);
            }
        }

        public async Task TrayExecute()
        {
            try
            {
                if (_implementation != null)
                {
                    await _implementation.TrayExecute();
                }
                else
                {
                    _loggingService.LogWarning("MainWindowViewModel未初始化，无法执行TrayExecute", WaferAligner.EventIds.EventIds.Tray_Control_Failed);
                }
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, "执行TrayExecute失败", WaferAligner.EventIds.EventIds.Tray_Control_Failed);
            }
        }

        public async Task ChuckLockExecute()
        {
            try
            {
                if (_implementation != null)
                {
                    await _implementation.ChuckLockExecute();
                }
                else
                {
                    _loggingService.LogWarning("MainWindowViewModel未初始化，无法执行ChuckLockExecute", WaferAligner.EventIds.EventIds.Chuck_Lock_Control_Failed);
                }
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, "执行ChuckLockExecute失败", WaferAligner.EventIds.EventIds.Chuck_Lock_Control_Failed);
            }
        }

        public async Task HorizontalAdjustLockExecute()
        {
            try
            {
                if (_implementation != null)
                {
                    await _implementation.HorizontalAdjustLockExecute();
                }
                else
                {
                    _loggingService.LogWarning("MainWindowViewModel未初始化，无法执行HorizontalAdjustLockExecute", WaferAligner.EventIds.EventIds.Horizontal_Adjust_Control_Failed);
                }
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, "执行HorizontalAdjustLockExecute失败", WaferAligner.EventIds.EventIds.Horizontal_Adjust_Control_Failed);
            }
        }
        #endregion

        #region 通信方法
        public async Task CalibrateXYR()
        {
            try
            {
                if (_implementation != null)
                {
                    await _implementation.CalibrateXYR();
                }
                else
                {
                    _loggingService.LogWarning("MainWindowViewModel未初始化，无法执行CalibrateXYR", WaferAligner.EventIds.EventIds.Xyr_Initial_Location_Failed);
                }
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, "执行CalibrateXYR失败", WaferAligner.EventIds.EventIds.Xyr_Initial_Location_Failed);
            }
        }

        public async Task SendMsg(int P1, int P2, int P3, int P4, int P5)
        {
            try
            {
                if (_implementation != null)
                {
                    await _implementation.SendMsg(P1, P2, P3, P4, P5);
                }
                else
                {
                    _loggingService.LogWarning($"MainWindowViewModel未初始化，无法发送消息: {P1},{P2},{P3},{P4},{P5}", WaferAligner.EventIds.EventIds.UI_Update_Failed);
                }
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"发送消息失败: {P1},{P2},{P3},{P4},{P5}", WaferAligner.EventIds.EventIds.UI_Update_Failed);
            }
        }

        public async Task<string> ReceiveMsg()
        {
            try
            {
                if (_implementation != null)
                {
                    return await _implementation.ReceiveMsg();
                }
                else
                {
                    _loggingService.LogWarning("MainWindowViewModel未初始化，无法接收消息", WaferAligner.EventIds.EventIds.UI_Update_Failed);
                    return string.Empty;
                }
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, "接收消息失败", WaferAligner.EventIds.EventIds.UI_Update_Failed);
                return string.Empty;
            }
        }

        public async Task SetXYRPos(float P1, float P2, float P3)
        {
            try
            {
                if (_implementation != null)
                {
                    await _implementation.SetXYRPos(P1, P2, P3);
                }
                else
                {
                    _loggingService.LogWarning($"MainWindowViewModel未初始化，无法设置XYR位置: {P1},{P2},{P3}", WaferAligner.EventIds.EventIds.Xyr_Position_Update_Error);
                }
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"设置XYR位置失败: {P1},{P2},{P3}", WaferAligner.EventIds.EventIds.Xyr_Position_Update_Error);
            }
        }

        public async Task<string> GetXYRPos()
        {
            try
            {
                if (_implementation != null)
                {
                    return await _implementation.GetXYRPos();
                }
                else
                {
                    _loggingService.LogWarning("MainWindowViewModel未初始化，无法获取XYR位置", WaferAligner.EventIds.EventIds.Xyr_Position_Update_Error);
                    return string.Empty;
                }
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, "获取XYR位置失败", WaferAligner.EventIds.EventIds.Xyr_Position_Update_Error);
                return string.Empty;
            }
        }

        public async Task SetXYRPosAsync(float x, float y, float r)
        {
            try
            {
                _loggingService.LogDebug($"设置XYR位置: X={x}, Y={y}, R={r}", WaferAligner.EventIds.EventIds.Axis_Move_Started);
                if (_implementation != null)
                {
                    await _implementation.SetXYRPos(x, y, r);
                }
                else
                {
                    _loggingService.LogWarning($"MainWindowViewModel未初始化，无法设置XYR位置: X={x}, Y={y}, R={r}", WaferAligner.EventIds.EventIds.Xyr_Position_Update_Error);
                }
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"设置XYR位置失败: X={x}, Y={y}, R={r}", WaferAligner.EventIds.EventIds.Xyr_Position_Update_Error);
                throw;
            }
        }

        /// <summary>
        /// 控制XYR轴运动到指定位置
        /// </summary>
        /// <param name="XTarget">X轴目标位置</param>
        /// <param name="YTarget">Y轴目标位置</param>
        /// <param name="RTarget">R轴目标位置</param>
        /// <returns>操作是否成功</returns>
        public async Task<bool> XYRMotion(double XTarget, double YTarget, double RTarget)
        {
            try
            {
                _loggingService.LogDebug($"XYR轴运动到位置: X={XTarget}, Y={YTarget}, R={RTarget}", WaferAligner.EventIds.EventIds.Axis_Move_Started);
                if (_implementation != null)
                {
                    return await _implementation.XYRMotion(XTarget, YTarget, RTarget);
                }
                else
                {
                    _loggingService.LogWarning($"MainWindowViewModel未初始化，无法执行XYR轴运动: X={XTarget}, Y={YTarget}, R={RTarget}", WaferAligner.EventIds.EventIds.Xyr_Motion_To_Initial_Location_Failed);
                    return false;
                }
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"XYR轴运动到位置失败: X={XTarget}, Y={YTarget}, R={RTarget}", WaferAligner.EventIds.EventIds.Xyr_Motion_To_Initial_Location_Failed);
                return false;
            }
        }

        /// <summary>
        /// 上晶圆运动至调平位
        /// </summary>
        /// <param name="IsTopHorizontalAdjust">是否进行调平</param>
        /// <param name="IsTopHorizontalPhoto">是否进行调平拍照</param>
        /// <returns>操作是否成功</returns>
        public async Task<bool> TopWaferUp(bool IsTopHorizontalAdjust, bool IsTopHorizontalPhoto)
        {
            try
            {
                _loggingService.LogDebug($"上晶圆运动至调平位: 调平={IsTopHorizontalAdjust}, 拍照={IsTopHorizontalPhoto}", WaferAligner.EventIds.EventIds.Top_Wafer_Move_Started);
                if (_implementation != null)
                {
                    return await _implementation.TopWaferUp(IsTopHorizontalAdjust, IsTopHorizontalPhoto);
                }
                else
                {
                    _loggingService.LogWarning($"MainWindowViewModel未初始化，无法执行上晶圆运动至调平位", WaferAligner.EventIds.EventIds.Top_Wafer_Move_Failed);
                    return false;
                }
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, "上晶圆运动至调平位失败", WaferAligner.EventIds.EventIds.Top_Wafer_Move_Failed);
                return false;
            }
        }

        /// <summary>
        /// 上晶圆取片
        /// </summary>
        /// <returns>操作是否成功</returns>
        public async Task<bool> TopWaferTakeUp()
        {
            try
            {
                _loggingService.LogDebug("上晶圆取片开始", WaferAligner.EventIds.EventIds.Top_Wafer_Take_Up_Started);
                if (_implementation != null)
                {
                    return await _implementation.TopWaferTakeUp();
                }
                else
                {
                    _loggingService.LogWarning("MainWindowViewModel未初始化，无法执行上晶圆取片", WaferAligner.EventIds.EventIds.Top_Wafer_Take_Up_Failed);
                    return false;
                }
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, "上晶圆取片失败", WaferAligner.EventIds.EventIds.Top_Wafer_Take_Up_Failed);
                return false;
            }
        }

        /// <summary>
        /// XYR轴确认
        /// </summary>
        /// <returns>操作是否成功</returns>
        public async Task<bool> TakeXYRConfirmed()
        {
            try
            {
                _loggingService.LogDebug("XYR轴确认开始", WaferAligner.EventIds.EventIds.Xyr_Confirm_Started);
                if (_implementation != null)
                {
                    return await _implementation.TakeXYRConfirmed();
                }
                else
                {
                    _loggingService.LogWarning("MainWindowViewModel未初始化，无法执行XYR轴确认", WaferAligner.EventIds.EventIds.Xyr_Confirm_Failed);
                    return false;
                }
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, "XYR轴确认失败", WaferAligner.EventIds.EventIds.Xyr_Confirm_Failed);
                return false;
            }
        }

        /// <summary>
        /// 下晶圆运动
        /// </summary>
        /// <param name="WaferSeries">晶圆系列</param>
        /// <returns>操作是否成功</returns>
        public async Task<bool> BottomWaferDown(uint WaferSeries)
        {
            try
            {
                _loggingService.LogDebug($"下晶圆运动开始: 系列={WaferSeries}", WaferAligner.EventIds.EventIds.Bottom_Wafer_Move_Started);
                if (_implementation != null)
                {
                    return await _implementation.BottomWaferDown(WaferSeries);
                }
                else
                {
                    _loggingService.LogWarning($"MainWindowViewModel未初始化，无法执行下晶圆运动: 系列={WaferSeries}", WaferAligner.EventIds.EventIds.Bottom_Wafer_Move_Failed);
                    return false;
                }
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"下晶圆运动失败: 系列={WaferSeries}", WaferAligner.EventIds.EventIds.Bottom_Wafer_Move_Failed);
                return false;
            }
        }

        /// <summary>
        /// 下晶圆取片
        /// </summary>
        /// <param name="WaferSeries">晶圆系列</param>
        /// <returns>操作是否成功</returns>
        public async Task<bool> BottomWaferTakeDown(uint WaferSeries)
        {
            try
            {
                _loggingService.LogDebug($"下晶圆取片开始: 系列={WaferSeries}", WaferAligner.EventIds.EventIds.Bottom_Wafer_Take_Down_Started);
                if (_implementation != null)
                {
                    return await _implementation.BottomWaferTakeDown(WaferSeries);
                }
                else
                {
                    _loggingService.LogWarning($"MainWindowViewModel未初始化，无法执行下晶圆取片: 系列={WaferSeries}", WaferAligner.EventIds.EventIds.Bottom_Wafer_Take_Down_Failed);
                    return false;
                }
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"下晶圆取片失败: 系列={WaferSeries}", WaferAligner.EventIds.EventIds.Bottom_Wafer_Take_Down_Failed);
                return false;
            }
        }

        /// <summary>
        /// 下晶圆确认
        /// </summary>
        /// <returns>操作是否成功</returns>
        public async Task<bool> TakeDownConfirmed()
        {
            try
            {
                _loggingService.LogDebug("下晶圆确认开始", WaferAligner.EventIds.EventIds.Take_Down_Confirm_Started);
                if (_implementation != null)
                {
                    return await _implementation.TakeDownConfirmed();
                }
                else
                {
                    _loggingService.LogWarning("MainWindowViewModel未初始化，无法执行下晶圆确认", WaferAligner.EventIds.EventIds.Take_Down_Confirm_Failed);
                    return false;
                }
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, "下晶圆确认失败", WaferAligner.EventIds.EventIds.Take_Down_Confirm_Failed);
                return false;
            }
        }

        /// <summary>
        /// 变量确认
        /// </summary>
        /// <param name="FinshVariable">完成变量名</param>
        /// <returns>操作是否成功</returns>
        public async Task<bool> VariableConfirm(string FinshVariable)
        {
            try
            {
                _loggingService.LogDebug($"变量确认开始: {FinshVariable}", WaferAligner.EventIds.EventIds.Variable_Confirm_Started);
                if (_implementation != null)
                {
                    return await _implementation.VariableConfirm(FinshVariable);
                }
                else
                {
                    _loggingService.LogWarning($"MainWindowViewModel未初始化，无法执行变量确认: {FinshVariable}", WaferAligner.EventIds.EventIds.Variable_Confirm_Failed);
                    return false;
                }
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"变量确认失败: {FinshVariable}", WaferAligner.EventIds.EventIds.Variable_Confirm_Failed);
                return false;
            }
        }

        /// <summary>
        /// 晶圆Z轴初始化
        /// </summary>
        /// <returns>操作是否成功</returns>
        public async Task<bool> WaferInitialZ()
        {
            try
            {
                _loggingService.LogDebug("晶圆Z轴初始化开始", WaferAligner.EventIds.EventIds.Z_Axis_Initial_Started);
                if (_implementation != null)
                {
                    return await _implementation.WaferInitialZ();
                }
                else
                {
                    _loggingService.LogWarning("MainWindowViewModel未初始化，无法执行晶圆Z轴初始化", WaferAligner.EventIds.EventIds.Z_Axis_Initial_Location_Failed);
                    return false;
                }
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, "晶圆Z轴初始化失败", WaferAligner.EventIds.EventIds.Z_Axis_Initial_Location_Failed);
                return false;
            }
        }

        /// <summary>
        /// 目标拍照
        /// </summary>
        /// <returns>操作任务</returns>
        public async Task TargetTakePhoto()
        {
            try
            {
                _loggingService.LogDebug("目标拍照开始", WaferAligner.EventIds.EventIds.Target_Take_Photo_Started);
                if (_implementation != null)
                {
                    await _implementation.TargetTakePhoto();
                }
                else
                {
                    _loggingService.LogWarning("MainWindowViewModel未初始化，无法执行目标拍照", WaferAligner.EventIds.EventIds.Target_Take_Photo_Failed);
                }
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, "目标拍照失败", WaferAligner.EventIds.EventIds.Target_Take_Photo_Failed);
            }
        }
        #endregion

        #region 初始化方法
        public async Task<bool> Init()
        {
            try
            {
                if (_implementation != null)
                {
                    return await _implementation.Init();
                }
                else
                {
                    _loggingService.LogWarning("MainWindowViewModel未初始化，无法执行Init", WaferAligner.EventIds.EventIds.UI_Update_Failed);
                    return false;
                }
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, "执行Init失败", WaferAligner.EventIds.EventIds.UI_Update_Failed);
                return false;
            }
        }
        #endregion
    }
} 