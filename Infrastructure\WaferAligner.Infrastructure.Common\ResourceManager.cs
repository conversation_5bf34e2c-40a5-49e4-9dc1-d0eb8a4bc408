using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Timers;
using WaferAligner.Services.Abstractions;
using WaferAligner.Services.Extensions;
namespace WaferAligner.Infrastructure.Common
{
    /// <summary>
    /// 应用程序资源生命周期管理器
    /// 提供统一的资源注册、清理和生命周期管理
    /// </summary>
    public class ResourceManager : IDisposable
    {
        private readonly ILoggingService _loggingService;
        private readonly ConcurrentDictionary<string, IDisposable> _resources;
        private readonly ConcurrentDictionary<string, System.Timers.Timer> _timers;
        // 修改BackgroundWorker字段，添加过期标记
        [Obsolete("BackgroundWorker已全部迁移至Task-based异步模式，此成员将在v4.0中移除", false)]
        private readonly ConcurrentDictionary<string, BackgroundWorker> _backgroundWorkers;
        // 添加CancellationTokenSource字典
        private readonly ConcurrentDictionary<string, CancellationTokenSource> _cancellationTokenSources = new();
        // 添加CancellationTokenSourceWrapper类
        private class CancellationTokenSourceWrapper : IDisposable
        {
            private readonly CancellationTokenSource _cts;
            
            public CancellationTokenSourceWrapper(CancellationTokenSource cts)
            {
                _cts = cts;
            }
            
            public void Dispose()
            {
                try
                {
                    if (_cts != null && !_cts.IsCancellationRequested)
                    {
                        _cts.Cancel();
                    }
                    _cts?.Dispose();
                }
                catch (ObjectDisposedException)
                {
                    // 已经被释放，忽略
                }
                catch (Exception)
                {
                    // 释放过程中的其他异常也忽略
                }
            }
        }
        private readonly List<Func<Task>> _customCleanupActions;
        private readonly object _lockObject = new object();
        private volatile bool _disposed = false;
        private volatile bool _isShuttingDown = false;

        public ResourceManager(ILoggingService loggingService = null)
        {
            _loggingService = loggingService;
            _resources = new ConcurrentDictionary<string, IDisposable>();
            _timers = new ConcurrentDictionary<string, System.Timers.Timer>();
            _backgroundWorkers = new ConcurrentDictionary<string, BackgroundWorker>();
            _customCleanupActions = new List<Func<Task>>();
        }

        /// <summary>
        /// 注册需要管理的资源
        /// </summary>
        public void RegisterResource(string name, IDisposable resource)
        {
            if (_disposed || _isShuttingDown) return;
            
            _resources.TryAdd(name, resource);
            _loggingService?.LogDebug($"已注册资源: {name}", WaferAligner.EventIds.EventIds.Resource_Registered);
        }

        /// <summary>
        /// 注册定时器
        /// </summary>
        public void RegisterTimer(string name, System.Timers.Timer timer)
        {
            if (_disposed || _isShuttingDown) return;
            
            _timers.TryAdd(name, timer);
            RegisterResource($"Timer_{name}", timer);
            _loggingService?.LogDebug($"已注册定时器: {name}", WaferAligner.EventIds.EventIds.Resource_Registered);
        }

        /// <summary>
        /// 批量注册资源
        /// </summary>
        /// <param name="baseKey">基础键名，将自动添加索引后缀</param>
        /// <param name="resources">资源集合</param>
        public void RegisterResources<T>(string baseKey, IEnumerable<T> resources) where T : IDisposable
        {
            if (_disposed || _isShuttingDown || resources == null) return;
            
            int index = 0;
            foreach (var resource in resources)
            {
                RegisterResource($"{baseKey}_{index++}", resource);
            }
            
            _loggingService?.LogDebug($"已批量注册资源: {baseKey}, 数量: {index}", WaferAligner.EventIds.EventIds.Resource_Registered);
        }
        
        /// <summary>
        /// 批量注册定时器
        /// </summary>
        /// <param name="baseKey">基础键名，将自动添加索引后缀</param>
        /// <param name="timers">定时器集合</param>
        public void RegisterTimers(string baseKey, IEnumerable<System.Timers.Timer> timers)
        {
            if (_disposed || _isShuttingDown || timers == null) return;
            
            int index = 0;
            foreach (var timer in timers)
            {
                RegisterTimer($"{baseKey}_{index++}", timer);
            }
            
            _loggingService?.LogDebug($"已批量注册定时器: {baseKey}, 数量: {index}", WaferAligner.EventIds.EventIds.Resource_Registered);
        }
        
        /// <summary>
        /// 批量注册取消令牌源
        /// </summary>
        /// <param name="baseKey">基础键名，将自动添加索引后缀</param>
        /// <param name="cancellationTokenSources">取消令牌源集合</param>
        public void RegisterCancellationTokenSources(string baseKey, IEnumerable<CancellationTokenSource> cancellationTokenSources)
        {
            if (_disposed || _isShuttingDown || cancellationTokenSources == null) return;
            
            int index = 0;
            foreach (var cts in cancellationTokenSources)
            {
                RegisterCancellationTokenSource($"{baseKey}_{index++}", cts);
            }
            
            _loggingService?.LogDebug($"已批量注册取消令牌源: {baseKey}, 数量: {index}", WaferAligner.EventIds.EventIds.Resource_Registered);
        }

        /// <summary>
        /// 注册后台工作线程
        /// </summary>
        [Obsolete("BackgroundWorker已全部迁移至Task-based异步模式，请使用RegisterCustomCleanup注册Task清理操作", false)]
        public void RegisterBackgroundWorker(string name, BackgroundWorker worker)
        {
            if (_disposed || _isShuttingDown) return;
            
            _loggingService?.LogWarning($"使用已过期的RegisterBackgroundWorker方法: {name}", 
                WaferAligner.EventIds.EventIds.Deprecated_Api_Usage);
            _backgroundWorkers.TryAdd(name, worker);
            RegisterResource($"BackgroundWorker_{name}", worker);
            _loggingService?.LogDebug($"已注册后台工作线程: {name}", WaferAligner.EventIds.EventIds.Resource_Registered);
        }

        /// <summary>
        /// 注册Windows Forms定时器（System.Windows.Forms.Timer）
        /// </summary>
        public void RegisterWinFormsTimer(string name, System.Windows.Forms.Timer timer)
        {
            if (_disposed || _isShuttingDown) return;
            
            // Windows Forms Timer实现了IDisposable，可以直接注册
            RegisterResource($"WinFormsTimer_{name}", timer);
            _loggingService?.LogDebug($"已注册Windows Forms定时器: {name}", WaferAligner.EventIds.EventIds.Resource_Registered);
        }

        /// <summary>
        /// 注册自定义清理操作
        /// </summary>
        public void RegisterCustomCleanup(Func<Task> cleanupAction)
        {
            if (_disposed || _isShuttingDown) return;
            
            lock (_lockObject)
            {
                _customCleanupActions.Add(cleanupAction);
            }
        }

        /// <summary>
        /// 注册取消令牌源
        /// </summary>
        public void RegisterCancellationTokenSource(string name, CancellationTokenSource cts)
        {
            if (_disposed || _isShuttingDown) return;
            
            _cancellationTokenSources.TryAdd(name, cts);
            RegisterResource($"CTS_{name}", new CancellationTokenSourceWrapper(cts));
            _loggingService?.LogDebug($"已注册取消令牌源: {name}", WaferAligner.EventIds.EventIds.Resource_Registered);
        }

        /// <summary>
        /// 开始关闭流程
        /// </summary>
        public async Task BeginShutdownAsync(int timeoutMs = 5000)
        {
            if (_disposed || _isShuttingDown) return;
            
            _isShuttingDown = true;
            _loggingService?.LogInformation("开始资源清理流程", WaferAligner.EventIds.EventIds.Resource_Released);

            using (var cts = new CancellationTokenSource(timeoutMs))
            {
                try
                {
                    await CleanupAllResourcesAsync(cts.Token);
                    _loggingService?.LogInformation("资源清理完成", WaferAligner.EventIds.EventIds.Resource_Released);
                }
                catch (OperationCanceledException)
                {
                    _loggingService?.LogWarning($"资源清理超时 ({timeoutMs}ms)，强制继续", WaferAligner.EventIds.EventIds.Resource_Released);
                }
                catch (Exception ex)
                {
                    _loggingService?.LogError(ex, "资源清理过程中发生错误", WaferAligner.EventIds.EventIds.Resource_Released);
                }
            }
        }

        /// <summary>
        /// 清理所有资源
        /// </summary>
        private async Task CleanupAllResourcesAsync(CancellationToken cancellationToken)
        {
            var tasks = new List<Task>();

            // 1. 停止所有定时器
            tasks.Add(Task.Run(() => StopAllTimers(), cancellationToken));

            // 2. 取消所有取消令牌源（新增）
            tasks.Add(Task.Run(() => CancelAllCancellationTokenSources(), cancellationToken));
            
            // 3. 取消所有后台工作线程（已过期）
            tasks.Add(Task.Run(() => CancelAllBackgroundWorkers(), cancellationToken));

            // 4. 执行自定义清理操作
            tasks.Add(Task.Run(() => ExecuteCustomCleanupActions(), cancellationToken));

            // 5. 释放所有其他资源
            tasks.Add(Task.Run(() => DisposeAllResources(), cancellationToken));

            await Task.WhenAll(tasks);
        }

        /// <summary>
        /// 停止所有定时器
        /// </summary>
        private void StopAllTimers()
        {
            foreach (var kvp in _timers)
            {
                try
                {
                    kvp.Value?.Stop();
                    _loggingService?.LogDebug($"已停止定时器: {kvp.Key}", WaferAligner.EventIds.EventIds.Resource_Released);
                }
                catch (Exception ex)
                {
                    _loggingService?.LogWarning(ex, $"停止定时器 {kvp.Key} 时发生错误", WaferAligner.EventIds.EventIds.Timer_Error);
                }
            }
        }

        /// <summary>
        /// 取消所有后台工作线程
        /// </summary>
        [Obsolete("BackgroundWorker已全部迁移至Task-based异步模式，此方法将在v4.0中移除", false)]
        private void CancelAllBackgroundWorkers()
        {
            foreach (var kvp in _backgroundWorkers)
            {
                try
                {
                    var worker = kvp.Value;
                    if (worker?.IsBusy == true)
                    {
                        worker.CancelAsync();
                        _loggingService?.LogDebug($"已取消后台工作线程: {kvp.Key}", WaferAligner.EventIds.EventIds.Resource_Released);
                    }
                }
                catch (Exception ex)
                {
                    _loggingService?.LogWarning(ex, $"取消后台工作线程 {kvp.Key} 时发生错误", WaferAligner.EventIds.EventIds.Thread_Aborted);
                }
            }
        }

        /// <summary>
        /// 取消所有取消令牌源
        /// </summary>
        private void CancelAllCancellationTokenSources()
        {
            foreach (var kvp in _cancellationTokenSources)
            {
                try
                {
                    var cts = kvp.Value;
                    if (cts != null && !cts.IsCancellationRequested)
                    {
                        cts.Cancel();
                        _loggingService?.LogDebug($"已取消令牌源: {kvp.Key}", WaferAligner.EventIds.EventIds.Resource_Released);
                    }
                }
                catch (Exception ex)
                {
                    _loggingService?.LogWarning(ex, $"取消令牌源 {kvp.Key} 时发生错误", WaferAligner.EventIds.EventIds.Unhandled_Exception);
                }
            }
        }

        /// <summary>
        /// 执行自定义清理操作
        /// </summary>
        private async Task ExecuteCustomCleanupActions()
        {
            var actions = new List<Func<Task>>();
            lock (_lockObject)
            {
                actions.AddRange(_customCleanupActions);
            }

            foreach (var action in actions)
            {
                try
                {
                    await action();
                    _loggingService?.LogDebug("已执行自定义清理操作", WaferAligner.EventIds.EventIds.Resource_Released);
                }
                catch (Exception ex)
                {
                    _loggingService?.LogWarning(ex, "执行自定义清理操作时发生错误", WaferAligner.EventIds.EventIds.Unhandled_Exception);
                }
            }
        }

        /// <summary>
        /// 释放所有资源
        /// </summary>
        private void DisposeAllResources()
        {
            foreach (var kvp in _resources)
            {
                try
                {
                    kvp.Value?.Dispose();
                    _loggingService?.LogDebug($"已释放资源: {kvp.Key}", WaferAligner.EventIds.EventIds.Resource_Released);
                }
                catch (Exception ex)
                {
                    _loggingService?.LogWarning(ex, $"释放资源 {kvp.Key} 时发生错误", WaferAligner.EventIds.EventIds.Unhandled_Exception);
                }
            }
        }

        /// <summary>
        /// 移除指定资源
        /// </summary>
        public bool RemoveResource(string name)
        {
            if (_resources.TryRemove(name, out var resource))
            {
                try
                {
                    resource?.Dispose();
                    _loggingService?.LogDebug($"已移除并释放资源: {name}", WaferAligner.EventIds.EventIds.Resource_Released);
                    return true;
                }
                catch (Exception ex)
                {
                    _loggingService?.LogWarning(ex, $"移除资源 {name} 时发生错误", WaferAligner.EventIds.EventIds.Unhandled_Exception);
                }
            }
            return false;
        }

        /// <summary>
        /// 获取资源统计信息
        /// </summary>
        public ResourceStatistics GetStatistics()
        {
            return new ResourceStatistics
            {
                TotalResources = _resources.Count,
                TotalTimers = _timers.Count,
                TotalBackgroundWorkers = _backgroundWorkers.Count,
                ActiveBackgroundWorkers = _backgroundWorkers.Values.Count(w => w?.IsBusy == true),
                TotalCancellationTokenSources = _cancellationTokenSources.Count,
                ActiveCancellationTokenSources = _cancellationTokenSources.Values.Count(cts => cts != null && !cts.IsCancellationRequested),
                CustomCleanupActions = _customCleanupActions.Count,
                IsShuttingDown = _isShuttingDown,
                IsDisposed = _disposed
            };
        }

        /// <summary>
        /// 记录资源状态到日志
        /// </summary>
        /// <param name="loggingService">日志服务，如果为null则使用内部日志服务</param>
        public void LogResourceStatus(ILoggingService loggingService = null)
        {
            var logger = loggingService ?? _loggingService;
            if (logger == null) return;
            
            var stats = GetStatistics();
            
            logger.LogInformation($"当前资源数: {stats.TotalResources}", WaferAligner.EventIds.EventIds.Resource_Status);
            
            // 分类统计
            logger.LogInformation(
                $"资源统计: 定时器({stats.TotalTimers}), " +
                $"取消令牌({stats.TotalCancellationTokenSources}, 活动={stats.ActiveCancellationTokenSources}), " +
                $"后台线程({stats.TotalBackgroundWorkers}, 活动={stats.ActiveBackgroundWorkers}), " +
                $"自定义清理({stats.CustomCleanupActions})",
                WaferAligner.EventIds.EventIds.Resource_Status);
                
            // 如果有大量资源，记录详细信息
            if (stats.TotalResources > 100)
            {
                logger.LogWarning($"资源数量过多 ({stats.TotalResources})，可能存在资源泄漏", WaferAligner.EventIds.EventIds.Resource_Status);
            }
        }
        
        /// <summary>
        /// 移除指定前缀的所有资源
        /// </summary>
        /// <param name="keyPrefix">键前缀</param>
        /// <returns>移除的资源数量</returns>
        public int RemoveResourcesByPrefix(string keyPrefix)
        {
            if (string.IsNullOrEmpty(keyPrefix)) return 0;
            
            int count = 0;
            var keysToRemove = _resources.Keys.Where(k => k.StartsWith(keyPrefix)).ToList();
            
            foreach (var key in keysToRemove)
            {
                if (RemoveResource(key))
                {
                    count++;
                }
            }
            
            _loggingService?.LogDebug($"已批量移除前缀为 {keyPrefix} 的资源: {count}个", WaferAligner.EventIds.EventIds.Resource_Released);
            return count;
        }

        public void Dispose()
        {
            if (_disposed) return;

            _disposed = true;
            
            try
            {
                // 同步清理，用于Dispose调用
                BeginShutdownAsync(3000).Wait();
            }
            catch (Exception ex)
            {
                _loggingService?.LogError(ex, "Dispose过程中发生错误", WaferAligner.EventIds.EventIds.Unhandled_Exception);
            }

            _resources.Clear();
            _timers.Clear();
            _backgroundWorkers.Clear();
            _customCleanupActions.Clear();
        }
    }

    /// <summary>
    /// 资源统计信息
    /// </summary>
    public class ResourceStatistics
    {
        public int TotalResources { get; set; }
        public int TotalTimers { get; set; }
        public int TotalBackgroundWorkers { get; set; }
        public int ActiveBackgroundWorkers { get; set; }
        // 新增属性
        public int TotalCancellationTokenSources { get; set; }
        public int ActiveCancellationTokenSources { get; set; }
        public int CustomCleanupActions { get; set; }
        public bool IsShuttingDown { get; set; }
        public bool IsDisposed { get; set; }

        public override string ToString()
        {
            return $"资源统计: 总资源={TotalResources}, 定时器={TotalTimers}, " +
                   $"后台线程={TotalBackgroundWorkers}(活动={ActiveBackgroundWorkers}), " +
                   $"取消令牌源={TotalCancellationTokenSources}(活动={ActiveCancellationTokenSources}), " +
                   $"自定义清理={CustomCleanupActions}, 关闭中={IsShuttingDown}, 已释放={IsDisposed}";
        }
    }
} 