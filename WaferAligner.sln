﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.7.34031.279
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "WaferAligner", "WaferAligner\WaferAligner.csproj", "{DAA8232C-700A-4341-9978-38D02DE3FEE4}"
EndProject

Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "WaferAligner.Communication.Inovance", "src\Communication\WaferAligner.Communication.Inovance\WaferAligner.Communication.Inovance.csproj", "{22C75AED-545D-4772-8343-7434C9E6988E}"
EndProject
Project("{54435603-DBB4-11D2-8724-00A0C9A8B90C}") = "WaferAlignerSetup", "WaferAlignerSetup\WaferAlignerSetup.vdproj", "{446F315C-6511-B153-AB53-E008E32F50FC}"
EndProject


Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "WaferAligner.Services.UserManagement", "src\Services\WaferAligner.Services.UserManagement\WaferAligner.Services.UserManagement.csproj", "{81242C68-8A2A-4B4F-B795-F1491B90726A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "WaferAligner.Services.Logging", "src\Services\WaferAligner.Services.Logging\WaferAligner.Services.Logging.csproj", "{B8F7E2A1-9C3D-4E5F-A8B6-1234567890AB}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "WaferAligner.EventIds", "src\Core\WaferAligner.EventIds\WaferAligner.EventIds.csproj", "{20DAEF9F-DA99-46D4-BA81-A032301F0233}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "WaferAligner.Infrastructure.Common", "src\Infrastructure\WaferAligner.Infrastructure.Common\WaferAligner.Infrastructure.Common.csproj", "{450887F6-3813-44A4-B3DD-DF173ED4D8D8}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{827E0CD3-B72D-47B6-A68D-7590B98EB39B}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Core", "Core", "{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Services", "Services", "{B2C3D4E5-F6G7-8901-BCDE-F23456789012}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Communication", "Communication", "{42AA32C4-1DB0-2C46-8A19-9CF10807ECC8}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Infrastructure", "Infrastructure", "{D1E2F3G4-H5I6-7890-JKLM-NO1234567890}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "WaferAligner.Communication.Serial", "src\Communication\WaferAligner.Communication.Serial\WaferAligner.Communication.Serial.csproj", "{C053D5EB-E089-497F-BB4A-3D7E80DDEA32}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|ARM = Debug|ARM
		Debug|ARM64 = Debug|ARM64
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|ARM = Release|ARM
		Release|ARM64 = Release|ARM64
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{DAA8232C-700A-4341-9978-38D02DE3FEE4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DAA8232C-700A-4341-9978-38D02DE3FEE4}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DAA8232C-700A-4341-9978-38D02DE3FEE4}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{DAA8232C-700A-4341-9978-38D02DE3FEE4}.Debug|ARM.Build.0 = Debug|Any CPU
		{DAA8232C-700A-4341-9978-38D02DE3FEE4}.Debug|ARM64.ActiveCfg = Debug|Any CPU
		{DAA8232C-700A-4341-9978-38D02DE3FEE4}.Debug|ARM64.Build.0 = Debug|Any CPU
		{DAA8232C-700A-4341-9978-38D02DE3FEE4}.Debug|x64.ActiveCfg = Debug|Any CPU
		{DAA8232C-700A-4341-9978-38D02DE3FEE4}.Debug|x64.Build.0 = Debug|Any CPU
		{DAA8232C-700A-4341-9978-38D02DE3FEE4}.Debug|x86.ActiveCfg = Debug|x86
		{DAA8232C-700A-4341-9978-38D02DE3FEE4}.Debug|x86.Build.0 = Debug|x86
		{DAA8232C-700A-4341-9978-38D02DE3FEE4}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DAA8232C-700A-4341-9978-38D02DE3FEE4}.Release|Any CPU.Build.0 = Release|Any CPU
		{DAA8232C-700A-4341-9978-38D02DE3FEE4}.Release|ARM.ActiveCfg = Release|Any CPU
		{DAA8232C-700A-4341-9978-38D02DE3FEE4}.Release|ARM.Build.0 = Release|Any CPU
		{DAA8232C-700A-4341-9978-38D02DE3FEE4}.Release|ARM64.ActiveCfg = Release|Any CPU
		{DAA8232C-700A-4341-9978-38D02DE3FEE4}.Release|ARM64.Build.0 = Release|Any CPU
		{DAA8232C-700A-4341-9978-38D02DE3FEE4}.Release|x64.ActiveCfg = Release|Any CPU
		{DAA8232C-700A-4341-9978-38D02DE3FEE4}.Release|x64.Build.0 = Release|Any CPU
		{DAA8232C-700A-4341-9978-38D02DE3FEE4}.Release|x86.ActiveCfg = Release|x86
		{DAA8232C-700A-4341-9978-38D02DE3FEE4}.Release|x86.Build.0 = Release|x86

		{22C75AED-545D-4772-8343-7434C9E6988E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{22C75AED-545D-4772-8343-7434C9E6988E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{22C75AED-545D-4772-8343-7434C9E6988E}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{22C75AED-545D-4772-8343-7434C9E6988E}.Debug|ARM.Build.0 = Debug|Any CPU
		{22C75AED-545D-4772-8343-7434C9E6988E}.Debug|ARM64.ActiveCfg = Debug|Any CPU
		{22C75AED-545D-4772-8343-7434C9E6988E}.Debug|ARM64.Build.0 = Debug|Any CPU
		{22C75AED-545D-4772-8343-7434C9E6988E}.Debug|x64.ActiveCfg = Debug|x64
		{22C75AED-545D-4772-8343-7434C9E6988E}.Debug|x64.Build.0 = Debug|x64
		{22C75AED-545D-4772-8343-7434C9E6988E}.Debug|x86.ActiveCfg = Debug|Any CPU
		{22C75AED-545D-4772-8343-7434C9E6988E}.Debug|x86.Build.0 = Debug|Any CPU
		{22C75AED-545D-4772-8343-7434C9E6988E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{22C75AED-545D-4772-8343-7434C9E6988E}.Release|Any CPU.Build.0 = Release|Any CPU
		{22C75AED-545D-4772-8343-7434C9E6988E}.Release|ARM.ActiveCfg = Release|Any CPU
		{22C75AED-545D-4772-8343-7434C9E6988E}.Release|ARM.Build.0 = Release|Any CPU
		{22C75AED-545D-4772-8343-7434C9E6988E}.Release|ARM64.ActiveCfg = Release|Any CPU
		{22C75AED-545D-4772-8343-7434C9E6988E}.Release|ARM64.Build.0 = Release|Any CPU
		{22C75AED-545D-4772-8343-7434C9E6988E}.Release|x64.ActiveCfg = Release|x64
		{22C75AED-545D-4772-8343-7434C9E6988E}.Release|x64.Build.0 = Release|x64
		{22C75AED-545D-4772-8343-7434C9E6988E}.Release|x86.ActiveCfg = Release|Any CPU
		{22C75AED-545D-4772-8343-7434C9E6988E}.Release|x86.Build.0 = Release|Any CPU
		{446F315C-6511-B153-AB53-E008E32F50FC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{446F315C-6511-B153-AB53-E008E32F50FC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{446F315C-6511-B153-AB53-E008E32F50FC}.Debug|ARM.ActiveCfg = Debug|ARM
		{446F315C-6511-B153-AB53-E008E32F50FC}.Debug|ARM.Build.0 = Debug|ARM
		{446F315C-6511-B153-AB53-E008E32F50FC}.Debug|ARM64.ActiveCfg = Debug|ARM64
		{446F315C-6511-B153-AB53-E008E32F50FC}.Debug|ARM64.Build.0 = Debug|ARM64
		{446F315C-6511-B153-AB53-E008E32F50FC}.Debug|x64.ActiveCfg = Debug|x64
		{446F315C-6511-B153-AB53-E008E32F50FC}.Debug|x64.Build.0 = Debug|x64
		{446F315C-6511-B153-AB53-E008E32F50FC}.Debug|x86.ActiveCfg = Debug|x86
		{446F315C-6511-B153-AB53-E008E32F50FC}.Debug|x86.Build.0 = Debug|x86
		{446F315C-6511-B153-AB53-E008E32F50FC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{446F315C-6511-B153-AB53-E008E32F50FC}.Release|Any CPU.Build.0 = Release|Any CPU
		{446F315C-6511-B153-AB53-E008E32F50FC}.Release|ARM.ActiveCfg = Release|ARM
		{446F315C-6511-B153-AB53-E008E32F50FC}.Release|ARM.Build.0 = Release|ARM
		{446F315C-6511-B153-AB53-E008E32F50FC}.Release|ARM64.ActiveCfg = Release|ARM64
		{446F315C-6511-B153-AB53-E008E32F50FC}.Release|ARM64.Build.0 = Release|ARM64
		{446F315C-6511-B153-AB53-E008E32F50FC}.Release|x64.ActiveCfg = Release|x64
		{446F315C-6511-B153-AB53-E008E32F50FC}.Release|x64.Build.0 = Release|x64
		{446F315C-6511-B153-AB53-E008E32F50FC}.Release|x86.ActiveCfg = Release|x86
		{446F315C-6511-B153-AB53-E008E32F50FC}.Release|x86.Build.0 = Release|x86


		{81242C68-8A2A-4B4F-B795-F1491B90726A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{81242C68-8A2A-4B4F-B795-F1491B90726A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{81242C68-8A2A-4B4F-B795-F1491B90726A}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{81242C68-8A2A-4B4F-B795-F1491B90726A}.Debug|ARM.Build.0 = Debug|Any CPU
		{81242C68-8A2A-4B4F-B795-F1491B90726A}.Debug|ARM64.ActiveCfg = Debug|Any CPU
		{81242C68-8A2A-4B4F-B795-F1491B90726A}.Debug|ARM64.Build.0 = Debug|Any CPU
		{81242C68-8A2A-4B4F-B795-F1491B90726A}.Debug|x64.ActiveCfg = Debug|Any CPU
		{81242C68-8A2A-4B4F-B795-F1491B90726A}.Debug|x64.Build.0 = Debug|Any CPU
		{81242C68-8A2A-4B4F-B795-F1491B90726A}.Debug|x86.ActiveCfg = Debug|Any CPU
		{81242C68-8A2A-4B4F-B795-F1491B90726A}.Debug|x86.Build.0 = Debug|Any CPU
		{81242C68-8A2A-4B4F-B795-F1491B90726A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{81242C68-8A2A-4B4F-B795-F1491B90726A}.Release|Any CPU.Build.0 = Release|Any CPU
		{81242C68-8A2A-4B4F-B795-F1491B90726A}.Release|ARM.ActiveCfg = Release|Any CPU
		{81242C68-8A2A-4B4F-B795-F1491B90726A}.Release|ARM.Build.0 = Release|Any CPU
		{81242C68-8A2A-4B4F-B795-F1491B90726A}.Release|ARM64.ActiveCfg = Release|Any CPU
		{81242C68-8A2A-4B4F-B795-F1491B90726A}.Release|ARM64.Build.0 = Release|Any CPU
		{81242C68-8A2A-4B4F-B795-F1491B90726A}.Release|x64.ActiveCfg = Release|Any CPU
		{81242C68-8A2A-4B4F-B795-F1491B90726A}.Release|x64.Build.0 = Release|Any CPU
		{81242C68-8A2A-4B4F-B795-F1491B90726A}.Release|x86.ActiveCfg = Release|Any CPU
		{81242C68-8A2A-4B4F-B795-F1491B90726A}.Release|x86.Build.0 = Release|Any CPU
		{B8F7E2A1-9C3D-4E5F-A8B6-1234567890AB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B8F7E2A1-9C3D-4E5F-A8B6-1234567890AB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B8F7E2A1-9C3D-4E5F-A8B6-1234567890AB}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{B8F7E2A1-9C3D-4E5F-A8B6-1234567890AB}.Debug|ARM.Build.0 = Debug|Any CPU
		{B8F7E2A1-9C3D-4E5F-A8B6-1234567890AB}.Debug|ARM64.ActiveCfg = Debug|Any CPU
		{B8F7E2A1-9C3D-4E5F-A8B6-1234567890AB}.Debug|ARM64.Build.0 = Debug|Any CPU
		{B8F7E2A1-9C3D-4E5F-A8B6-1234567890AB}.Debug|x64.ActiveCfg = Debug|Any CPU
		{B8F7E2A1-9C3D-4E5F-A8B6-1234567890AB}.Debug|x64.Build.0 = Debug|Any CPU
		{B8F7E2A1-9C3D-4E5F-A8B6-1234567890AB}.Debug|x86.ActiveCfg = Debug|Any CPU
		{B8F7E2A1-9C3D-4E5F-A8B6-1234567890AB}.Debug|x86.Build.0 = Debug|Any CPU
		{B8F7E2A1-9C3D-4E5F-A8B6-1234567890AB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B8F7E2A1-9C3D-4E5F-A8B6-1234567890AB}.Release|Any CPU.Build.0 = Release|Any CPU
		{B8F7E2A1-9C3D-4E5F-A8B6-1234567890AB}.Release|ARM.ActiveCfg = Release|Any CPU
		{B8F7E2A1-9C3D-4E5F-A8B6-1234567890AB}.Release|ARM.Build.0 = Release|Any CPU
		{B8F7E2A1-9C3D-4E5F-A8B6-1234567890AB}.Release|ARM64.ActiveCfg = Release|Any CPU
		{B8F7E2A1-9C3D-4E5F-A8B6-1234567890AB}.Release|ARM64.Build.0 = Release|Any CPU
		{B8F7E2A1-9C3D-4E5F-A8B6-1234567890AB}.Release|x64.ActiveCfg = Release|Any CPU
		{B8F7E2A1-9C3D-4E5F-A8B6-1234567890AB}.Release|x64.Build.0 = Release|Any CPU
		{B8F7E2A1-9C3D-4E5F-A8B6-1234567890AB}.Release|x86.ActiveCfg = Release|Any CPU
		{B8F7E2A1-9C3D-4E5F-A8B6-1234567890AB}.Release|x86.Build.0 = Release|Any CPU
		{20DAEF9F-DA99-46D4-BA81-A032301F0233}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{20DAEF9F-DA99-46D4-BA81-A032301F0233}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{20DAEF9F-DA99-46D4-BA81-A032301F0233}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{20DAEF9F-DA99-46D4-BA81-A032301F0233}.Debug|ARM.Build.0 = Debug|Any CPU
		{20DAEF9F-DA99-46D4-BA81-A032301F0233}.Debug|ARM64.ActiveCfg = Debug|Any CPU
		{20DAEF9F-DA99-46D4-BA81-A032301F0233}.Debug|ARM64.Build.0 = Debug|Any CPU
		{20DAEF9F-DA99-46D4-BA81-A032301F0233}.Debug|x64.ActiveCfg = Debug|Any CPU
		{20DAEF9F-DA99-46D4-BA81-A032301F0233}.Debug|x64.Build.0 = Debug|Any CPU
		{20DAEF9F-DA99-46D4-BA81-A032301F0233}.Debug|x86.ActiveCfg = Debug|Any CPU
		{20DAEF9F-DA99-46D4-BA81-A032301F0233}.Debug|x86.Build.0 = Debug|Any CPU
		{20DAEF9F-DA99-46D4-BA81-A032301F0233}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{20DAEF9F-DA99-46D4-BA81-A032301F0233}.Release|Any CPU.Build.0 = Release|Any CPU
		{20DAEF9F-DA99-46D4-BA81-A032301F0233}.Release|ARM.ActiveCfg = Release|Any CPU
		{20DAEF9F-DA99-46D4-BA81-A032301F0233}.Release|ARM.Build.0 = Release|Any CPU
		{20DAEF9F-DA99-46D4-BA81-A032301F0233}.Release|ARM64.ActiveCfg = Release|Any CPU
		{20DAEF9F-DA99-46D4-BA81-A032301F0233}.Release|ARM64.Build.0 = Release|Any CPU
		{20DAEF9F-DA99-46D4-BA81-A032301F0233}.Release|x64.ActiveCfg = Release|Any CPU
		{20DAEF9F-DA99-46D4-BA81-A032301F0233}.Release|x64.Build.0 = Release|Any CPU
		{20DAEF9F-DA99-46D4-BA81-A032301F0233}.Release|x86.ActiveCfg = Release|Any CPU
		{20DAEF9F-DA99-46D4-BA81-A032301F0233}.Release|x86.Build.0 = Release|Any CPU
		{450887F6-3813-44A4-B3DD-DF173ED4D8D8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{450887F6-3813-44A4-B3DD-DF173ED4D8D8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{450887F6-3813-44A4-B3DD-DF173ED4D8D8}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{450887F6-3813-44A4-B3DD-DF173ED4D8D8}.Debug|ARM.Build.0 = Debug|Any CPU
		{450887F6-3813-44A4-B3DD-DF173ED4D8D8}.Debug|ARM64.ActiveCfg = Debug|Any CPU
		{450887F6-3813-44A4-B3DD-DF173ED4D8D8}.Debug|ARM64.Build.0 = Debug|Any CPU
		{450887F6-3813-44A4-B3DD-DF173ED4D8D8}.Debug|x64.ActiveCfg = Debug|Any CPU
		{450887F6-3813-44A4-B3DD-DF173ED4D8D8}.Debug|x64.Build.0 = Debug|Any CPU
		{450887F6-3813-44A4-B3DD-DF173ED4D8D8}.Debug|x86.ActiveCfg = Debug|Any CPU
		{450887F6-3813-44A4-B3DD-DF173ED4D8D8}.Debug|x86.Build.0 = Debug|Any CPU
		{450887F6-3813-44A4-B3DD-DF173ED4D8D8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{450887F6-3813-44A4-B3DD-DF173ED4D8D8}.Release|Any CPU.Build.0 = Release|Any CPU
		{450887F6-3813-44A4-B3DD-DF173ED4D8D8}.Release|ARM.ActiveCfg = Release|Any CPU
		{450887F6-3813-44A4-B3DD-DF173ED4D8D8}.Release|ARM.Build.0 = Release|Any CPU
		{450887F6-3813-44A4-B3DD-DF173ED4D8D8}.Release|ARM64.ActiveCfg = Release|Any CPU
		{450887F6-3813-44A4-B3DD-DF173ED4D8D8}.Release|ARM64.Build.0 = Release|Any CPU
		{450887F6-3813-44A4-B3DD-DF173ED4D8D8}.Release|x64.ActiveCfg = Release|Any CPU
		{450887F6-3813-44A4-B3DD-DF173ED4D8D8}.Release|x64.Build.0 = Release|Any CPU
		{450887F6-3813-44A4-B3DD-DF173ED4D8D8}.Release|x86.ActiveCfg = Release|Any CPU
		{450887F6-3813-44A4-B3DD-DF173ED4D8D8}.Release|x86.Build.0 = Release|Any CPU
		{C053D5EB-E089-497F-BB4A-3D7E80DDEA32}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C053D5EB-E089-497F-BB4A-3D7E80DDEA32}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C053D5EB-E089-497F-BB4A-3D7E80DDEA32}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{C053D5EB-E089-497F-BB4A-3D7E80DDEA32}.Debug|ARM.Build.0 = Debug|Any CPU
		{C053D5EB-E089-497F-BB4A-3D7E80DDEA32}.Debug|ARM64.ActiveCfg = Debug|Any CPU
		{C053D5EB-E089-497F-BB4A-3D7E80DDEA32}.Debug|ARM64.Build.0 = Debug|Any CPU
		{C053D5EB-E089-497F-BB4A-3D7E80DDEA32}.Debug|x64.ActiveCfg = Debug|Any CPU
		{C053D5EB-E089-497F-BB4A-3D7E80DDEA32}.Debug|x64.Build.0 = Debug|Any CPU
		{C053D5EB-E089-497F-BB4A-3D7E80DDEA32}.Debug|x86.ActiveCfg = Debug|Any CPU
		{C053D5EB-E089-497F-BB4A-3D7E80DDEA32}.Debug|x86.Build.0 = Debug|Any CPU
		{C053D5EB-E089-497F-BB4A-3D7E80DDEA32}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C053D5EB-E089-497F-BB4A-3D7E80DDEA32}.Release|Any CPU.Build.0 = Release|Any CPU
		{C053D5EB-E089-497F-BB4A-3D7E80DDEA32}.Release|ARM.ActiveCfg = Release|Any CPU
		{C053D5EB-E089-497F-BB4A-3D7E80DDEA32}.Release|ARM.Build.0 = Release|Any CPU
		{C053D5EB-E089-497F-BB4A-3D7E80DDEA32}.Release|ARM64.ActiveCfg = Release|Any CPU
		{C053D5EB-E089-497F-BB4A-3D7E80DDEA32}.Release|ARM64.Build.0 = Release|Any CPU
		{C053D5EB-E089-497F-BB4A-3D7E80DDEA32}.Release|x64.ActiveCfg = Release|Any CPU
		{C053D5EB-E089-497F-BB4A-3D7E80DDEA32}.Release|x64.Build.0 = Release|Any CPU
		{C053D5EB-E089-497F-BB4A-3D7E80DDEA32}.Release|x86.ActiveCfg = Release|Any CPU
		{C053D5EB-E089-497F-BB4A-3D7E80DDEA32}.Release|x86.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{42AA32C4-1DB0-2C46-8A19-9CF10807ECC8} = {827E0CD3-B72D-47B6-A68D-7590B98EB39B}
		{C053D5EB-E089-497F-BB4A-3D7E80DDEA32} = {42AA32C4-1DB0-2C46-8A19-9CF10807ECC8}
		{22C75AED-545D-4772-8343-7434C9E6988E} = {42AA32C4-1DB0-2C46-8A19-9CF10807ECC8}
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890} = {827E0CD3-B72D-47B6-A68D-7590B98EB39B}
		{B2C3D4E5-F6G7-8901-BCDE-F23456789012} = {827E0CD3-B72D-47B6-A68D-7590B98EB39B}
		{D1E2F3G4-H5I6-7890-JKLM-NO1234567890} = {827E0CD3-B72D-47B6-A68D-7590B98EB39B}
		{20DAEF9F-DA99-46D4-BA81-A032301F0233} = {A1B2C3D4-E5F6-7890-ABCD-EF1234567890}
		{81242C68-8A2A-4B4F-B795-F1491B90726A} = {B2C3D4E5-F6G7-8901-BCDE-F23456789012}
		{B8F7E2A1-9C3D-4E5F-A8B6-1234567890AB} = {B2C3D4E5-F6G7-8901-BCDE-F23456789012}
		{450887F6-3813-44A4-B3DD-DF173ED4D8D8} = {D1E2F3G4-H5I6-7890-JKLM-NO1234567890}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {1EE0A7E0-E168-4879-8E1A-0478C8C32ACF}
	EndGlobalSection
EndGlobal
