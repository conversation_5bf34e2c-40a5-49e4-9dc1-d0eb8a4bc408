<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0-windows</TargetFramework>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
    <PackageId>WaferAligner.Infrastructure.Common</PackageId>
    <PackageVersion>1.0.0</PackageVersion>
    <Authors>WaferAligner Team</Authors>
    <Description>WaferAligner基础设施通用组件库，包含资源管理、定时器封装、UI线程安全调用、任务扩展、性能监控等功能</Description>
    <PackageTags>WinForms;Infrastructure;ResourceManager;Timer;UI;Performance</PackageTags>
    <RepositoryUrl>https://github.com/your-org/WaferAligner</RepositoryUrl>
    <PackageLicenseExpression>MIT</PackageLicenseExpression>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.6" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\..\Services\Service.Common\WaferAligner.Services.Abstractions\WaferAligner.Services.Abstractions.csproj" />
    <ProjectReference Include="..\..\..\Services\Service.Common\WaferAligner.Services.Extensions\WaferAligner.Services.Extensions.csproj" />
    <ProjectReference Include="..\..\..\WaferAligner.EventIds\WaferAligner.EventIds.csproj" />
  </ItemGroup>

</Project>
