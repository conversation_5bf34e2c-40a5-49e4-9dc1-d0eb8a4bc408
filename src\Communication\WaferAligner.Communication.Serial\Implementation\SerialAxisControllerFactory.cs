﻿using System;
using System.Collections.Concurrent;
using System.Threading;
using System.Threading.Tasks;
using WaferAligner.Services.Abstractions;
using WaferAligner.EventIds;
using WaferAligner.Communication.Serial.Interfaces;
using WaferAligner.Services.Extensions;
namespace WaferAligner.Communication.Serial.Implementation
{
    /// <summary>
    /// 串口轴控制器工厂实现
    /// </summary>
    public class SerialAxisControllerFactory : ISerialAxisControllerFactory
    {
        private readonly ILoggingService _loggingService;
        private readonly ISerialConnectionManager _serialConnectionManager;
        private readonly ISerialComWrapper _serialComWrapper;
        private readonly ConcurrentDictionary<string, ISerialAxisController> _controllers;
        
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="loggingService">日志服务</param>
        /// <param name="serialConnectionManager">串口连接管理器</param>
        /// <param name="serialComWrapper">串口通信封装</param>
        public SerialAxisControllerFactory(
            ILoggingService loggingService,
            ISerialConnectionManager serialConnectionManager,
            ISerialComWrapper serialComWrapper)
        {
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
            _serialConnectionManager = serialConnectionManager ?? throw new ArgumentNullException(nameof(serialConnectionManager));
            _serialComWrapper = serialComWrapper ?? throw new ArgumentNullException(nameof(serialComWrapper));
            _controllers = new ConcurrentDictionary<string, ISerialAxisController>();
            
            _loggingService.LogDebug("创建SerialAxisControllerFactory", WaferAligner.EventIds.EventIds.Serial_Axis_Operation);
        }
        
        /// <inheritdoc/>
        public async Task<ISerialAxisController> CreateAxisControllerAsync(string axisName, CancellationToken cancellationToken = default)
        {
            // 验证轴名称
            if (string.IsNullOrWhiteSpace(axisName))
            {
                throw new ArgumentException("轴名称不能为空", nameof(axisName));
            }
            
            // 规范化轴名称为大写
            axisName = axisName.ToUpper();
            
            // 检查是否已创建
            if (_controllers.TryGetValue(axisName, out var existingController))
            {
                return existingController;
            }
            
            // 验证轴名称是否有效
            if (axisName != "X" && axisName != "Y" && axisName != "R")
            {
                throw new ArgumentException($"不支持的轴名称: {axisName}", nameof(axisName));
            }
            
            // 创建新控制器
            var controller = new SerialAxisController(
                axisName,
                _loggingService,
                _serialConnectionManager,
                _serialComWrapper);
                
            // 添加到缓存
            _controllers.TryAdd(axisName, controller);
            
            // 记录日志
            _loggingService.LogInformation($"创建{axisName}轴控制器", WaferAligner.EventIds.EventIds.Serial_Axis_Operation);
            
            // 使能轴
            await controller.EnableAxisAsync(cancellationToken);
            
            return controller;
        }
        
        /// <inheritdoc/>
        public ISerialAxisController GetAxisController(string axisName)
        {
            if (string.IsNullOrWhiteSpace(axisName))
            {
                throw new ArgumentException("轴名称不能为空", nameof(axisName));
            }
            
            // 规范化轴名称为大写
            axisName = axisName.ToUpper();
            
            if (_controllers.TryGetValue(axisName, out var controller))
            {
                return controller;
            }
            
            // 验证轴名称是否有效
            if (axisName != "X" && axisName != "Y" && axisName != "R")
            {
                throw new ArgumentException($"不支持的轴名称: {axisName}", nameof(axisName));
            }
            
            // 如果不存在，同步创建
            var newController = new SerialAxisController(
                axisName,
                _loggingService,
                _serialConnectionManager,
                _serialComWrapper);
                
            _controllers.TryAdd(axisName, newController);
            
            _loggingService.LogInformation($"同步创建{axisName}轴控制器", WaferAligner.EventIds.EventIds.Serial_Axis_Operation);
            
            return newController;
        }
    }
}
