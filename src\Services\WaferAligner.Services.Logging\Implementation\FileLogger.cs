using WaferAligner.Services.Logging.Models;
using WaferAligner.Services.Logging.Abstractions;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Threading;
using System.Threading.Channels;

namespace WaferAligner.Services.Logging.Implementation
{
    public class FileLogger : ILogger
    {
        static string DebugLogFilePath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData) + @"\Aligner\log\Debug\Debug_" + DateTime.Today.ToString("yyyyMMdd") + ".log";
        static string CommonLogFilePath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData) + @"\Aligner\log\Log_" + DateTime.Today.ToString("yyyyMMdd") + ".log";
        static string TraceLogFilePath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData) + @"\Aligner\log\Trace\Trace_" + DateTime.Today.ToString("yyyyMMdd") + ".log";
        private readonly string _name;
        private readonly ILoggingService _loggingService;

        // 添加最低日志级别字段
        private LogLevel _minimumLogLevel = LogLevel.Information;

        // 添加静态锁对象，确保所有FileLogger实例共享同一个锁
        private static readonly object _fileLock = new object();

        ChannelReader<LogEntry<string>> _channel;
        private Thread _listenerThread;
        public int EventId { get; set; }

        public Dictionary<LogLevel, string> LogLevels { get; set; } = new()
        {
            [LogLevel.Trace] = TraceLogFilePath,
            [LogLevel.Debug] = DebugLogFilePath,
            [LogLevel.Information] = CommonLogFilePath,
            [LogLevel.Warning] = CommonLogFilePath,
            [LogLevel.Error] = CommonLogFilePath,
        };

        public FileLogger(ILoggingService loggingService)
        {

            if (!Directory.Exists(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData) + @"\Aligner\log"))
                Directory.CreateDirectory(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData) + @"\Aligner\log");
            if (!Directory.Exists(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData) + @"\Aligner\log\Debug"))
                Directory.CreateDirectory(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData) + @"\Aligner\log\Debug");
            if (!Directory.Exists(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData) + @"\Aligner\log\Trace"))
                Directory.CreateDirectory(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData) + @"\Aligner\log\Trace");

            _loggingService = loggingService;
            _channel = _loggingService.StreamLog();
            _listenerThread = new Thread(async () =>
            {

                while (await _channel.WaitToReadAsync())
                {
                    while (_channel.TryRead(out var logEntry))
                    {
                        Log(logEntry.LogLevel, logEntry.Id, logEntry.LogMessage, null, null);
                    }
                }
            });
            _listenerThread.Start();
        }
        
        /// <summary>
        /// 设置最低日志级别
        /// </summary>
        /// <param name="level">日志级别</param>
        public void SetMinimumLogLevel(LogLevel level)
        {
            _minimumLogLevel = level;
        }

        public IDisposable BeginScope<TState>(TState state) => default!;

        public bool IsEnabled(LogLevel logLevel) =>
            LogLevels.ContainsKey(logLevel) && logLevel >= _minimumLogLevel;

        public void Log<TState>(LogLevel logLevel, EventId eventId, TState state, Exception exception, Func<TState, Exception, string> formatter)
        {
            if (!IsEnabled(logLevel)) return;

            LogEntry<string> log = new LogEntry<string>
            {
                LogLevel = logLevel,
                Id = eventId,
                LogMessage = state.ToString(),
                TimeStamp = DateTime.Now,
            };
            // 使用FileShare.ReadWrite允许多个进程/线程同时访问文件
            using (var fs = File.Open(LogLevels[logLevel], FileMode.Append, FileAccess.Write, FileShare.ReadWrite))
            {
                byte[] bytes = new UTF8Encoding(true).GetBytes(log.ToString());

                fs.Write(bytes, 0, bytes.Length);
                fs.Flush(); // 确保数据立即写入磁盘
            }
        }
    }
}
