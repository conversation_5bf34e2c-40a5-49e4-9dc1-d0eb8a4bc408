﻿using System;
using System.Threading;
using System.Threading.Tasks;
using WaferAligner.Services.Abstractions;
using WaferAligner.Interfaces;
using WaferAligner.Services;
using WaferAligner.Communication.Inovance.Abstractions;
using WaferAligner.EventIds;
using Microsoft.Extensions.Logging;
using AlignerUI; // 添加引用以使用AxisAction枚举

namespace WaferAligner.Models
{
    /// <summary>
    /// Z轴视图模型实现，继承自PlcAxisViewModelBase，包含Z轴特有功能
    /// </summary>
    public class ZAxisViewModelNew : PlcAxisViewModelBase, IZAxisViewModel
    {
        private readonly IAxisEventService _axisEventService;
        
        public ZAxisViewModelNew(
            IPlcCommunication plcCommunication,
            ILoggingService loggingService,
            IAxisEventService axisEventService)
            : base("Z", plcCommunication, loggingService)
        {
            _axisEventService = axisEventService ?? throw new ArgumentNullException(nameof(axisEventService));
        }
        
        #region IZAxisViewModel特有方法
        
        /// <summary>
        /// 设置回原点偏移量
        /// </summary>
        public async Task<bool> SetHomeOffsetAsync(double value, CancellationToken cancellationToken = default)
        {
            try
            {
                cancellationToken.ThrowIfCancellationRequested();
                
                LogInformation($"Z轴设置回原点偏移量: {value}", WaferAligner.EventIds.EventIds.Axis_Operation_Info);
                
                // Z轴回原点偏移量需要乘以转换因子
                double convertedValue = value * AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION;
                
                // 使用基类中的WritePlcVariableAsync方法，而不是直接操作PLC实例
                bool result = await WritePlcVariableAsync($"{AxisConstants.AXIS_GVL}.ZHomeOffset", convertedValue, cancellationToken);
                
                if (result)
                {
                    LogInformation($"Z轴设置回原点偏移量成功: {value} (转换后: {convertedValue})", WaferAligner.EventIds.EventIds.Axis_Operation_Info);
                }
                
                return result;
            }
            catch (OperationCanceledException)
            {
                LogWarning($"Z轴设置回原点偏移量操作被取消", WaferAligner.EventIds.EventIds.Operation_Cancelled);
                throw;
            }
            catch (Exception ex)
            {
                LogError(ex, $"Z轴设置回原点偏移量失败: {ex.Message}", WaferAligner.EventIds.EventIds.Axis_Move_Error);
                return false;
            }
        }
        
        /// <summary>
        /// 为兼容旧版本保留的方法
        /// </summary>
        [Obsolete("请使用带CancellationToken参数的SetHomeOffsetAsync方法")]
        public Task<bool> SetHomeOffsetAsync(double value)
        {
            return SetHomeOffsetAsync(value, CancellationToken.None);
        }
        
        /// <summary>
        /// Z轴回原点停止
        /// </summary>
        public async Task<bool> ZHomeStopAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                cancellationToken.ThrowIfCancellationRequested();
                
                LogInformation("Z轴回原点停止", WaferAligner.EventIds.EventIds.Axis_Operation_Info);
                
                // 发送回原点停止命令
                await WritePlcVariableAsync($"{AxisConstants.AXIS_GVL}.ZHomeStop", true, cancellationToken);
                
                // 使用TaskDelay代替直接延时，支持取消
                using (var delayTokenSource = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken))
                {
                    await Task.Delay(200, delayTokenSource.Token);
                }
                
                await WritePlcVariableAsync($"{AxisConstants.AXIS_GVL}.ZHomeStop", false, cancellationToken);
                
                LogInformation("Z轴回原点停止命令已发送", WaferAligner.EventIds.EventIds.Axis_Operation_Info);
                return true;
            }
            catch (OperationCanceledException)
            {
                LogWarning("Z轴回原点停止操作被取消", WaferAligner.EventIds.EventIds.Operation_Cancelled);
                
                // 即使操作被取消，也要尝试将停止信号设为false
                try 
                {
                    await WritePlcVariableAsync($"{AxisConstants.AXIS_GVL}.ZHomeStop", false, CancellationToken.None);
                }
                catch { /* 忽略清理操作的错误 */ }
                
                throw;
            }
            catch (Exception ex)
            {
                LogError(ex, $"Z轴回原点停止失败: {ex.Message}", WaferAligner.EventIds.EventIds.Axis_Move_Error);
                return false;
            }
        }
        
        /// <summary>
        /// 为向后兼容保留的方法
        /// </summary>
        [Obsolete("请使用ZHomeStopAsync")]
        public async Task<bool> ZHomeStop()
        {
            try
            {
                return await ZHomeStopAsync(CancellationToken.None);
            }
            catch (OperationCanceledException)
            {
                // 兼容方法不应该传播取消异常
                LogWarning("Z轴回原点停止操作被取消", WaferAligner.EventIds.EventIds.Operation_Cancelled);
                return false;
            }
        }
        
        /// <summary>
        /// Z轴强制停止
        /// </summary>
        public async Task<bool> ZTakeForceStopAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                cancellationToken.ThrowIfCancellationRequested();
                
                LogInformation("Z轴强制停止", WaferAligner.EventIds.EventIds.Axis_Operation_Info);
                
                // 发送强制停止命令
                await WritePlcVariableAsync($"{AxisConstants.AXIS_GVL}.ZTakeForceStop", true, cancellationToken);
                
                // 使用TaskDelay代替直接延时，支持取消
                using (var delayTokenSource = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken))
                {
                    await Task.Delay(200, delayTokenSource.Token);
                }
                
                await WritePlcVariableAsync($"{AxisConstants.AXIS_GVL}.ZTakeForceStop", false, cancellationToken);
                
                LogInformation("Z轴强制停止命令已发送", WaferAligner.EventIds.EventIds.Axis_Operation_Info);
                return true;
            }
            catch (OperationCanceledException)
            {
                LogWarning("Z轴强制停止操作被取消", WaferAligner.EventIds.EventIds.Operation_Cancelled);
                
                // 即使操作被取消，也要尝试将停止信号设为false
                try
                {
                    await WritePlcVariableAsync($"{AxisConstants.AXIS_GVL}.ZTakeForceStop", false, CancellationToken.None);
                }
                catch { /* 忽略清理操作的错误 */ }
                
                throw;
            }
            catch (Exception ex)
            {
                LogError(ex, $"Z轴强制停止失败: {ex.Message}", WaferAligner.EventIds.EventIds.Axis_Move_Error);
                return false;
            }
        }
        
        /// <summary>
        /// 为向后兼容保留的方法
        /// </summary>
        [Obsolete("请使用ZTakeForceStopAsync")]
        public async Task<bool> ZTakeForceStop()
        {
            try
            {
                return await ZTakeForceStopAsync(CancellationToken.None);
            }
            catch (OperationCanceledException)
            {
                // 兼容方法不应该传播取消异常
                LogWarning("Z轴强制停止操作被取消", WaferAligner.EventIds.EventIds.Operation_Cancelled);
                return false;
            }
        }
        
        /// <summary>
        /// 获取当前位置，支持取消操作
        /// </summary>
        public async Task<double> GetCurrentPositionAsync(CancellationToken cancellationToken)
        {
            cancellationToken.ThrowIfCancellationRequested();
            // 调用基类方法，但不支持取消，这是一个折中方案
            return await ReadPlcVariableAsync<double>($"{AxisConstants.AXIS_GVL}.ZPosition", cancellationToken);
        }

        /// <summary>
        /// 等待Z轴到达安全位置
        /// </summary>
        public async Task<bool> WaitSafetyPositionAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                cancellationToken.ThrowIfCancellationRequested();
                LogInformation("Z轴等待安全位置", WaferAligner.EventIds.EventIds.Axis_Operation_Info);
                
                // 读取安全位置设定值
                double safetyPosition = 0;
                try
                {
                    safetyPosition = await ReadPlcVariableAsync<double>($"{AxisConstants.AXIS_GVL}.ZSafetyPosition", cancellationToken);
                    LogInformation($"Z轴安全位置设定值: {safetyPosition}", WaferAligner.EventIds.EventIds.Axis_Operation_Info);
                }
                catch (Exception ex)
                {
                    LogWarning($"读取Z轴安全位置失败: {ex.Message}，使用默认值", WaferAligner.EventIds.EventIds.Plc_Variables_Read_Failed);
                    safetyPosition = 1000; // 读取失败时使用默认值
                }
                
                // 检查当前位置是否已经在安全位置
                double currentPosition = await GetCurrentPositionAsync(cancellationToken);
                double tolerance = 1.0; // 位置误差容许范围，与原始实现保持一致
                
                if (Math.Abs(currentPosition - safetyPosition) <= tolerance)
                {
                    LogInformation($"Z轴已在安全位置 {safetyPosition}", WaferAligner.EventIds.EventIds.Axis_Operation_Info);
                    return true;
                }
                
                // 移动到安全位置
                LogInformation($"Z轴移动到安全位置: {safetyPosition}", WaferAligner.EventIds.EventIds.Axis_Move_Started);
                return await MoveToPositionAsync(safetyPosition, cancellationToken);
            }
            catch (OperationCanceledException)
            {
                LogWarning("Z轴等待安全位置操作被取消", WaferAligner.EventIds.EventIds.Operation_Cancelled);
                throw;
            }
            catch (Exception ex)
            {
                LogError(ex, $"Z轴等待安全位置失败: {ex.Message}", WaferAligner.EventIds.EventIds.Axis_Move_Error);
                return false;
            }
        }
        
        /// <summary>
        /// 为向后兼容保留的方法，原方法名拼写有误(Safty)，现已纠正为Safety
        /// </summary>
        [Obsolete("请使用WaitSafetyPositionAsync")]
        public Task<bool> WaitSaftyPositionAsync()
        {
            return WaitSafetyPositionAsync(CancellationToken.None);
        }
        
        #endregion
        
        #region Z轴特有方法

        /// <summary>
        /// 执行Z轴复位操作，清除错误状态
        /// </summary>
        public async Task ResetPosition(CancellationToken cancellationToken = default)
        {
            try
            {
                cancellationToken.ThrowIfCancellationRequested();
                
                LogInformation("开始Z轴复位操作", WaferAligner.EventIds.EventIds.Axis_Reset_Start);
                
                // 复位前先确保轴停止
                bool isStopDone = false;
                try
                {
                    // 读取停止状态
                    isStopDone = await ReadPlcVariableAsync<bool>($"{AxisConstants.AXIS_GVL}.{AxisName}StopDone", cancellationToken);
                }
                catch (Exception ex)
                {
                    LogWarning($"读取Z轴停止状态失败: {ex.Message}", WaferAligner.EventIds.EventIds.Plc_Variables_Read_Failed);
                }
                
                if (!isStopDone)
                {
                    await StopAsync(cancellationToken);
                    
                    // 使用TaskDelay代替直接延时，支持取消
                    using (var delayTokenSource = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken))
                    {
                        await Task.Delay(500, delayTokenSource.Token); // 等待停止完成
                    }
                }
                
                // 执行复位操作
                await WritePlcVariableAsync($"{AxisConstants.AXIS_GVL}.{AxisName}AxisActions", AxisAction.Reset, cancellationToken);
                
                using (var delayTokenSource = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken))
                {
                    await Task.Delay(200, delayTokenSource.Token);
                }
                
                await WritePlcVariableAsync($"{AxisConstants.AXIS_GVL}.{AxisName}Execute", true, cancellationToken);
                
                // 等待复位完成信号
                int timeout = 0;
                bool isResetDone = false;
                
                while (!isResetDone && timeout < 50) // 最多等待10秒
                {
                    cancellationToken.ThrowIfCancellationRequested();
                    
                    // 读取复位状态
                    try
                    {
                        isResetDone = await ReadPlcVariableAsync<bool>($"{AxisConstants.AXIS_GVL}.{AxisName}ResetDone", cancellationToken);
                    }
                    catch (Exception ex)
                    {
                        LogWarning($"读取Z轴复位完成状态失败: {ex.Message}", WaferAligner.EventIds.EventIds.Plc_Variables_Read_Failed);
                    }
                    
                    if (!isResetDone)
                    {
                        using (var delayTokenSource = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken))
                        {
                            await Task.Delay(200, delayTokenSource.Token);
                        }
                        timeout++;
                    }
                }
                
                if (isResetDone)
                {
                    LogInformation("Z轴复位操作完成", WaferAligner.EventIds.EventIds.Axis_Reset_Complete);
                }
                else
                {
                    LogWarning("Z轴复位操作超时", WaferAligner.EventIds.EventIds.Axis_Reset_Timeout);
                }
            }
            catch (OperationCanceledException)
            {
                LogWarning("Z轴复位操作被取消", WaferAligner.EventIds.EventIds.Operation_Cancelled);
                throw;
            }
            catch (Exception ex)
            {
                LogError(ex, $"Z轴复位操作失败: {ex.Message}", WaferAligner.EventIds.EventIds.Axis_Move_Error);
                throw; // 重新抛出异常，让调用者处理
            }
        }
        
        /// <summary>
        /// 为向后兼容保留的方法
        /// </summary>
        [Obsolete("请使用带CancellationToken参数的ResetPosition方法")]
        public async Task ResetPosition()
        {
            try
            {
                await ResetPosition(CancellationToken.None);
            }
            catch (OperationCanceledException)
            {
                // 兼容方法不应该传播取消异常
                LogWarning("Z轴复位操作被取消", WaferAligner.EventIds.EventIds.Operation_Cancelled);
            }
            catch (Exception ex)
            {
                LogError(ex, $"Z轴复位操作失败: {ex.Message}", WaferAligner.EventIds.EventIds.Axis_Move_Error);
                throw; // 重新抛出异常，保持与原始行为一致
            }
        }
        
        /// <summary>
        /// 执行复位操作，清除错误状态
        /// </summary>
        public override async Task<bool> ResetAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                // 使用Z轴专用的Reset功能
                await ResetPosition(cancellationToken);
                return true;
            }
            catch (OperationCanceledException)
            {
                LogWarning("Z轴复位操作被取消", WaferAligner.EventIds.EventIds.Operation_Cancelled);
                throw;
            }
            catch (Exception ex)
            {
                LogError(ex, $"Z轴复位失败: {ex.Message}", WaferAligner.EventIds.EventIds.Axis_Move_Error);
                return false;
            }
        }
        
        /// <summary>
        /// 为向后兼容保留的方法
        /// </summary>
        [Obsolete("请使用带CancellationToken参数的ResetAsync方法")]
        public override Task<bool> ResetAsync()
        {
            return ResetAsync(CancellationToken.None);
        }
        
        /// <summary>
        /// Z轴正向点动
        /// </summary>
        public override async Task<bool> JogForwardAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                cancellationToken.ThrowIfCancellationRequested();
                LogInformation("Z轴开始正向点动", WaferAligner.EventIds.EventIds.Axis_Move_Started);
                
                // 向PLC发送正向点动命令
                await WritePlcVariableAsync($"{AxisConstants.AXIS_GVL}.{AxisName}AxisActions", AxisAction.JogF, cancellationToken);
                await WritePlcVariableAsync($"{AxisConstants.AXIS_GVL}.{AxisName}Execute", true, cancellationToken);
                
                return true;
            }
            catch (OperationCanceledException)
            {
                LogWarning("Z轴正向点动操作被取消", WaferAligner.EventIds.EventIds.Operation_Cancelled);
                throw;
            }
            catch (Exception ex)
            {
                LogError(ex, $"Z轴正向点动失败: {ex.Message}", WaferAligner.EventIds.EventIds.Axis_Move_Error);
                return false;
            }
        }
        
        /// <summary>
        /// 为向后兼容保留的方法
        /// </summary>
        [Obsolete("请使用带CancellationToken参数的JogForwardAsync方法")]
        public new async Task<bool> JogForwardAsync()
        {
            try
            {
                return await JogForwardAsync(CancellationToken.None);
            }
            catch (OperationCanceledException)
            {
                // 兼容方法不应该传播取消异常
                LogWarning("Z轴正向点动操作被取消", WaferAligner.EventIds.EventIds.Operation_Cancelled);
                return false;
            }
        }
        
        /// <summary>
        /// Z轴反向点动
        /// </summary>
        public override async Task<bool> JogBackwardAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                cancellationToken.ThrowIfCancellationRequested();
                LogInformation("Z轴开始反向点动", WaferAligner.EventIds.EventIds.Axis_Move_Started);
                
                // 向PLC发送反向点动命令
                await WritePlcVariableAsync($"{AxisConstants.AXIS_GVL}.{AxisName}AxisActions", AxisAction.JogB, cancellationToken);
                await WritePlcVariableAsync($"{AxisConstants.AXIS_GVL}.{AxisName}Execute", true, cancellationToken);
                
                return true;
            }
            catch (OperationCanceledException)
            {
                LogWarning("Z轴反向点动操作被取消", WaferAligner.EventIds.EventIds.Operation_Cancelled);
                throw;
            }
            catch (Exception ex)
            {
                LogError(ex, $"Z轴反向点动失败: {ex.Message}", WaferAligner.EventIds.EventIds.Axis_Move_Error);
                return false;
            }
        }
        
        /// <summary>
        /// 为向后兼容保留的方法
        /// </summary>
        [Obsolete("请使用带CancellationToken参数的JogBackwardAsync方法")]
        public new async Task<bool> JogBackwardAsync()
        {
            try
            {
                return await JogBackwardAsync(CancellationToken.None);
            }
            catch (OperationCanceledException)
            {
                // 兼容方法不应该传播取消异常
                LogWarning("Z轴反向点动操作被取消", WaferAligner.EventIds.EventIds.Operation_Cancelled);
                return false;
            }
        }
        
        /// <summary>
        /// Z轴点动停止
        /// </summary>
        public override async Task<bool> JogStopAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                cancellationToken.ThrowIfCancellationRequested();
                LogInformation("Z轴点动停止", WaferAligner.EventIds.EventIds.Axis_Operation_Info);
                
                // 向PLC发送点动停止命令
                await WritePlcVariableAsync($"{AxisConstants.AXIS_GVL}.{AxisName}AxisActions", AxisAction.Stop, cancellationToken);
                await WritePlcVariableAsync($"{AxisConstants.AXIS_GVL}.{AxisName}Execute", true, cancellationToken);
                
                return true;
            }
            catch (OperationCanceledException)
            {
                LogWarning("Z轴点动停止操作被取消", WaferAligner.EventIds.EventIds.Operation_Cancelled);
                throw;
            }
            catch (Exception ex)
            {
                LogError(ex, $"Z轴点动停止失败: {ex.Message}", WaferAligner.EventIds.EventIds.Axis_Move_Error);
                return false;
            }
        }
        
        /// <summary>
        /// 为向后兼容保留的方法
        /// </summary>
        [Obsolete("请使用带CancellationToken参数的JogStopAsync方法")]
        public new async Task<bool> JogStopAsync()
        {
            try
            {
                return await JogStopAsync(CancellationToken.None);
            }
            catch (OperationCanceledException)
            {
                // 兼容方法不应该传播取消异常
                LogWarning("Z轴点动停止操作被取消", WaferAligner.EventIds.EventIds.Operation_Cancelled);
                return false;
            }
        }
        
        #endregion
        
        #region 初始化和事件订阅
        
        public override async Task InitializeAsync()
        {
            await base.InitializeAsync();

            // 直接注册PLC变量，避免通过AxisEventService造成循环依赖
            // 注册位置变化事件
            RegisterAction("position", OnAxisPositionChanged);

            // 注册状态变化事件
            RegisterAction("state", OnAxisStateChanged);

            LogInformation("Z轴初始化完成", WaferAligner.EventIds.EventIds.Axis_Initialize_Succeeded);
        }
        
        private void OnAxisPositionChanged(object value)
        {
            try
            {
                if (value is int position)
                {
                    OnPositionChanged(position);
                }
            }
            catch (Exception ex)
            {
                LogError(ex, $"处理Z轴位置事件失败: {ex.Message}", WaferAligner.EventIds.EventIds.Axis_Position_Read_Error);
            }
        }
        
        private void OnAxisStateChanged(object value)
        {
            try
            {
                // 解析状态对象
                dynamic stateData = value;
                bool isMoving = stateData?.IsMoving ?? false;
                bool isHomed = stateData?.IsHomed ?? false;
                bool isConnected = stateData?.IsConnected ?? false;
                
                OnStateChanged(isMoving, isHomed, isConnected);
            }
            catch (Exception ex)
            {
                LogError(ex, $"处理Z轴状态事件失败: {ex.Message}", WaferAligner.EventIds.EventIds.Axis_Position_Read_Error);
            }
        }
        
        #endregion
    }
}
