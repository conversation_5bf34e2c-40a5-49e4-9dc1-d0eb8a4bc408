using System;
using System.ComponentModel;
using System.Threading.Tasks;
using System.Windows.Forms;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Sunny.UI;
using WaferAligner.Services.Abstractions;
using WaferAligner.Services.Extensions;
using WaferAligner.Interfaces; // 添加对接口命名空间的引用
using System.Collections.Generic;
using System.Timers; // 添加对List的引用

namespace WaferAligner.Common
{
    /// <summary>
    /// 基础页面类
    /// 继承自UIPage，提供统一的UI功能、资源管理和生命周期控制
    /// </summary>
    public class BasePage : UIPage, IDisposable
    {
        private ILoggingService _loggingService;
        private WaferAligner.Common.ResourceManager _resourceManager;
        private PerformanceMonitor _performanceMonitor;
        private IUserContext _userContext; // 添加用户上下文
        private volatile bool _isDisposed = false;
        private volatile bool _isInitialized = false;
        private volatile bool _isContentLoaded = false;
        
        // 检查是否在设计模式
        protected static bool IsDesignMode
        {
            get
            {
                return (LicenseManager.UsageMode == LicenseUsageMode.Designtime);
            }
        }

        protected IServiceProvider ServiceProvider => IsDesignMode ? null : CommonFun.host?.Services;

        /// <summary>
        /// 获取日志记录器
        /// </summary>
        protected ILoggingService Logger => _loggingService;
        
        // 移除过时的ILogger属性

        /// <summary>
        /// 获取资源管理器
        /// </summary>
        protected WaferAligner.Common.ResourceManager ResourceManager => _resourceManager;
        
        /// <summary>
        /// 获取性能监控器
        /// </summary>
        protected PerformanceMonitor PerformanceMonitor => _performanceMonitor;

        /// <summary>
        /// 获取用户上下文
        /// </summary>
        protected IUserContext UserContext => _userContext;

        /// <summary>
        /// 页面是否已初始化
        /// </summary>
        protected bool IsInitialized => _isInitialized;
        
        /// <summary>
        /// 页面内容是否已加载
        /// </summary>
        protected bool IsContentLoaded => _isContentLoaded;
        
        /// <summary>
        /// 是否启用懒加载
        /// </summary>
        protected virtual bool EnableLazyLoading => false;

        public BasePage()
        {
            try
            {
                // 设计模式下不初始化服务
                if (IsDesignMode)
                {
                    return;
                }
                
                // 获取服务
                _loggingService = ServiceProvider?.GetService<ILoggingService>();
                _resourceManager = ServiceProvider?.GetService<WaferAligner.Common.ResourceManager>();
                _performanceMonitor = ServiceProvider?.GetService<PerformanceMonitor>();
                _userContext = ServiceProvider?.GetService<IUserContext>(); // 获取用户上下文

                // 注册事件
                this.Load += BasePage_Load;
                this.ParentChanged += BasePage_ParentChanged;
                this.VisibleChanged += BasePage_VisibleChanged;

                _loggingService?.LogDebug($"创建页面: {this.GetType().Name}");
            }
            catch (Exception ex)
            {
                // 如果初始化失败，记录错误但不抛出异常
                Console.WriteLine($"BasePage初始化失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 可见性变化事件处理
        /// </summary>
        private async void BasePage_VisibleChanged(object sender, EventArgs e)
        {
            // 设计模式下不处理
            if (IsDesignMode)
                return;
                
            try
            {
                // 如果页面变为可见，且启用了懒加载，且内容未加载，则加载内容
                if (this.Visible && EnableLazyLoading && !_isContentLoaded)
                {
                    _loggingService?.LogInformation($"页面首次可见，开始懒加载内容: {this.GetType().Name}", 
                        WaferAligner.EventIds.EventIds.Lazy_Load_Triggered);
                    
                    await LoadContentAsync();
                    
                    _isContentLoaded = true;
                    _loggingService?.LogInformation($"页面懒加载内容完成: {this.GetType().Name}", 
                        WaferAligner.EventIds.EventIds.Lazy_Load_Completed);
                }
                
                // 可见性变化时触发相应事件
                if (this.Visible)
                    await OnPageBecameVisibleAsync();
                else
                    await OnPageBecameInvisibleAsync();
            }
            catch (Exception ex)
            {
                _loggingService?.LogError(ex, $"页面可见性变化处理失败: {this.GetType().Name}", 
                    WaferAligner.EventIds.EventIds.Visible_Changed_Error);
            }
        }

        /// <summary>
        /// 安全地在UI线程执行操作
        /// </summary>
        /// <param name="action">要执行的操作</param>
        /// <returns>操作是否成功执行</returns>
        protected bool SafeInvoke(Action action)
        {
            if (IsDesignMode)
                return false;
                
            return this.SafeInvoke(action, _loggingService);
        }

        /// <summary>
        /// 异步安全地在UI线程执行操作
        /// </summary>
        protected Task<bool> SafeInvokeAsync(Action action)
        {
            if (IsDesignMode)
                return Task.FromResult(false);
                
            return this.SafeInvokeAsync(action, _loggingService);
        }

        /// <summary>
        /// 安全地设置控件文本
        /// </summary>
        protected bool SafeSetText(Control control, string text)
        {
            if (IsDesignMode)
                return false;
                
            return control.SafeSetText(text, _loggingService);
        }

        /// <summary>
        /// 注册需要管理的资源
        /// </summary>
        protected void RegisterResource(string name, IDisposable resource)
        {
            if (IsDesignMode)
                return;
                
            _resourceManager?.RegisterResource($"{this.GetType().Name}_{name}", resource);
        }

        /// <summary>
        /// 注册定时器
        /// </summary>
        protected void RegisterTimer(string name, System.Timers.Timer timer)
        {
            if (IsDesignMode)
                return;
                
            _resourceManager?.RegisterTimer($"{this.GetType().Name}_{name}", timer);
        }

        /// <summary>
        /// 注册BackgroundWorker，当页面关闭时自动取消
        /// </summary>
        [Obsolete("BackgroundWorker已全部迁移至Task-based异步模式，请使用RegisterResource注册Task或CancellationTokenSource", false)]
        protected void RegisterBackgroundWorker(string name, BackgroundWorker worker)
        {
            if (IsDesignMode)
                return;
            
            Logger?.LogWarning($"页面{this.GetType().Name}使用已过期的RegisterBackgroundWorker方法: {name}", 
                WaferAligner.EventIds.EventIds.Deprecated_Api_Usage);
            _resourceManager?.RegisterBackgroundWorker($"{this.GetType().Name}_{name}", worker);
        }

        /// <summary>
        /// 注册CancellationTokenSource，当页面关闭时自动取消并释放
        /// </summary>
        protected void RegisterCancellationTokenSource(string name, CancellationTokenSource cts)
        {
            if (IsDesignMode)
                return;
            
            _resourceManager?.RegisterResource($"{this.GetType().Name}_CTS_{name}", new CancellationTokenSourceWrapper(cts));
        }

        /// <summary>
        /// 创建定时器并自动注册到资源管理器
        /// </summary>
        /// <param name="name">定时器名称</param>
        /// <param name="interval">时间间隔(毫秒)</param>
        /// <returns>TimerWrapper实例</returns>
        protected TimerWrapper CreateTimer(string name, double interval)
        {
            if (IsDesignMode)
                return null;
                
            var timer = new TimerWrapper($"{this.GetType().Name}_{name}", interval, Logger);
            RegisterResource(name, timer);
            return timer;
        }
        
        /// <summary>
        /// 创建定时器并自动注册到资源管理器
        /// </summary>
        /// <param name="name">定时器名称</param>
        /// <param name="interval">时间间隔(毫秒)</param>
        /// <param name="handler">定时器事件处理方法</param>
        /// <returns>TimerWrapper实例</returns>
        protected TimerWrapper CreateTimer(string name, double interval, ElapsedEventHandler handler)
        {
            if (IsDesignMode)
                return null;
                
            var timer = new TimerWrapper($"{this.GetType().Name}_{name}", interval, Logger);
            if (handler != null)
            {
                timer.AddElapsedHandler((s, e) => {
                    SafeInvoke(() => handler(s, e));
                });
            }
            RegisterResource(name, timer);
            return timer;
        }
        
        /// <summary>
        /// 创建定时器并自动注册到资源管理器，支持异步方法
        /// </summary>
        /// <param name="name">定时器名称</param>
        /// <param name="interval">时间间隔(毫秒)</param>
        /// <param name="asyncHandler">异步定时器事件处理方法</param>
        /// <returns>TimerWrapper实例</returns>
        protected TimerWrapper CreateTimerWithAsyncHandler(string name, double interval, Action<object, System.Timers.ElapsedEventArgs> asyncHandler)
        {
            if (IsDesignMode)
                return null;
                
            var timer = new TimerWrapper($"{this.GetType().Name}_{name}", interval, Logger);
            if (asyncHandler != null)
            {
                timer.AddElapsedHandler((s, e) => {
                    SafeInvoke(() => asyncHandler(s, e));
                });
            }
            RegisterResource(name, timer);
            return timer;
        }
        
        /// <summary>
        /// 创建取消令牌源并自动注册到资源管理器
        /// </summary>
        /// <param name="name">取消令牌源名称</param>
        /// <returns>CancellationTokenSource实例</returns>
        protected CancellationTokenSource CreateCancellationTokenSource(string name)
        {
            if (IsDesignMode)
                return null;
                
            var cts = new CancellationTokenSource();
            RegisterCancellationTokenSource(name, cts);
            return cts;
        }
        
        /// <summary>
        /// 创建带超时的取消令牌源并自动注册到资源管理器
        /// </summary>
        /// <param name="name">取消令牌源名称</param>
        /// <param name="timeoutMs">超时时间(毫秒)</param>
        /// <returns>CancellationTokenSource实例</returns>
        protected CancellationTokenSource CreateCancellationTokenSource(string name, int timeoutMs)
        {
            if (IsDesignMode)
                return null;
                
            var cts = new CancellationTokenSource(timeoutMs);
            RegisterCancellationTokenSource(name, cts);
            return cts;
        }
        
        /// <summary>
        /// 创建链接到另一个取消令牌的取消令牌源并自动注册到资源管理器
        /// </summary>
        /// <param name="name">取消令牌源名称</param>
        /// <param name="token">链接的取消令牌</param>
        /// <returns>CancellationTokenSource实例</returns>
        protected CancellationTokenSource CreateLinkedCancellationTokenSource(string name, CancellationToken token)
        {
            if (IsDesignMode)
                return null;
                
            var cts = CancellationTokenSource.CreateLinkedTokenSource(token);
            RegisterCancellationTokenSource(name, cts);
            return cts;
        }

        /// <summary>
        /// CancellationTokenSource的包装类，实现IDisposable以便自动清理
        /// </summary>
        internal class CancellationTokenSourceWrapper : IDisposable
        {
            private readonly CancellationTokenSource _cts;
            
            public CancellationTokenSourceWrapper(CancellationTokenSource cts)
            {
                _cts = cts;
            }
            
            public void Dispose()
            {
                try
                {
                    if (_cts != null && !_cts.IsCancellationRequested)
                    {
                        _cts.Cancel();
                    }
                    _cts?.Dispose();
                }
                catch (ObjectDisposedException)
                {
                    // 已经被释放，忽略
                }
                catch (Exception)
                {
                    // 释放过程中的其他异常也忽略
                }
            }
        }

        /// <summary>
        /// 显示错误消息
        /// </summary>
        protected void ShowError(string message)
        {
            if (IsDesignMode)
                return;
                
            SafeInvoke(() =>
            {
                UIMessageBox.ShowError(message);
                _loggingService?.LogInformation($"显示错误消息: {message}");
            });
        }

        /// <summary>
        /// 显示信息消息
        /// </summary>
        protected void ShowInfo(string message)
        {
            if (IsDesignMode)
                return;
                
            SafeInvoke(() =>
            {
                UIMessageBox.ShowInfo(message);
                _loggingService?.LogInformation($"显示信息消息: {message}");
            });
        }

        /// <summary>
        /// 显示警告消息
        /// </summary>
        protected void ShowWarning(string message)
        {
            if (IsDesignMode)
                return;
                
            SafeInvoke(() =>
            {
                UIMessageBox.ShowWarning(message);
                _loggingService?.LogWarning($"显示警告消息: {message}");
            });
        }

        /// <summary>
        /// 显示确认对话框
        /// </summary>
        protected bool ShowConfirm(string message)
        {
            if (IsDesignMode)
                return false;
                
            if (!IsPageUsable())
                return false;

            bool result = false;
            if (this.InvokeRequired)
            {
                try
                {
                    // 再次检查表单状态
                    if (this.IsDisposed || this.Disposing || !this.IsHandleCreated)
                    {
                        _loggingService?.LogDebug($"表单已释放，无法显示确认对话框: {this.GetType().Name}");
                        return false;
                    }

                    this.Invoke(new Action(() =>
                    {
                        result = CommonFun.ShowAskDialog2(message);
                        _loggingService?.LogInformation($"显示确认对话框: {message}, 结果: {result}", WaferAligner.EventIds.EventIds.UI_Update_Failed);
                    }));
                }
                catch (ObjectDisposedException ex)
                {
                    _loggingService?.LogDebug($"表单已释放，无法显示确认对话框: {this.GetType().Name} - {ex.Message}");
                    return false;
                }
                catch (InvalidOperationException ex)
                {
                    _loggingService?.LogDebug($"表单句柄无效，无法显示确认对话框: {this.GetType().Name} - {ex.Message}");
                    return false;
                }
                catch (Exception ex)
                {
                    _loggingService?.LogWarning(ex, $"显示确认对话框时发生异常: {this.GetType().Name}", WaferAligner.EventIds.EventIds.UI_Update_Failed);
                    return false;
                }
            }
            else
            {
                result = CommonFun.ShowAskDialog2(message);
                _loggingService?.LogInformation($"显示确认对话框: {message}, 结果: {result}", WaferAligner.EventIds.EventIds.UI_Update_Failed);
            }
            return result;
        }

        /// <summary>
        /// 执行异步操作并统一处理异常
        /// </summary>
        protected async Task ExecuteAsync(Func<Task> action, string operationName = "操作")
        {
            if (IsDesignMode)
                return;
                
            try
            {
                await action();
            }
            catch (Exception ex)
            {
                _loggingService?.LogError(ex, $"{operationName}执行失败", WaferAligner.EventIds.EventIds.Unhandled_Exception);
                ShowError($"{operationName}失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 页面加载事件
        /// </summary>
        private async void BasePage_Load(object sender, EventArgs e)
        {
            // 设计模式下不执行初始化
            if (IsDesignMode)
                return;
                
            try
            {
                _loggingService?.LogDebug($"页面开始加载: {this.GetType().Name}", 
                    WaferAligner.EventIds.EventIds.Page_Load_Started);
                    
                if (_performanceMonitor != null)
                {
                    _performanceMonitor.StartOperation($"{this.GetType().Name}初始化");
                }
                
                await OnInitializeAsync();
                _isInitialized = true;
                
                // 如果不使用懒加载或页面已可见，则立即加载内容
                if (!EnableLazyLoading || this.Visible)
                {
                    await LoadContentAsync();
                    _isContentLoaded = true;
                }
                
                if (_performanceMonitor != null)
                {
                    _performanceMonitor.EndOperation();
                }
                
                _loggingService?.LogDebug($"页面加载完成: {this.GetType().Name}", 
                    WaferAligner.EventIds.EventIds.Page_Load_Completed);
            }
            catch (Exception ex)
            {
                _loggingService?.LogError(ex, $"页面初始化失败: {this.GetType().Name}", 
                    WaferAligner.EventIds.EventIds.Page_Initialize_Completed);
                ShowError($"页面初始化失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 父容器变化事件（用于检测页面移除）
        /// </summary>
        private void BasePage_ParentChanged(object sender, EventArgs e)
        {
            // 设计模式下不处理
            if (IsDesignMode)
                return;
                
            if (this.Parent == null && !_isDisposed)
            {
                // 关键修复：避免嵌套Invoke调用，直接调用DisposeInternal
                SafeInvoke(() =>
                {
                    _ = Task.Run(async () =>
                    {
                        try
                        {
                            // 等待100ms，避免在页面切换时过早释放资源
                            await Task.Delay(100);
                            
                            // 再次检查Parent是否为null
                            if (this.Parent == null && !_isDisposed)
                            {
                                DisposeInternal();
                            }
                        }
                        catch (Exception ex)
                        {
                            _loggingService?.LogError(ex, $"在ParentChanged事件中释放资源时发生异常: {this.GetType().Name}");
                        }
                    });
                });
            }
        }

        /// <summary>
        /// 子类重写此方法实现异步初始化逻辑
        /// </summary>
        protected virtual Task OnInitializeAsync()
        {
            return Task.CompletedTask;
        }
        
        /// <summary>
        /// 子类重写此方法实现懒加载逻辑
        /// </summary>
        protected virtual Task LoadContentAsync()
        {
            return Task.CompletedTask;
        }
        
        /// <summary>
        /// 子类重写此方法实现页面变为可见时的逻辑
        /// </summary>
        protected virtual Task OnPageBecameVisibleAsync()
        {
            return Task.CompletedTask;
        }
        
        /// <summary>
        /// 子类重写此方法实现页面变为不可见时的逻辑
        /// </summary>
        protected virtual Task OnPageBecameInvisibleAsync()
        {
            return Task.CompletedTask;
        }

        /// <summary>
        /// 子类重写此方法实现关闭前的清理逻辑
        /// </summary>
        /// <returns>返回true表示可以关闭，false表示取消关闭</returns>
        protected virtual Task<bool> OnClosingAsync()
        {
            return Task.FromResult(true);
        }

        /// <summary>
        /// 子类重写此方法实现自定义清理逻辑
        /// </summary>
        protected virtual void OnDispose()
        {
            // 子类可以重写此方法添加自定义清理逻辑
        }

        /// <summary>
        /// 清理资源的公共方法（向后兼容）
        /// </summary>
        public virtual void CleanUp()
        {
            _ = Task.Run(async () =>
            {
                try
                {
                    await OnClosingAsync();
                }
                finally
                {
                    // 关键修复：避免嵌套Invoke调用，直接调用DisposeInternal
                    SafeInvoke(() => DisposeInternal());
                }
            });
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public new void Dispose()
        {
            if (_isDisposed) return;

            // 终极修复：使用SafeInvoke替代直接Invoke，彻底避免ObjectDisposedException
            if (this.InvokeRequired)
            {
                // 使用SafeInvoke，它已经包含了所有必要的异常处理和状态检查
                bool invokeSuccess = this.SafeInvoke(DisposeInternal, _loggingService);
                
                if (!invokeSuccess)
                {
                    // SafeInvoke失败，直接标记为已释放
                    _isDisposed = true;
                    _loggingService?.LogDebug($"SafeInvoke DisposeInternal失败，直接标记为已释放: {this.GetType().Name}");
                }
            }
            else
            {
                // 已在UI线程，直接调用
                DisposeInternal();
            }
        }

        /// <summary>
        /// 内部释放资源方法（在UI线程执行）
        /// </summary>
        protected void DisposeInternal()
        {
            if (_isDisposed) return;

            _isDisposed = true;

            try
            {
                // 调用子类的清理方法（这可能包含Timer和资源清理）
                OnDispose();

                // 安全注销事件，防止在表单已释放时操作
                try
                {
                    // 检查表单状态再注销事件
                    if (!this.IsDisposed)
                    {
                        this.Load -= BasePage_Load;
                        this.ParentChanged -= BasePage_ParentChanged;
                        this.VisibleChanged -= BasePage_VisibleChanged;
                    }
                }
                catch (ObjectDisposedException)
                {
                    // 表单已释放，忽略事件注销
                    _loggingService?.LogDebug($"表单已释放，无法注销事件: {this.GetType().Name}");
                }
                catch (Exception eventEx)
                {
                    _loggingService?.LogWarning(eventEx, $"注销事件时发生异常: {this.GetType().Name}", WaferAligner.EventIds.EventIds.Resource_Released);
                }

                _loggingService?.LogDebug($"页面资源释放完成: {this.GetType().Name}");
            }
            catch (Exception ex)
            {
                _loggingService?.LogError(ex, $"页面资源释放失败: {this.GetType().Name}", WaferAligner.EventIds.EventIds.Resource_Released);
            }
            finally
            {
                // 最后调用基类Dispose，使用更安全的方式
                try
                {
                    if (!this.IsDisposed)
                    {
                        base.Dispose();
                    }
                }
                catch (ObjectDisposedException)
                {
                    // 基类已释放，这是正常情况
                    _loggingService?.LogDebug($"基类已释放: {this.GetType().Name}");
                }
                catch (Exception ex)
                {
                    _loggingService?.LogError(ex, $"基类资源释放失败: {this.GetType().Name}", WaferAligner.EventIds.EventIds.Resource_Released);
                }
            }
        }

        /// <summary>
        /// 检查页面是否可用
        /// </summary>
        protected bool IsPageUsable()
        {
            return !_isDisposed && !this.IsDisposed && this.IsHandleCreated;
        }

        /// <summary>
        /// 获取服务实例
        /// </summary>
        protected T GetService<T>() where T : class
        {
            try
            {
                return ServiceProvider?.GetService<T>();
            }
            catch (Exception ex)
            {
                _loggingService?.LogError(ex, $"获取服务失败: {typeof(T).Name}", WaferAligner.EventIds.EventIds.Unhandled_Exception);
                return null;
            }
        }

        /// <summary>
        /// 获取必需的服务实例
        /// </summary>
        protected T GetRequiredService<T>() where T : class
        {
            try
            {
                return ServiceProvider?.GetRequiredService<T>();
            }
            catch (Exception ex)
            {
                _loggingService?.LogError(ex, $"获取必需服务失败: {typeof(T).Name}", WaferAligner.EventIds.EventIds.Unhandled_Exception);
                throw;
            }
        }

        /// <summary>
        /// 批量获取多个服务，返回获取失败的服务类型列表
        /// </summary>
        protected List<Type> GetServices<T1, T2>(
            out T1 service1, out T2 service2)
            where T1 : class
            where T2 : class
        {
            List<Type> failedServices = new List<Type>();
            
            try { service1 = GetService<T1>(); } 
            catch (Exception) { service1 = null; failedServices.Add(typeof(T1)); }
            
            try { service2 = GetService<T2>(); } 
            catch (Exception) { service2 = null; failedServices.Add(typeof(T2)); }
            
            return failedServices;
        }
        
        /// <summary>
        /// 批量获取多个服务，返回获取失败的服务类型列表
        /// </summary>
        protected List<Type> GetServices<T1, T2, T3>(
            out T1 service1, out T2 service2, out T3 service3)
            where T1 : class
            where T2 : class
            where T3 : class
        {
            List<Type> failedServices = new List<Type>();
            
            try { service1 = GetService<T1>(); } 
            catch (Exception) { service1 = null; failedServices.Add(typeof(T1)); }
            
            try { service2 = GetService<T2>(); } 
            catch (Exception) { service2 = null; failedServices.Add(typeof(T2)); }
            
            try { service3 = GetService<T3>(); } 
            catch (Exception) { service3 = null; failedServices.Add(typeof(T3)); }
            
            return failedServices;
        }
        
        /// <summary>
        /// 批量获取多个服务，返回获取失败的服务类型列表
        /// </summary>
        protected List<Type> GetServices<T1, T2, T3, T4>(
            out T1 service1, out T2 service2, out T3 service3, out T4 service4)
            where T1 : class
            where T2 : class
            where T3 : class
            where T4 : class
        {
            List<Type> failedServices = new List<Type>();
            
            try { service1 = GetService<T1>(); } 
            catch (Exception) { service1 = null; failedServices.Add(typeof(T1)); }
            
            try { service2 = GetService<T2>(); } 
            catch (Exception) { service2 = null; failedServices.Add(typeof(T2)); }
            
            try { service3 = GetService<T3>(); } 
            catch (Exception) { service3 = null; failedServices.Add(typeof(T3)); }
            
            try { service4 = GetService<T4>(); } 
            catch (Exception) { service4 = null; failedServices.Add(typeof(T4)); }
            
            return failedServices;
        }
        
        /// <summary>
        /// 批量获取多个服务，返回获取失败的服务类型列表
        /// </summary>
        protected List<Type> GetServices<T1, T2, T3, T4, T5>(
            out T1 service1, out T2 service2, out T3 service3, out T4 service4, out T5 service5)
            where T1 : class
            where T2 : class
            where T3 : class
            where T4 : class
            where T5 : class
        {
            List<Type> failedServices = new List<Type>();
            
            try { service1 = GetService<T1>(); } 
            catch (Exception) { service1 = null; failedServices.Add(typeof(T1)); }
            
            try { service2 = GetService<T2>(); } 
            catch (Exception) { service2 = null; failedServices.Add(typeof(T2)); }
            
            try { service3 = GetService<T3>(); } 
            catch (Exception) { service3 = null; failedServices.Add(typeof(T3)); }
            
            try { service4 = GetService<T4>(); } 
            catch (Exception) { service4 = null; failedServices.Add(typeof(T4)); }
            
            try { service5 = GetService<T5>(); } 
            catch (Exception) { service5 = null; failedServices.Add(typeof(T5)); }
            
            return failedServices;
        }
        
        /// <summary>
        /// 批量获取多个服务，返回获取失败的服务类型列表
        /// </summary>
        protected List<Type> GetServices<T1, T2, T3, T4, T5, T6>(
            out T1 service1, out T2 service2, out T3 service3, out T4 service4, out T5 service5, out T6 service6)
            where T1 : class
            where T2 : class
            where T3 : class
            where T4 : class
            where T5 : class
            where T6 : class
        {
            List<Type> failedServices = new List<Type>();
            
            try { service1 = GetService<T1>(); } 
            catch (Exception) { service1 = null; failedServices.Add(typeof(T1)); }
            
            try { service2 = GetService<T2>(); } 
            catch (Exception) { service2 = null; failedServices.Add(typeof(T2)); }
            
            try { service3 = GetService<T3>(); } 
            catch (Exception) { service3 = null; failedServices.Add(typeof(T3)); }
            
            try { service4 = GetService<T4>(); } 
            catch (Exception) { service4 = null; failedServices.Add(typeof(T4)); }
            
            try { service5 = GetService<T5>(); } 
            catch (Exception) { service5 = null; failedServices.Add(typeof(T5)); }
            
            try { service6 = GetService<T6>(); } 
            catch (Exception) { service6 = null; failedServices.Add(typeof(T6)); }
            
            return failedServices;
        }
        
        /// <summary>
        /// 批量获取多个服务，返回获取失败的服务类型列表
        /// </summary>
        protected List<Type> GetServices<T1, T2, T3, T4, T5, T6, T7>(
            out T1 service1, out T2 service2, out T3 service3, out T4 service4, out T5 service5, out T6 service6, out T7 service7)
            where T1 : class
            where T2 : class
            where T3 : class
            where T4 : class
            where T5 : class
            where T6 : class
            where T7 : class
        {
            List<Type> failedServices = new List<Type>();
            
            try { service1 = GetService<T1>(); } 
            catch (Exception) { service1 = null; failedServices.Add(typeof(T1)); }
            
            try { service2 = GetService<T2>(); } 
            catch (Exception) { service2 = null; failedServices.Add(typeof(T2)); }
            
            try { service3 = GetService<T3>(); } 
            catch (Exception) { service3 = null; failedServices.Add(typeof(T3)); }
            
            try { service4 = GetService<T4>(); } 
            catch (Exception) { service4 = null; failedServices.Add(typeof(T4)); }
            
            try { service5 = GetService<T5>(); } 
            catch (Exception) { service5 = null; failedServices.Add(typeof(T5)); }
            
            try { service6 = GetService<T6>(); } 
            catch (Exception) { service6 = null; failedServices.Add(typeof(T6)); }
            
            try { service7 = GetService<T7>(); } 
            catch (Exception) { service7 = null; failedServices.Add(typeof(T7)); }
            
            return failedServices;
        }
    }
} 