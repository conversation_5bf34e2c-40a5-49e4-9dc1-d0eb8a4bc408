<Project Sdk="Microsoft.NET.Sdk">
	<PropertyGroup>
		<TargetFrameworks>net6.0-windows</TargetFrameworks>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
		<PackageReference Include="System.Reactive" Version="5.0.0" />
	</ItemGroup>
        <ItemGroup>
                <ProjectReference Include="..\..\..\Common\WaferAligner.Infrastructure.Extensions\WaferAligner.Infrastructure.Extensions.csproj" />
                <ProjectReference Include="..\..\..\Common\WaferAligner.Infrastructure.Logging\WaferAligner.Infrastructure.Logging.csproj" />
                <ProjectReference Include="..\..\..\src\Core\WaferAligner.EventIds\WaferAligner.EventIds.csproj" />
                <ProjectReference Include="..\WaferAligner.Services.Extensions\WaferAligner.Services.Extensions.csproj" />
                <ProjectReference Include="..\WaferAligner.Services.Abstractions\WaferAligner.Services.Abstractions.csproj" />
        </ItemGroup>
	<ItemGroup>
		<Compile Update="Properties\Resources.Designer.cs">
			<DesignTime>True</DesignTime>
			<AutoGen>True</AutoGen>
			<DependentUpon>Resources.resx</DependentUpon>
		</Compile>
	</ItemGroup>
	<ItemGroup>
		<EmbeddedResource Update="Properties\Resources.resx">
			<Generator>ResXFileCodeGenerator</Generator>
			<LastGenOutput>Resources.Designer.cs</LastGenOutput>
		</EmbeddedResource>
	</ItemGroup>
	<ItemGroup>
		<None Update="system_configuration.json">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
	</ItemGroup>
</Project>
