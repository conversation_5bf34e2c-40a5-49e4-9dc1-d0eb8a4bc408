﻿// using AlignerUI; // 已迁移到WaferAligner.Models
using WaferAligner.Communication.Inovance;
using Sunny.UI;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Text.RegularExpressions;
using WaferAligner.Communication.Inovance.Abstractions;
using System.Runtime.CompilerServices;
using Sunny.UI.Demo;
using static System.Windows.Forms.AxHost;
using System.Security.Cryptography;
using WaferAligner.Common;
using WaferAligner.Services.Abstractions;
using WaferAligner.Services.Extensions;
using System.Threading;
using System.Drawing;
using System.Windows.Forms;
using System.Resources;
using Microsoft.Extensions.DependencyInjection;
using WaferAligner.Services;
using WaferAligner.Infrastructure.Common;
using WaferAligner.Interfaces;

namespace WaferAligner
{
    public partial class AxisControl : InstrumnetControl
    {
        #region 声明变量
        private int _id;
        private string _ReadyState;
        private string _AlarmState;

        private string _EnableAddress;
        private string _EnableState;

        private string _ZeroAddress;
        private string _ZeroState;

        private string _VeloAddress;
        private string _TargetPos;
        private string _MoveAbsAddress;
        private string _StopAddress;
        private string _MoveAbsState;
        private string _CurrentPos;
        private string _CurrentSpeed;

        private string _JogVelAddress;
        private string _JOGFront;
        private string _JOGBack;


        private double _PosMax;
        private double _PosMin;
        private double _Pos;
        private double _SpeedMax;
        private double _SpeedMin;
        private double _Speed;
        private double _JogSpeedMax;
        private double _JogSpeedMin;
        private double _JogSpeed;

        // 添加服务接口字段
        private ILoggingService _loggingService;
        private IAxisEventService _axisEventService;
        private IPlcVariableService _plcVariableService;
        private IPlcConnectionManager _plcConnectionManager;
        private IAxisViewModelFactory _axisFactory;
        private ICylinderService _cylinderService;
        private WaferAligner.Infrastructure.Common.ResourceManager _resourceManager;
        private IStatusUpdateService _statusUpdateService;

        #endregion 声明变量

        #region  定义属性
        [Category("自定义属性")]
        [Description("步进电机序号")]
        public int ID
        {
            get { return _id; }
            set { _id = value; }
        }

        [Category("自定义属性")]
        [Description("步进电机位置描述")]
        public string V_Test
        {
            get { return lalZTextName.Text; }
            set { lalZTextName.Text = value; }
        }

        [Category("自定义属性")]
        [Description("步进电机准备状态")]
        public string ReadyState
        {
            get { return _ReadyState; }
            set { _ReadyState = value; }
        }
        [Category("自定义属性")]
        [Description("步进电机报警状态")]
        public string AlarmState
        {
            get { return _AlarmState; }
            set { _AlarmState = value; }
        }

        [Category("自定义属性")]
        [Description("步进电机使能变量")]
        public string EnableAddress
        {
            get { return _EnableAddress; }
            set { _EnableAddress = value; }
        }
        [Category("自定义属性")]
        [Description("步进电机使能状态")]
        public string EnableState
        {
            get { return _EnableState; }
            set { _EnableState = value; }
        }

        [Category("自定义属性")]
        [Description("步进电机回零变量")]
        public string ZeroAddress
        {
            get { return _ZeroAddress; }
            set { _ZeroAddress = value; }
        }
        [Category("自定义属性")]
        [Description("步进电机回零状态")]
        public string ZeroState
        {
            get { return _ZeroState; }
            set { _ZeroState = value; }
        }

        [Category("自定义属性")]
        [Description("步进电机目标速度")]
        public string JogVelAddress
        {
            get { return _JogVelAddress; }
            set { _JogVelAddress = value; }
        }
        [Category("自定义属性")]
        [Description("步进电机目标位置")]
        public string TargetPos
        {
            get { return _TargetPos; }
            set { _TargetPos = value; }
        }
        [Category("自定义属性")]
        [Description("步进电机绝对运动变量")]
        public string MoveAbsAddress
        {
            get { return _MoveAbsAddress; }
            set { _MoveAbsAddress = value; }
        }
        [Category("自定义属性")]
        [Description("步进电机停止变量")]
        public string StopAddress
        {
            get { return _StopAddress; }
            set { _StopAddress = value; }
        }
        [Category("自定义属性")]
        [Description("步进电机绝对跑位状态")]
        public string MoveAbsState
        {
            get { return _MoveAbsState; }
            set { _MoveAbsState = value; }
        }
        [Category("自定义属性")]
        [Description("步进电机当前位置")]
        public string CurrentPos
        {
            get { return _CurrentPos; }
            set { _CurrentPos = value; }
        }

        [Category("自定义属性")]
        [Description("步进电机当前速度")]
        public string CurrentSpeed
        {
            get { return _CurrentSpeed; }
            set { _CurrentSpeed = value; }
        }

        [Category("自定义属性")]
        [Description("步进电机JOG速度地址")]
        public string VeloAddress
        {
            get { return _VeloAddress; }
            set { _VeloAddress = value; }
        }
        [Category("自定义属性")]
        [Description("步进电机JOG前进")]
        public string JOGFront
        {
            get { return _JOGFront; }
            set { _JOGFront = value; }
        }
        [Category("自定义属性")]
        [Description("步进电机JOG后退")]
        public string JOGBack
        {
            get { return _JOGBack; }
            set { _JOGBack = value; }
        }


        [Category("自定义属性")]
        [Description("步进电机限速max")]
        public double SpeedMax
        {
            get { return _SpeedMax; }
            set
            {
                // 使用轴工厂替代兼容性服务
                if (_axisFactory != null)
                {
                    try
                    {
                        string axisName = IdToName(_id);
                        switch (_id)
                        {
                            case 0: // X轴
                                {
                                    var xAxis = _axisFactory.GetXAxisViewModel();
                                    if (xAxis != null)
                                    {
                                        _ = SetRunSpeedForXAxisAsync(value);
                                    }
                                }
                                break;
                            case 1: // Y轴
                                {
                                    var yAxis = _axisFactory.GetYAxisViewModel();
                                    if (yAxis != null)
                                    {
                                        _ = SetRunSpeedForYAxisAsync(value);
                                    }
                                }
                                break;
                            case 2: // R轴
                                {
                                    var rAxis = _axisFactory.GetRAxisViewModel();
                                    if (rAxis != null)
                                    {
                                        _ = SetRunSpeedForRAxisAsync(value);
                                    }
                                }
                                break;
                            // 其他轴的处理...
                        }
                    }
                    catch (Exception ex)
                    {
                        _loggingService?.LogError(ex, $"设置轴{_id}最大速度失败: {ex.Message}", WaferAligner.EventIds.EventIds.Axis_Speed_Set_Failed);
                    }
                }
                TxtTargetVel.Maximum = _SpeedMax = value;
            }
        }

        // 新增的异步辅助方法
        private async Task SetRunSpeedForXAxisAsync(double value)
        {
            try
            {
                var xAxis = _axisFactory?.GetXAxisViewModel();
                if (xAxis != null)
                {
                    await xAxis.SetRunSpeedAsync(value * AxisConstants.AXIS_XY_MULTIPLE_CONVERTION);
                    _loggingService?.LogDebug($"X轴运行速度设置成功: {value}", WaferAligner.EventIds.EventIds.Axis_Speed_Set);
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError(ex, $"设置X轴运行速度时发生错误: {ex.Message}", WaferAligner.EventIds.EventIds.Axis_Speed_Set_Failed);
            }
        }

        private async Task SetRunSpeedForYAxisAsync(double value)
        {
            try
            {
                var yAxis = _axisFactory?.GetYAxisViewModel();
                if (yAxis != null)
                {
                    await yAxis.SetRunSpeedAsync(value * AxisConstants.AXIS_XY_MULTIPLE_CONVERTION);
                    _loggingService?.LogDebug($"Y轴运行速度设置成功: {value}", WaferAligner.EventIds.EventIds.Axis_Speed_Set);
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError(ex, $"设置Y轴运行速度时发生错误: {ex.Message}", WaferAligner.EventIds.EventIds.Axis_Speed_Set_Failed);
            }
        }

        private async Task SetRunSpeedForRAxisAsync(double value)
        {
            try
            {
                var rAxis = _axisFactory?.GetRAxisViewModel();
                if (rAxis != null)
                {
                    await rAxis.SetRunSpeedAsync(value * AxisConstants.AXIS_R_MULTIPLE_CONVERTION);
                    _loggingService?.LogDebug($"R轴运行速度设置成功: {value}", WaferAligner.EventIds.EventIds.Axis_Speed_Set);
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError(ex, $"设置R轴运行速度时发生错误: {ex.Message}", WaferAligner.EventIds.EventIds.Axis_Speed_Set_Failed);
            }
        }

        [Category("自定义属性")]
        [Description("步进电机限速min")]
        public double SpeedMin
        {
            get { return _SpeedMin; }
            set 
            { 
                // 使用轴工厂替代兼容性服务
                if (_axisFactory != null)
                {
                    try
                    {
                        // 不需要特别设置最小速度，只保存到本地属性
                        _SpeedMin = value;
                    }
                    catch (Exception ex)
                    {
                        _loggingService?.LogError(ex, $"设置轴{_id}最小速度失败: {ex.Message}", WaferAligner.EventIds.EventIds.Axis_Speed_Set_Failed);
                    }
                }
                TxtTargetVel.Minimum = _SpeedMin = value; 
            }
        }

        [Category("自定义属性")]
        [Description("步进电机速度")]
        public double Speed
        {
            get { return _Speed; }
            set
            {
                //TxtTargetVel.Text = Convert.ToString(value);
                _Speed = value;
            }
        }
        [Category("自定义属性")]
        [Description("步进电机限位max")]
        public double PosMax
        {
            get { return _PosMax; }
            set 
            { 
                // 使用轴工厂替代兼容性服务
                if (_axisFactory != null)
                {
                    try
                    {
                        // 设置轴的正限位值，暂时保存到本地属性，轴需要实际使用时才会被应用
                        _PosMax = value;
                    }
                    catch (Exception ex)
                    {
                        _loggingService?.LogError(ex, $"设置轴{_id}正向限位失败: {ex.Message}", WaferAligner.EventIds.EventIds.Axis_Operation_Info);
                    }
                }
                TxtTargetPos.Maximum = _PosMax = value; 
            }
        }
        [Category("自定义属性")]
        [Description("步进电机限位min")]
        public double PosMin
        {
            get { return _PosMin; }
            set 
            { 
                // 使用轴工厂替代兼容性服务
                if (_axisFactory != null)
                {
                    try
                    {
                        // 设置轴的负限位值，暂时保存到本地属性，轴需要实际使用时才会被应用
                        _PosMin = value;
                    }
                    catch (Exception ex)
                    {
                        _loggingService?.LogError(ex, $"设置轴{_id}负向限位失败: {ex.Message}", WaferAligner.EventIds.EventIds.Axis_Operation_Info);
                    }
                }
                TxtTargetPos.Minimum = _PosMin = value; 
            }
        }
        [Category("自定义属性")]
        [Description("步进电机目标位置")]
        public double Pos
        {
            get { return _Pos; }
            set
            {
                _Pos = value;
                //TxtTargetPos.Text = _Pos.ToString();

            }
        }
        [Category("自定义属性")]
        [Description("步进电机限JOG速max")]
        public double JogSpeedMax
        {
            get { return _JogSpeedMax; }
            set { TxtJogVel.Maximum = _JogSpeedMax = value; }
        }
        [Category("自定义属性")]
        [Description("步进电机限JOG速min")]
        public double JogSpeedMin
        {
            get { return _JogSpeedMin; }
            set { TxtJogVel.Minimum = _JogSpeedMin = value; }
        }
        [Category("自定义属性")]
        [Description("步进电机速度")]
        public double JogSpeed
        {
            get { return _JogSpeed; }
            set
            {
                //TxtJogVel.Text = Convert.ToString(value);
                _JogSpeed = value;
            }
        }

        public Color Ready_Color
        {
            get { return LED_Ready.LEDColor; }
            set { LED_Ready.LEDColor = value; }
        }
        public Color Alarm_Color
        {
            get { return LED_Alarm.LEDColor; }
            set { LED_Alarm.LEDColor = value; }
        }
        public Color Enable_Color
        {
            get { return LED_Enable.LEDColor; }
            set { LED_Enable.LEDColor = value; }
        }
        public Color Zero_Color
        {
            get { return LED_Zero.LEDColor; }
            set { LED_Zero.LEDColor = value; }
        }
        public MyLED Zero_State
        {
            get { return LED_Zero; }
            set { LED_Zero = value; }
        }
        public Color PosComplete_Color
        {
            get { return LED_PosComplete.LEDColor; }
            set { LED_PosComplete.LEDColor = value; }
        }
        public bool PosComplete_Bool
        {
            get { return LED_PosComplete.LedStatus; }
            set { LED_PosComplete.LedStatus = value; }
        }
        public string CurenttPos
        {
            get { return TxtCurentPos.Text; }
            set { TxtCurentPos.Text = value; }
        }
        public string CurenttVel
        {
            get { return TxtCurentVel.Text; }
            set { TxtCurentVel.Text = value; }
        }
        public string JogVel
        {
            get { return TxtJogVel.Text; }
            set { TxtJogVel.Text = value; }
        }
        public bool BtnZeroEnable
        {
            get { return BtnZero.Enabled; }
            set { BtnZero.Enabled = value; }
        }
        #endregion 定义属性


        private TimerWrapper _timer;
        // 添加数据缓存字段
        private string _lastPositionText = "";
        private string _lastVelocityText = "";
        private bool _lastReadyState = false;
        private bool _lastEnableState = false;
        private bool _lastAlarmState = false;
        private bool _lastPosCompleteState = false;
        private DateTime _lastUpdateTime = DateTime.MinValue;
        private const int UPDATE_INTERVAL_MS = 200; // 增加更新间隔到200ms
        
        // 性能监控字段（可选）
        private int _updateCount = 0;
        private DateTime _lastPerformanceCheck = DateTime.Now;
        private const int PERFORMANCE_CHECK_INTERVAL_MS = 5000; // 每5秒检查一次性能
        
        // 添加清理标志，用于防止在清理过程中执行UI操作
        private volatile bool _isCleaningUp = false;
        
        #region 构造函数与初始化

        /// <summary>
        /// 设计器使用的构造函数
        /// </summary>
        public AxisControl(IContainer container) : base()
        {
            if (container != null)
            {
                container.Add(this);
            }
            InitializeComponent();
            // 设计器模式下直接返回，避免依赖注入
            if (LicenseManager.UsageMode == LicenseUsageMode.Designtime)
                return;
            // host/Services未就绪时也直接返回
            if (CommonFun.host?.Services == null)
                return;
            // 其余依赖注入、服务获取等全部延后到Load事件或显式初始化
        }

        /// <summary>
        /// 标准构造函数
        /// </summary>
        public AxisControl() : base()
        {
            InitializeComponent();
            // 仅供设计器使用，不做任何服务相关操作
        }

        public void InitServices(
            ILoggingService loggingService,
            IAxisEventService axisEventService,
            IPlcVariableService plcVariableService,
            IPlcConnectionManager plcConnectionManager,
            IAxisViewModelFactory axisFactory,
            ICylinderService cylinderService
        )
        {
            _loggingService = loggingService;
            _axisEventService = axisEventService;
            _plcVariableService = plcVariableService;
            _plcConnectionManager = plcConnectionManager;
            _axisFactory = axisFactory;
            _cylinderService = cylinderService;
        }
        
        /// <summary>
        /// 初始化服务（包括ResourceManager）
        /// </summary>
        public void InitServices(
            ILoggingService loggingService,
            IAxisEventService axisEventService,
            IPlcVariableService plcVariableService,
            IPlcConnectionManager plcConnectionManager,
            IAxisViewModelFactory axisFactory,
            ICylinderService cylinderService,
            WaferAligner.Infrastructure.Common.ResourceManager resourceManager
        )
        {
            _loggingService = loggingService;
            _axisEventService = axisEventService;
            _plcVariableService = plcVariableService;
            _plcConnectionManager = plcConnectionManager;
            _axisFactory = axisFactory;
            _cylinderService = cylinderService;
            _resourceManager = resourceManager;
        }



        /// <summary>
        /// 依赖注入构造函数，用于手动注入服务
        /// </summary>
        public AxisControl(
            ILoggingService loggingService,
            IAxisEventService axisEventService,
            IPlcVariableService plcVariableService,
            IPlcConnectionManager plcConnectionManager,
            IAxisViewModelFactory axisFactory,
            ICylinderService cylinderService,
            IStatusUpdateService statusUpdateService)
        {
            InitializeComponent();
            
            _loggingService = loggingService;
            _axisEventService = axisEventService;
            _plcVariableService = plcVariableService;
            _plcConnectionManager = plcConnectionManager;
            _axisFactory = axisFactory;
            _cylinderService = cylinderService;
            _statusUpdateService = statusUpdateService;
            
            GetInformation();
            StateWatch();
            InitializeTimer();
        }

        /// <summary>
        /// 依赖注入构造函数，用于手动注入服务（包括ResourceManager）
        /// </summary>
        public AxisControl(
            ILoggingService loggingService,
            IAxisEventService axisEventService,
            IPlcVariableService plcVariableService,
            IPlcConnectionManager plcConnectionManager,
            IAxisViewModelFactory axisFactory,
            ICylinderService cylinderService,
            WaferAligner.Infrastructure.Common.ResourceManager resourceManager,
            IStatusUpdateService statusUpdateService)
        {
            InitializeComponent();
            
            _loggingService = loggingService;
            _axisEventService = axisEventService;
            _plcVariableService = plcVariableService;
            _plcConnectionManager = plcConnectionManager;
            _axisFactory = axisFactory;
            _cylinderService = cylinderService;
            _resourceManager = resourceManager;
            _statusUpdateService = statusUpdateService;
            
            GetInformation();
            StateWatch();
            InitializeTimer();
        }

        #endregion

        public void CleanUp()
        {
            try
            {
                // 0. 标记控件正在清理中，防止新的UI更新
                _isCleaningUp = true;
                
                // 1. 停止并释放定时器
                if (_timer != null)
                {
                    try
                    {
                        _timer.Stop();
                        _timer.RemoveElapsedHandler(_Timer_Tick);
                        _timer.Dispose();
                    }
                    catch (ObjectDisposedException)
                    {
                        // 定时器已被释放，忽略
                        _loggingService?.LogDebug($"轴{_id}定时器已被释放，跳过清理", WaferAligner.EventIds.EventIds.Resource_Released);
                    }
                    _timer = null;
                }
                
                // 2. 取消所有异步操作
                if (_zeroCts != null)
                {
                    try
                    {
                        _zeroCts.Cancel();
                        _zeroCts.Dispose();
                        _zeroCts = null;
                    }
                    catch (ObjectDisposedException)
                    {
                        _loggingService?.LogDebug($"轴{_id}归零取消令牌已被释放，跳过清理", WaferAligner.EventIds.EventIds.Resource_Released);
                    }
                    catch (Exception ex)
                    {
                        _loggingService?.LogError(ex, $"取消归零操作时出错: {ex.Message}", WaferAligner.EventIds.EventIds.Resource_Released);
                    }
                }
                
                if (_posCts != null)
                {
                    try
                    {
                        _posCts.Cancel();
                        _posCts.Dispose();
                        _posCts = null;
                    }
                    catch (ObjectDisposedException)
                    {
                        _loggingService?.LogDebug($"轴{_id}定位取消令牌已被释放，跳过清理", WaferAligner.EventIds.EventIds.Resource_Released);
                    }
                    catch (Exception ex)
                    {
                        _loggingService?.LogError(ex, $"取消定位操作时出错: {ex.Message}", WaferAligner.EventIds.EventIds.Resource_Released);
                    }
                }
                
                // 3. 注销所有PLC事件处理程序
                try
                {
                    if (_axisEventService != null)
                    {
                        string axisName = IdToName(_id);
                        if (!string.IsNullOrEmpty(axisName))
                        {
                            // 注销所有已注册的事件 - 不使用ConfigureAwait(false)，确保同步执行
                            if (!string.IsNullOrEmpty(_CurrentPos))
                                _axisEventService.UnregisterAxisEventAsync(axisName, $"{AxisConstants.AXIS_GVL}.{_CurrentPos}");
                            
                            if (!string.IsNullOrEmpty(_CurrentSpeed))
                                _axisEventService.UnregisterAxisEventAsync(axisName, $"{AxisConstants.AXIS_GVL}.{_CurrentSpeed}");
                            
                            if (!string.IsNullOrEmpty(_ReadyState))
                                _axisEventService.UnregisterAxisEventAsync(axisName, $"{AxisConstants.AXIS_GVL}.{_ReadyState}");
                            
                            if (!string.IsNullOrEmpty(_EnableState))
                                _axisEventService.UnregisterAxisEventAsync(axisName, $"{AxisConstants.AXIS_GVL}.{_EnableState}");
                            
                            if (!string.IsNullOrEmpty(_ZeroState))
                                _axisEventService.UnregisterAxisEventAsync(axisName, $"{AxisConstants.AXIS_GVL}.{_ZeroState}");
                            
                            if (!string.IsNullOrEmpty(_AlarmState))
                                _axisEventService.UnregisterAxisEventAsync(axisName, $"{AxisConstants.AXIS_GVL}.{_AlarmState}");
                            
                            if (!string.IsNullOrEmpty(_MoveAbsState))
                                _axisEventService.UnregisterAxisEventAsync(axisName, $"{AxisConstants.AXIS_GVL}.{_MoveAbsState}");
                            
                            _loggingService?.LogDebug($"轴{_id}({axisName})事件注销成功", WaferAligner.EventIds.EventIds.Resource_Released);
                        }
                    }
                    
                    // 清除变量更改动作字典
                    VariableChangeActions?.Clear();
                }
                catch (Exception ex)
                {
                    _loggingService?.LogError(ex, $"注销PLC事件时出错: {ex.Message}", WaferAligner.EventIds.EventIds.Resource_Released);
                }
                
                _loggingService?.LogDebug($"AxisControl {_id} cleanup completed", WaferAligner.EventIds.EventIds.Resource_Released);
                
            }
            catch (Exception ex)
            {
                _loggingService?.LogError(ex, $"CleanUp error: {ex.Message}", WaferAligner.EventIds.EventIds.Resource_Released);
            }
            finally
            {
                // 清理完成后重置标志，允许后续操作（如果有）
                _isCleaningUp = false;
            }
        }


        #region  load
        private async void 步进电机调试_Load(object sender, EventArgs e)
        {
            try
            {
                // 让UI先完成渲染，然后再执行初始化
                await Task.Yield();

                // 检查是否为PLC轴，如果是且PLC未连接，则延迟初始化
                if (_id > 2) // Z轴和相机轴
                {
                    string plcConnectionName = GetPlcNameFromAxisId(_id);
                    bool isPlcConnected = IsPlcReallyConnected(plcConnectionName);

                    if (!isPlcConnected)
                    {
                        _loggingService?.LogDebug($"轴{_id}的PLC连接({plcConnectionName})未就绪，延迟初始化", WaferAligner.EventIds.EventIds.Control_Initialization_Failed);

                        // 只设置基本UI，不进行可能阻塞的操作
                        SetBasicUI();

                        // 在后台定期检查PLC连接状态，连接后再完成初始化
                        _ = Task.Run(async () => {
                            await WaitForPlcAndInitialize(plcConnectionName);
                        });

                        return;
                    }
                }

                // PLC已连接或串口轴，正常初始化
                await PerformFullInitialization();
            }
            catch (Exception ex)
            {
                _loggingService?.LogError(ex, $"轴{_id}Load事件处理失败", WaferAligner.EventIds.EventIds.Control_Initialization_Failed);
            }
        }

        private void SetBasicUI()
        {
            try
            {
                // 设置基本的UI显示，不涉及轴通信
                if (TxtTargetPos != null) TxtTargetPos.Text = "0.000";
                if (TxtCurentPos != null) TxtCurentPos.Text = "0.000";
                if (TxtTargetVel != null) TxtTargetVel.Text = "0.000";
                if (TxtJogVel != null) TxtJogVel.Text = "0.000";
                if (TxtCurentVel != null) TxtCurentVel.Text = "0.000";

                // 设置默认的限位值
                switch (_id)
                {
                    case 3: // Z轴
                        UnitOfLal("mm", "mm/s");
                        break;
                    case 4: case 5: case 7: case 8: // LX, LY, RX, RY
                        UnitOfLal("mm", "mm/s");
                        break;
                    case 6: case 9: // LZ, RZ
                        UnitOfLal("mm", "mm/s");
                        break;
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError(ex, $"轴{_id}设置基本UI失败", WaferAligner.EventIds.EventIds.Control_Initialization_Failed);
            }
        }

        private async Task WaitForPlcAndInitialize(string plcConnectionName)
        {
            try
            {
                // 等待PLC连接，最多等待60秒（增加等待时间，适应实际硬件启动时间）
                int maxWaitSeconds = 60;
                int waitedSeconds = 0;

                _loggingService?.LogInformation($"轴{_id}开始等待PLC连接({plcConnectionName})，最多等待{maxWaitSeconds}秒", WaferAligner.EventIds.EventIds.Control_Initialization_In_Progress);

                while (waitedSeconds < maxWaitSeconds)
                {
                    await Task.Delay(1000); // 每秒检查一次
                    waitedSeconds++;

                    bool isConnected = IsPlcReallyConnected(plcConnectionName);
                    if (isConnected)
                    {
                        _loggingService?.LogInformation($"轴{_id}的PLC连接({plcConnectionName})已就绪，开始完整初始化", WaferAligner.EventIds.EventIds.Control_Initialization_Completed);

                        // 在UI线程上执行完整初始化
                        this.SafeInvoke(async () => {
                            await PerformFullInitialization();
                        });
                        return;
                    }

                    // 每10秒输出一次等待状态，避免日志过多
                    if (waitedSeconds % 10 == 0)
                    {
                        _loggingService?.LogDebug($"轴{_id}仍在等待PLC连接({plcConnectionName})，已等待{waitedSeconds}秒", WaferAligner.EventIds.EventIds.Control_Initialization_In_Progress);
                    }
                }

                _loggingService?.LogWarning($"轴{_id}等待PLC连接({plcConnectionName})超时({maxWaitSeconds}秒)，继续使用基本模式", WaferAligner.EventIds.EventIds.Control_Initialization_Failed);
            }
            catch (Exception ex)
            {
                _loggingService?.LogError(ex, $"轴{_id}等待PLC连接失败", WaferAligner.EventIds.EventIds.Control_Initialization_Failed);
            }
        }

        private async Task PerformFullInitialization()
        {
            try
            {
                // 在UI线程上执行GetInformation，因为它需要访问UI控件
                GetInformation();

                // 立即初始化状态监控，确保事件注册及时完成
                StateWatch();

                // 稍微延迟启动定时器，让初始化完成
                await Task.Delay(50);
                InitializeTimer();
            }
            catch (Exception ex)
            {
                _loggingService?.LogError(ex, $"轴{_id}完整初始化失败", WaferAligner.EventIds.EventIds.Control_Initialization_Failed);
            }
        }

        private void InitializeTimer()
        {
            try
            {
                // 使用TimerWrapper替代System.Timers.Timer
                string timerName = $"AxisControl_{_id}_UpdateTimer";
                _timer = new TimerWrapper(timerName, UPDATE_INTERVAL_MS, _Timer_Tick, _loggingService);

                // 启用状态跟踪以监控性能
                _timer.EnableStateTracking();

                // 设置重试行为
                _timer.SetRetryBehavior(3, 50); // 最多重试3次，间隔50ms

                // 启动定时器
                _timer.Start();

                // 注册到ResourceManager
                if (_resourceManager != null)
                {
                    _resourceManager.RegisterResource(timerName, _timer);
                    _loggingService?.LogDebug($"轴{_id}定时器已注册到ResourceManager", WaferAligner.EventIds.EventIds.Resource_Registered);
                }
                else
                {
                    _loggingService?.LogWarning($"无法获取ResourceManager，轴{_id}定时器未注册", WaferAligner.EventIds.EventIds.Resource_Registered);
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError(ex, $"初始化定时器异常 - {ex.Message}", WaferAligner.EventIds.EventIds.Service_Initialization_Failed);
            }
        }

        private async void StateWatch()
        {
            try
            {
                if (_axisEventService == null)
                {
                    _loggingService?.LogWarning("_axisEventService为null", WaferAligner.EventIds.EventIds.State_Watch_Init_Failed);
                    return;
                }
                
                if (_plcVariableService == null)
                {
                    _loggingService?.LogWarning("_plcVariableService为null", WaferAligner.EventIds.EventIds.State_Watch_Init_Failed);
                    return;
                }
                
                if (_axisFactory == null)
                {
                    _loggingService?.LogWarning("_axisFactory为null", WaferAligner.EventIds.EventIds.State_Watch_Init_Failed);
                    return;
                }
                
                string axisName = IdToName(_id);
                if (string.IsNullOrEmpty(axisName))
                {
                    if (_loggingService != null)
                    {
                        _loggingService.LogWarning($"未能确定轴名称 ID={_id}", WaferAligner.EventIds.EventIds.State_Watch_Init_Failed);
                    }
                    return;
                }
                
                // 注册轴事件
                if (_id > 2) // Z、LX、LY、LZ、RX、RY、RZ轴
                {
                    // 检查对应的PLC连接状态，避免网络超时
                    string plcConnectionName = GetPlcNameFromAxisId(_id);
                    bool isPlcConnected = IsPlcReallyConnected(plcConnectionName);

                    if (!isPlcConnected)
                    {
                        _loggingService?.LogDebug($"轴{_id}({axisName})的PLC连接({plcConnectionName})未就绪，跳过事件注册", WaferAligner.EventIds.EventIds.State_Watch_Init_Failed);
                        return; // 跳过事件注册，避免网络超时
                    }

                    try
                    {
                        // 监听当前位置变化
                        if (!string.IsNullOrEmpty(_CurrentPos) && this.IsHandleCreated && !this.IsDisposed)
                        {
                            await _axisEventService.RegisterAxisEventAsync(axisName, 
                                $"{AxisConstants.AXIS_GVL}.{_CurrentPos}", 
                                (obj) =>
                                {
                                    // 使用弱引用检查，避免在控件释放后仍然尝试更新UI
                                    if (_isCleaningUp || this.IsDisposed || this.Disposing || !this.IsHandleCreated)
                                        return;
                                        
                                    this.SafeInvoke(() =>
                                    {
                                        if (this.IsHandleCreated && !this.IsDisposed)
                                        {
                                            if (_id == 6 || _id == 9) // LZ或RZ轴
                                                TxtCurentPos.Text = string.Format("{0:0.000}", Convert.ToDouble(obj));
                                            else
                                                TxtCurentPos.Text = string.Format("{0:0.000}", 
                                                    Convert.ToDouble(obj) / AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION);
                                        }
                                    });
                                });
                        }
                        
                        // 监听当前速度变化
                        if (!string.IsNullOrEmpty(_CurrentSpeed) && this.IsHandleCreated && !this.IsDisposed)
                        {
                            await _axisEventService.RegisterAxisEventAsync(axisName, 
                                $"{AxisConstants.AXIS_GVL}.{_CurrentSpeed}", 
                                (obj) =>
                                {
                                    // 安全检查，避免在控件释放后更新UI
                                    if (_isCleaningUp || this.IsDisposed || this.Disposing || !this.IsHandleCreated)
                                        return;
                                        
                                    this.SafeInvoke(() =>
                                    {
                                        if (this.IsHandleCreated && !this.IsDisposed)
                                        {
                                            if (_id == 6 || _id == 9) // LZ或RZ轴
                                                TxtCurentVel.Text = string.Format("{0:0.000}", Convert.ToDouble(obj));
                                            else
                                                TxtCurentVel.Text = string.Format("{0:0.000}", 
                                                    Convert.ToDouble(obj) / AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION);
                                        }
                                    });
                                });
                        }
                        
                        // 监听就绪状态变化
                        if (!string.IsNullOrEmpty(_ReadyState) && this.IsHandleCreated && !this.IsDisposed)
                        {
                            await _axisEventService.RegisterAxisEventAsync(axisName, 
                                $"{AxisConstants.AXIS_GVL}.{_ReadyState}", 
                                (obj) =>
                                {
                                    // 安全检查，避免在控件释放后更新UI
                                    if (_isCleaningUp || this.IsDisposed || this.Disposing || !this.IsHandleCreated)
                                        return;
                                        
                                    this.SafeInvoke(() =>
                                    {
                                        if (this.IsHandleCreated && !this.IsDisposed && LED_Ready != null)
                                        {
                                            LED_Ready.LedStatus = Convert.ToBoolean(obj);
                                        }
                                    });
                                });
                        }
                        
                        // 监听使能状态变化
                        if (!string.IsNullOrEmpty(_EnableState) && this.IsHandleCreated && !this.IsDisposed)
                        {
                            await _axisEventService.RegisterAxisEventAsync(axisName, 
                                $"{AxisConstants.AXIS_GVL}.{_EnableState}", 
                                (obj) =>
                                {
                                    // 安全检查，避免在控件释放后更新UI
                                    if (_isCleaningUp || this.IsDisposed || this.Disposing || !this.IsHandleCreated)
                                        return;
                                        
                                    this.SafeInvoke(() =>
                                    {
                                        if (this.IsHandleCreated && !this.IsDisposed && LED_Enable != null)
                                        {
                                            LED_Enable.LedStatus = Convert.ToBoolean(obj);
                                        }
                                    });
                                });
                        }
                        
                        // 监听零位状态变化
                        if (!string.IsNullOrEmpty(_ZeroState) && this.IsHandleCreated && !this.IsDisposed)
                        {
                            await _axisEventService.RegisterAxisEventAsync(axisName, 
                                $"{AxisConstants.AXIS_GVL}.{_ZeroState}", 
                                (obj) =>
                                {
                                    // 安全检查，避免在控件释放后更新UI
                                    if (_isCleaningUp || this.IsDisposed || this.Disposing || !this.IsHandleCreated)
                                        return;
                                        
                                    this.SafeInvoke(() =>
                                    {
                                        if (this.IsHandleCreated && !this.IsDisposed && LED_Zero != null)
                                        {
                                            LED_Zero.LedStatus = Convert.ToBoolean(obj);
                                        }
                                    });
                                });
                        }
                        
                        // 监听报警状态变化
                        if (!string.IsNullOrEmpty(_AlarmState) && this.IsHandleCreated && !this.IsDisposed)
                        {
                            await _axisEventService.RegisterAxisEventAsync(axisName, 
                                $"{AxisConstants.AXIS_GVL}.{_AlarmState}", 
                                (obj) =>
                                {
                                    // 安全检查，避免在控件释放后更新UI
                                    if (_isCleaningUp || this.IsDisposed || this.Disposing || !this.IsHandleCreated)
                                        return;
                                        
                                    this.SafeInvoke(() =>
                                    {
                                        if (this.IsHandleCreated && !this.IsDisposed && LED_Alarm != null)
                                        {
                                            LED_Alarm.LedStatus = Convert.ToBoolean(obj);
                                        }
                                    });
                                });
                        }
                        
                        // 监听位置完成状态变化
                        if (!string.IsNullOrEmpty(_MoveAbsState) && this.IsHandleCreated && !this.IsDisposed)
                        {
                            await _axisEventService.RegisterAxisEventAsync(axisName, 
                                $"{AxisConstants.AXIS_GVL}.{_MoveAbsState}", 
                                (obj) =>
                                {
                                    // 安全检查，避免在控件释放后更新UI
                                    if (_isCleaningUp || this.IsDisposed || this.Disposing || !this.IsHandleCreated)
                                        return;
                                        
                                    this.SafeInvoke(() =>
                                    {
                                        if (this.IsHandleCreated && !this.IsDisposed && LED_PosComplete != null)
                                        {
                                            LED_PosComplete.LedStatus = Convert.ToBoolean(obj);
                                        }
                                    });
                                });
                        }
                        
                        if (_loggingService != null)
                        {
                            _loggingService.LogInformation($"轴{_id}({axisName})事件注册成功", WaferAligner.EventIds.EventIds.Events_Registration_Success);
                        }
                    }
                    catch (Exception ex)
                    {
                        if (_loggingService != null)
                        {
                            _loggingService.LogError(ex, $"轴{_id}事件注册失败: {ex.Message}", WaferAligner.EventIds.EventIds.Axis_Event_Registration_Failed);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (_loggingService != null)
                {
                    _loggingService.LogError(ex, $"轴{_id}初始化StateWatch异常: {ex.Message}", WaferAligner.EventIds.EventIds.State_Watch_Init_Failed);
                }
            }
        }

        private void GetInformation()
        {
            try
            {
                if (IsDisposed)
                {
                    _loggingService?.LogDebug("GetInformation: 控件未创建或已释放", WaferAligner.EventIds.EventIds.Form_Disposed_In_Timer);
                    return;
                }
                
                if (_axisFactory == null)
                {
                    _loggingService?.LogWarning("GetInformation: _axisFactory为null", WaferAligner.EventIds.EventIds.Axis_Position_Read_Error);
                    return;
                }
                
                string axisName = IdToName(_id);
                if (string.IsNullOrEmpty(axisName))
                {
                    _loggingService?.LogWarning("GetInformation: 无法确定轴名称", WaferAligner.EventIds.EventIds.Axis_Position_Read_Error);
                    return;
                }
                
                if (TxtTargetPos != null && string.IsNullOrEmpty(TxtTargetPos.Text))
                {
                    TxtTargetPos.Text = "0.000";
                }
                
                if (TxtTargetVel != null && string.IsNullOrEmpty(TxtTargetVel.Text))
                {
                    TxtTargetVel.Text = "0.000";
                }
                
                if (TxtJogVel != null && string.IsNullOrEmpty(TxtJogVel.Text))
                {
                    TxtJogVel.Text = "0.000";
                }
                
                switch (_id)
                {
                    case 0:
                        
                        if (BtnZero != null)
                        {
                            BtnZero.Enabled = true;
                        }
                        UnitOfLal("mm", "mm/s");
                        _SpeedMax = 50;
                        _SpeedMin = 5;
                        _Speed = 25;
                        _JogSpeedMax = 30;
                        _JogSpeedMin = 2;
                        _JogSpeed = 10;
                        _PosMax = 75;
                        _PosMin = -75;
                        
                        if (TxtPosLimit != null && TxtNegLimit != null)
                        {
                            TxtPosLimit.Text = _PosMax.ToString();
                            TxtNegLimit.Text = _PosMin.ToString();
                        }
                        
                        try
                        {
                            var xAxis = _axisFactory.GetXAxisViewModel();
                            if (xAxis != null)
                            {
                                // 检查PLC连接状态，避免在未连接时调用可能阻塞的DLL方法
                                if (_plcVariableService?.IsConnectionOpen("Main") ?? false)
                                {
                                    _JogSpeed = xAxis.GetJogSpeed() / AxisConstants.AXIS_XY_MULTIPLE_CONVERTION;
                                    _Speed = xAxis.GetSpeed() / AxisConstants.AXIS_XY_MULTIPLE_CONVERTION;
                                }
                                else
                                {
                                    // PLC未连接时使用默认值，避免DLL调用阻塞
                                    // 保持之前设置的默认值不变
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            if (_loggingService != null)
                            {
                                _loggingService.LogError(ex, $"获取X轴速度信息失败: {ex.Message}", WaferAligner.EventIds.EventIds.Axis_Position_Read_Error);
                            }
                        }
                        
                        if (TxtTargetVel != null && TxtJogVel != null)
                        {
                            TxtTargetVel.Text = string.Format("{0:0.00}", _Speed);
                            TxtJogVel.Text = string.Format("{0:0.00}", _JogSpeed);
                        }
                        break;
                    // 其他case省略...以相同的方式处理其他轴类型
                }
            }
            catch (Exception ex)
            {
                if (_loggingService != null)
                {
                    _loggingService.LogError(ex, $"获取轴{_id}信息异常: {ex.Message}", WaferAligner.EventIds.EventIds.Axis_Position_Read_Error);
                }
            }
        }

        private void _Timer_Tick(object sender, System.Timers.ElapsedEventArgs e)
        {
            // 严格检查控件状态，防止在控件被释放时继续执行
            if (_timer == null || this.IsDisposed || this.Disposing || !this.IsHandleCreated || _isCleaningUp)
            {
                // 控件已释放或正在释放，停止定时器
                try
                {
                    _timer?.Stop();
                }
                catch (ObjectDisposedException)
                {
                    // 定时器已释放，忽略
                }
                return;
            }
            
            // 检查是否需要更新UI（避免过于频繁的更新）
            if ((DateTime.Now - _lastUpdateTime).TotalMilliseconds < UPDATE_INTERVAL_MS)
            {
                return;
            }

            try
            {
                // 暂停定时器防止重入
                _timer?.Stop();
            }
            catch (ObjectDisposedException)
            {
                // 定时器已释放，忽略
                return;
            }
            
            try
            {
                // 再次检查控件状态
                if (this.IsDisposed || this.Disposing || !this.IsHandleCreated || _isCleaningUp)
                {
                    return;
                }
                
                // 使用同步方法获取数据，保持定时器性能
                var updateData = GetAxisData();
                
                // 再次检查控件状态，防止在数据获取过程中控件被释放
                if (this.IsDisposed || this.Disposing || !this.IsHandleCreated || _isCleaningUp)
                {
                    return;
                }
                
                // 批量更新UI，减少Invoke调用次数
                if (!this.SafeInvoke(() => UpdateUI(updateData)))
                {
                    // 无法调用UI更新，停止定时器
                    try
                    {
                        _timer?.Stop();
                    }
                    catch (ObjectDisposedException)
                    {
                        // 定时器已释放，忽略
                    }
                    return;
                }
                
                _lastUpdateTime = DateTime.Now;
                
                // 性能监控：每5秒输出一次定时器性能数据
                if ((DateTime.Now - _lastPerformanceCheck).TotalMilliseconds >= PERFORMANCE_CHECK_INTERVAL_MS)
                {
                    double avgExecTime = _timer.AverageExecutionTimeMs;
                    int execCount = _timer.ExecutionCount;
                    _loggingService?.LogDebug($"轴{_id}定时器性能：平均执行时间={avgExecTime:F2}ms，执行次数={execCount}", 
                        WaferAligner.EventIds.EventIds.Timer_Performance_Warning);
                    
                    _lastPerformanceCheck = DateTime.Now;
                }
            }
            catch (ObjectDisposedException)
            {
                // 控件已释放，停止定时器并退出
                try
                {
                    _timer?.Stop();
                }
                catch (ObjectDisposedException)
                {
                    // 定时器也已释放，忽略
                }
                return;
            }
            catch (Exception ex)
            {
                // 记录错误但不影响定时器运行
                _loggingService?.LogError(ex, $"轴{_id}定时器执行时发生错误: {ex.Message}", WaferAligner.EventIds.EventIds.Timer_Error);
                
                // 如果是可恢复的错误，请求重试
                if (!(ex is ObjectDisposedException || ex is InvalidOperationException))
                {
                    _timer?.RequestRetry(ex);
                }
            }
            finally
            {
                try
                {
                    // 最后检查控件状态再启动定时器
                    if (!this.IsDisposed && !this.Disposing && this.IsHandleCreated && _timer != null && !_timer.IsDisposed && !_isCleaningUp)
                    {
                        _timer?.Start();
                    }
                }
                catch (ObjectDisposedException)
                {
                    // 控件或定时器已释放，忽略
                }
            }
        }

        // 新增：获取轴数据的方法
        private (string position, string velocity, bool ready, bool enable, bool alarm, bool posComplete) GetAxisData()
        {
            try
            {
                // 检查服务是否可用
                if (_axisFactory == null || _plcVariableService == null)
                {
                    _loggingService?.LogError($"轴{_id}无法获取数据: 服务未注入", WaferAligner.EventIds.EventIds.Axis_Position_Read_Error);
                    return ("0.000", "0.000", false, false, false, false);
                }

                string position = "", velocity = "";
                bool ready = false, enable = false, alarm = false, posComplete = false;
                
                switch (_id)
                {
                    case 0: // X轴
                        {
                            var xAxis = _axisFactory.GetXAxisViewModel();
                            if (xAxis != null)
                            {
                                // 检查是否为开发模式
                                bool isDevelopmentMode = WaferAligner.Common.DevelopmentModeHelper.IsDevelopmentMode();

                                if (isDevelopmentMode)
                                {
                                    // 开发模式：所有状态都设为未连接状态
                                    ready = false;
                                    enable = false;
                                    alarm = false;
                                    posComplete = false;
                                }
                                else
                                {
                                    // 生产模式：检查真实连接状态
                                    ready = _plcVariableService.IsConnectionOpen("Main");

                                    // 如果PLC未连接，避免调用可能阻塞的DLL方法
                                    if (ready)
                                    {
                                        enable = xAxis.GetEnableState() == 1;
                                        int alarmState = xAxis.GetAlarmState();
                                        alarm = alarmState != 0;
                                        posComplete = xAxis.GetRunState() == 0;
                                    }
                                    else
                                    {
                                        // PLC未连接时使用默认值，避免DLL调用阻塞
                                        enable = false;
                                        alarm = false;
                                        posComplete = false;
                                    }
                                }

                                // 获取位置和速度信息
                                if (isDevelopmentMode)
                                {
                                    // 开发模式：显示模拟数据
                                    position = "0.000";
                                    velocity = "0.000";
                                }
                                else if (ready)
                                {
                                    // 生产模式且已连接：获取真实数据
                                    double pos = xAxis.GetPosition();
                                    position = string.Format("{0:0.000}", pos / AxisConstants.AXIS_XY_MULTIPLE_CONVERTION);

                                    double speed = xAxis.GetSpeed();
                                    velocity = string.Format("{0:0.000}", speed / AxisConstants.AXIS_XY_MULTIPLE_CONVERTION);
                                }
                                else
                                {
                                    // 生产模式但未连接：显示默认值
                                    position = "0.000";
                                    velocity = "0.000";
                                }

                                // Debug输出：检查X轴状态获取
                                if (_updateCount % 20 == 0) // 每20次更新输出一次，减少输出频率
                                {
                                    string method = ready ? "DLL调用" : "跳过DLL(未连接)";
                                    System.Diagnostics.Debug.WriteLine($"[AxisControl] X轴状态({method}): Enable={enable}, Alarm={alarm}, PosComplete={posComplete}, Ready={ready}");
                                }
                            }
                            else
                            {
                                _loggingService?.LogWarning("无法获取X轴视图模型", WaferAligner.EventIds.EventIds.Axis_Initialize_Failed);
                                return ("0.000", "0.000", false, false, false, false);
                            }
                        }
                        break;
                    case 1: // Y轴
                        {
                            var yAxis = _axisFactory.GetYAxisViewModel();
                            if (yAxis != null)
                            {
                                // 检查是否为开发模式
                                bool isDevelopmentMode = WaferAligner.Common.DevelopmentModeHelper.IsDevelopmentMode();

                                if (isDevelopmentMode)
                                {
                                    // 开发模式：所有状态都设为未连接状态
                                    ready = false;
                                    enable = false;
                                    alarm = false;
                                    posComplete = false;
                                }
                                else
                                {
                                    // 生产模式：检查真实连接状态
                                    ready = _plcVariableService.IsConnectionOpen("Main");

                                    // 如果PLC未连接，避免调用可能阻塞的DLL方法
                                    if (ready)
                                    {
                                        enable = yAxis.GetEnableState() == 1;
                                        int alarmState = yAxis.GetAlarmState();
                                        alarm = alarmState != 0;
                                        posComplete = yAxis.GetRunState() == 0;
                                    }
                                    else
                                    {
                                        // PLC未连接时使用默认值，避免DLL调用阻塞
                                        enable = false;
                                        alarm = false;
                                        posComplete = false;
                                    }
                                }

                                // 获取位置和速度信息
                                if (isDevelopmentMode)
                                {
                                    // 开发模式：显示模拟数据
                                    position = "0.000";
                                    velocity = "0.000";
                                }
                                else if (ready)
                                {
                                    // 生产模式且已连接：获取真实数据
                                    double pos = yAxis.GetPosition();
                                    position = string.Format("{0:0.000}", pos / AxisConstants.AXIS_XY_MULTIPLE_CONVERTION);

                                    double speed = yAxis.GetSpeed();
                                    velocity = string.Format("{0:0.000}", speed / AxisConstants.AXIS_XY_MULTIPLE_CONVERTION);
                                }
                                else
                                {
                                    // 生产模式但未连接：显示默认值
                                    position = "0.000";
                                    velocity = "0.000";
                                }

                                // Debug输出：检查Y轴状态获取
                                if (_updateCount % 20 == 0)
                                {
                                    string method = ready ? "DLL调用" : "跳过DLL(未连接)";
                                    System.Diagnostics.Debug.WriteLine($"[AxisControl] Y轴状态({method}): Enable={enable}, Alarm={alarm}, PosComplete={posComplete}, Ready={ready}");
                                }
                            }
                            else
                            {
                                _loggingService?.LogWarning("无法获取Y轴视图模型", WaferAligner.EventIds.EventIds.Axis_Initialize_Failed);
                                return ("0.000", "0.000", false, false, false, false);
                            }
                        }
                        break;
                    case 2: // R轴
                        {
                            var rAxis = _axisFactory.GetRAxisViewModel();
                            if (rAxis != null)
                            {
                                // 检查是否为开发模式
                                bool isDevelopmentMode = WaferAligner.Common.DevelopmentModeHelper.IsDevelopmentMode();

                                if (isDevelopmentMode)
                                {
                                    // 开发模式：所有状态都设为未连接状态
                                    ready = false;
                                    enable = false;
                                    alarm = false;
                                    posComplete = false;
                                    position = "0.000";
                                    velocity = "0.000";
                                }
                                else
                                {
                                    // 生产模式：检查真实连接状态
                                    ready = _plcVariableService.IsConnectionOpen("Main");

                                    if (ready)
                                    {
                                        enable = rAxis.GetEnableState() == 1;
                                        int alarmState = rAxis.GetAlarmState();
                                        alarm = alarmState != 0;
                                        posComplete = rAxis.GetRunState() == 0;

                                        double pos = rAxis.GetPosition();
                                        position = string.Format("{0:0.000}", pos / AxisConstants.AXIS_R_MULTIPLE_CONVERTION);
                                        double speed = rAxis.GetSpeed();
                                        velocity = string.Format("{0:0.000}", speed / AxisConstants.AXIS_R_MULTIPLE_CONVERTION);
                                    }
                                    else
                                    {
                                        enable = false;
                                        alarm = false;
                                        posComplete = false;
                                        position = "0.000";
                                        velocity = "0.000";
                                    }
                                }

                                // 获取位置和速度信息，同样需要检查连接状态
                                if (ready)
                                {
                                    double pos = rAxis.GetPosition();
                                    position = string.Format("{0:0.000}", pos / AxisConstants.AXIS_R_MULTIPLE_CONVERTION);

                                    double speed = rAxis.GetSpeed();
                                    velocity = string.Format("{0:0.000}", speed / AxisConstants.AXIS_R_MULTIPLE_CONVERTION);
                                }
                                else
                                {
                                    // PLC未连接时使用默认值
                                    position = "0.000";
                                    velocity = "0.000";
                                }

                                // Debug输出：检查R轴状态获取
                                if (_updateCount % 20 == 0)
                                {
                                    string method = ready ? "DLL调用" : "跳过DLL(未连接)";
                                    System.Diagnostics.Debug.WriteLine($"[AxisControl] R轴状态({method}): Enable={enable}, Alarm={alarm}, PosComplete={posComplete}, Ready={ready}");
                                }
                            }
                            else
                            {
                                _loggingService?.LogWarning("无法获取R轴视图模型", WaferAligner.EventIds.EventIds.Axis_Initialize_Failed);
                                return ("0.000", "0.000", false, false, false, false);
                            }
                        }
                        break;
                    case 3: // Z轴
                        {
                            var zAxis = _axisFactory.GetZAxisViewModel();
                            if (zAxis != null)
                            {
                                // 检查是否为开发模式
                                bool isDevelopmentMode = WaferAligner.Common.DevelopmentModeHelper.IsDevelopmentMode();

                                if (isDevelopmentMode)
                                {
                                    // 开发模式：所有状态都设为未连接状态
                                    ready = false;
                                    enable = false;
                                    alarm = false;
                                    posComplete = false;
                                    position = "0.000";
                                    velocity = "0.000";
                                }
                                else
                                {
                                    // 生产模式：检查真实连接状态
                                    ready = _plcVariableService.IsConnectionOpen("ZAxis");

                                    if (ready)
                                    {
                                        // Z轴使用PLC轴接口，状态通过PLC变量获取
                                        enable = true; // Z轴的使能状态通过PLC变量获取
                                        alarm = false; // Z轴的报警状态通过PLC变量获取
                                        posComplete = true; // Z轴的位置完成状态通过PLC变量获取

                                        // Z轴位置通过事件更新，这里提供默认值
                                        double pos = 0;
                                        position = string.Format("{0:0.000}", pos / AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION);
                                        velocity = "0.000"; // Z轴速度通过PLC变量获取
                                    }
                                    else
                                    {
                                        // PLC未连接时使用默认值
                                        enable = false;
                                        alarm = false;
                                        posComplete = false;
                                        position = "0.000";
                                        velocity = "0.000";
                                    }
                                }
                            }
                            else
                            {
                                _loggingService?.LogWarning("无法获取Z轴视图模型", WaferAligner.EventIds.EventIds.Axis_Initialize_Failed);
                                return ("0.000", "0.000", false, false, false, false);
                            }
                        }
                        break;
                    case 4: // LX轴
                    case 5: // LY轴
                    case 6: // LZ轴
                    case 7: // RX轴
                    case 8: // RY轴
                    case 9: // RZ轴
                        {
                            // 检查是否为开发模式
                            bool isDevelopmentMode = WaferAligner.Common.DevelopmentModeHelper.IsDevelopmentMode();

                            if (isDevelopmentMode)
                            {
                                // 开发模式：所有状态都设为未连接状态
                                ready = false;
                                enable = false;
                                alarm = false;
                                posComplete = false;
                                position = "0.000";
                                velocity = "0.000";
                            }
                            else
                            {
                                // 生产模式：检查真实连接状态
                                string plcName = GetPlcNameFromAxisId(_id);
                                ready = _plcVariableService.IsConnectionOpen(plcName);

                                if (ready)
                                {
                                    // 相机轴使用PLC轴接口，状态主要通过事件更新
                                    enable = true; // 相机轴状态通过事件更新
                                    alarm = false; // 相机轴状态通过事件更新
                                    posComplete = true; // 相机轴状态通过事件更新
                                    position = "0.000"; // 相机轴位置通过事件更新
                                    velocity = "0.000"; // 相机轴速度通过事件更新
                                }
                                else
                                {
                                    // PLC未连接时使用默认值
                                    enable = false;
                                    alarm = false;
                                    posComplete = false;
                                    position = "0.000";
                                    velocity = "0.000";
                                }
                            }
                        }
                        break;
                }

                return (position, velocity, ready, enable, alarm, posComplete);
            }
            catch (Exception ex)
            {
                _loggingService?.LogError(ex, $"获取轴{_id}数据失败: {ex.Message}", WaferAligner.EventIds.EventIds.Axis_Position_Read_Error);
                return ("0.000", "0.000", false, false, false, false); // 返回安全值避免UI异常
            }
        }
        
        private async Task<(string position, string velocity, bool ready, bool enable, bool alarm, bool posComplete)> GetAxisDataAsync()
        {
            try
            {
                // 检查服务是否可用
                if (_axisFactory == null || _plcVariableService == null)
                {
                    _loggingService?.LogError($"轴{_id}无法使用依赖注入异步获取数据: 服务未注入", WaferAligner.EventIds.EventIds.Service_Initialization_Failed);
                    return ("0.000", "0.000", false, false, false, false);
                }

                string position = "", velocity = "";
                bool ready = false, enable = false, alarm = false, posComplete = false;
                
                switch (_id)
                {
                    case 0: // X轴
                        {
                            var xAxis = _axisFactory.GetXAxisViewModel();
                            if (xAxis != null)
                            {
                                ready = _plcVariableService.IsConnectionOpen("Main");
                                enable = await xAxis.GetEnableStateAsync() == 1;
                                alarm = await xAxis.GetAlarmStateAsync() != 0;
                                posComplete = await xAxis.GetRunStateAsync() == 0;
                                double pos = await xAxis.GetCurrentPositionAsync();
                                double spd = await xAxis.GetCurrentSpeedAsync();
                                position = string.Format("{0:0.000}", pos / AxisConstants.AXIS_XY_MULTIPLE_CONVERTION);
                                velocity = string.Format("{0:0.000}", spd / AxisConstants.AXIS_XY_MULTIPLE_CONVERTION);
                            }
                            else
                            {
                                _loggingService?.LogWarning("无法获取X轴视图模型，使用默认数据", WaferAligner.EventIds.EventIds.Axis_Initialize_Failed);
                                return ("0.000", "0.000", false, false, false, false);
                            }
                        }
                        break;
                    case 1: // Y轴
                        {
                            var yAxis = _axisFactory.GetYAxisViewModel();
                            if (yAxis != null)
                            {
                                ready = _plcVariableService.IsConnectionOpen("Main");
                                enable = await yAxis.GetEnableStateAsync() == 1;
                                alarm = await yAxis.GetAlarmStateAsync() != 0;
                                posComplete = await yAxis.GetRunStateAsync() == 0;
                                double pos = await yAxis.GetCurrentPositionAsync();
                                double spd = await yAxis.GetCurrentSpeedAsync();
                                position = string.Format("{0:0.000}", pos / AxisConstants.AXIS_XY_MULTIPLE_CONVERTION);
                                velocity = string.Format("{0:0.000}", spd / AxisConstants.AXIS_XY_MULTIPLE_CONVERTION);
                            }
                            else
                            {
                                _loggingService?.LogWarning("无法获取Y轴视图模型，使用默认数据", WaferAligner.EventIds.EventIds.Axis_Initialize_Failed);
                                return ("0.000", "0.000", false, false, false, false);
                            }
                        }
                        break;
                    case 2: // R轴
                        {
                            var rAxis = _axisFactory.GetRAxisViewModel();
                            if (rAxis != null)
                            {
                                ready = _plcVariableService.IsConnectionOpen("Main");
                                enable = await rAxis.GetEnableStateAsync() == 1;
                                alarm = await rAxis.GetAlarmStateAsync() != 0;
                                posComplete = await rAxis.GetRunStateAsync() == 0;
                                double pos = await rAxis.GetCurrentPositionAsync();
                                double spd = await rAxis.GetCurrentSpeedAsync();
                                position = string.Format("{0:0.000}", pos / AxisConstants.AXIS_R_MULTIPLE_CONVERTION);
                                velocity = string.Format("{0:0.000}", spd / AxisConstants.AXIS_R_MULTIPLE_CONVERTION);
                            }
                            else
                            {
                                _loggingService?.LogWarning("无法获取R轴视图模型，使用默认数据", WaferAligner.EventIds.EventIds.Axis_Initialize_Failed);
                                return ("0.000", "0.000", false, false, false, false);
                            }
                        }
                        break;
                    // 其他轴的处理类似...省略
                }

                return (position, velocity, ready, enable, alarm, posComplete);
            }
            catch (Exception ex)
            {
                _loggingService?.LogError(ex, $"异步获取轴{_id}数据失败: {ex.Message}", WaferAligner.EventIds.EventIds.Axis_Position_Read_Error);
                return ("0.000", "0.000", false, false, false, false); // 返回安全值避免UI异常
            }
        }

        // 新增：批量更新UI的方法
        private void UpdateUI((string position, string velocity, bool ready, bool enable, bool alarm, bool posComplete) data)
        {
            _updateCount++;
            
            // 只有当数据发生变化时才更新UI，减少不必要的重绘
            if (data.position != _lastPositionText)
            {
                TxtCurentPos.Text = data.position;
                _lastPositionText = data.position;
            }

            if (data.velocity != _lastVelocityText)
            {
                TxtCurentVel.Text = data.velocity;
                _lastVelocityText = data.velocity;
            }

            if (data.ready != _lastReadyState)
            {
                LED_Ready.LedStatus = data.ready;
                _lastReadyState = data.ready;
            }

            if (data.enable != _lastEnableState)
            {
                LED_Enable.LedStatus = data.enable;
                _lastEnableState = data.enable;
            }

            if (data.alarm != _lastAlarmState)
            {
                LED_Alarm.LedStatus = data.alarm;
                _lastAlarmState = data.alarm;

                // Debug输出：检查报警状态更新
                System.Diagnostics.Debug.WriteLine($"[AxisControl] 轴{_id} 报警状态更新: LED_Alarm.LedStatus = {data.alarm}");
            }

            if (data.posComplete != _lastPosCompleteState)
            {
                LED_PosComplete.LedStatus = data.posComplete;
                _lastPosCompleteState = data.posComplete;
            }
        }

        void UnitOfLal(string Unit1, string Unit2)
        {
            LalCurentPosUnit.Text = Unit1;
            LalTargetPosUnit.Text = Unit1;
            LalCurentVelUnit.Text = Unit2;
            LalTargetVelUnit.Text = Unit2;
            LalJogVelUnit.Text = Unit2;
            LalPosLimitUnit.Text = Unit1;
            LalNegLimitUnit.Text = Unit1;
        }

        #endregion  load

        #region  Button

        #region 归零
        // 取消令牌源 - 用于归零操作
        private CancellationTokenSource _zeroCts;
        
        private async void BtnZZero_Click(object sender, EventArgs e)
        {
            // 禁用按钮，避免重复操作
            BtnManage(false);
            BtnZPause.Enabled = false;
            
            // 创建新的取消令牌源，取消之前的操作
            if (_zeroCts != null)
            {
                _zeroCts.Cancel();
                _zeroCts.Dispose();
            }
            _zeroCts = new CancellationTokenSource();
            
            try
            {
                // 检查服务是否可用
                if (_axisFactory == null)
                {
                    _loggingService?.LogError($"轴{_id}归零操作失败：轴工厂未注入", WaferAligner.EventIds.EventIds.Service_Unavailable);
                    return;
                }
                
                // 轴归零操作
                switch (_id)
                {
                    case 0://X
                        {
                            var xAxis = _axisFactory.GetXAxisViewModel();
                            await xAxis.InitAxisAsync();
                            LED_Zero.LedStatus = false;
                        }
                        break;
                    case 1://Y
                        {
                            var yAxis = _axisFactory.GetYAxisViewModel();
                            await yAxis.InitAxisAsync();
                            LED_Zero.LedStatus = false;
                        }
                        break;
                    case 2://R
                        {
                            var rAxis = _axisFactory.GetRAxisViewModel();
                            await rAxis.InitAxisAsync();
                            LED_Zero.LedStatus = false;
                        }
                        break;
                    case 3://Z
                        { 
                            var zAxis = _axisFactory.GetZAxisViewModel();
                            await zAxis.HomeAsync(); 
                        }
                        break;
                    case 4://LX
                        { 
                            var lxAxis = _axisFactory.GetLXAxisViewModel();
                            await lxAxis.HomeAsync(); 
                        }
                        break;
                    case 5://LY
                        { 
                            var lyAxis = _axisFactory.GetLYAxisViewModel();
                            await lyAxis.HomeAsync(); 
                        }
                        break;
                    case 6://LZ
                        { 
                            var lzAxis = _axisFactory.GetLZAxisViewModel();
                            await lzAxis.HomeAsync(); 
                        }
                        break;
                    case 7://RX 
                        { 
                            var rxAxis = _axisFactory.GetRXAxisViewModel();
                            await rxAxis.HomeAsync(); 
                        }
                        break;
                    case 8://RY  
                        { 
                            var ryAxis = _axisFactory.GetRYAxisViewModel();
                            await ryAxis.HomeAsync(); 
                        }
                        break;
                    case 9://RZ  
                        { 
                            var rzAxis = _axisFactory.GetRZAxisViewModel();
                            await rzAxis.HomeAsync(); 
                        }
                        break;
                }
                
                // 等待归零完成，替代原BackgroundWorker的功能
                await Task.Delay(600); // 等待初始化
                
                // 等待归零状态或取消操作
                CancellationTokenSource timeoutCts = null;
                CancellationTokenSource linkedCts = null;
                
                try 
                {
                    timeoutCts = new CancellationTokenSource(60000); // 60秒超时
                    linkedCts = CancellationTokenSource.CreateLinkedTokenSource(_zeroCts.Token, timeoutCts.Token);
                    
                    await Task.Run(async () => 
                    {
                        switch (_id)
                        {
                            case 0://X
                                {
                                    var xAxis = _axisFactory.GetXAxisViewModel();
                                    int retX = await xAxis.GetRunStateAsync();
                                    while (retX == 1)
                                    {
                                        linkedCts.Token.ThrowIfCancellationRequested();
                                        retX = await xAxis.GetRunStateAsync();
                                        await Task.Delay(50, linkedCts.Token);
                                    }
                                }
                                break;
                            case 1://Y
                                {
                                    var yAxis = _axisFactory.GetYAxisViewModel();
                                    int retY = await yAxis.GetRunStateAsync();
                                    while (retY == 1)
                                    {
                                        linkedCts.Token.ThrowIfCancellationRequested();
                                        retY = await yAxis.GetRunStateAsync();
                                        await Task.Delay(50, linkedCts.Token);
                                    }
                                }
                                break;
                            case 2://R
                                {
                                    var rAxis = _axisFactory.GetRAxisViewModel();
                                    int retR = await rAxis.GetRunStateAsync();
                                    while (retR == 1)
                                    {
                                        linkedCts.Token.ThrowIfCancellationRequested();
                                        retR = await rAxis.GetRunStateAsync();
                                        await Task.Delay(50, linkedCts.Token);
                                    }
                                }
                                break;
                            default:
                                {
                                    // 对于其他轴，等待LED指示灯状态
                                    while (!LED_Zero.LedStatus)
                                    {
                                        linkedCts.Token.ThrowIfCancellationRequested();
                                        await Task.Delay(50, linkedCts.Token);
                                    }
                                }
                                break;
                        }
                    }, linkedCts.Token);
                    
                    // 归零成功
                    if (_id < 3) LED_Zero.LedStatus = true;
                    _loggingService?.LogInformation($"轴{_id}归零操作成功完成", WaferAligner.EventIds.EventIds.Axis_Home_Completed);
                }
                catch (OperationCanceledException)
                {
                    if (timeoutCts != null && timeoutCts.IsCancellationRequested)
                    {
                        _loggingService?.LogWarning($"轴{_id}归零操作超时", WaferAligner.EventIds.EventIds.Axis_Home_Timeout);
                        SafeInvoke(() => MessageBox.Show("归零操作超时，请检查轴状态！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Warning));
                    }
                    else
                    {
                        _loggingService?.LogInformation($"轴{_id}归零操作被取消", WaferAligner.EventIds.EventIds.Axis_Home_Cancelled);
                        SafeInvoke(() => MessageBox.Show("您取消了归零操作!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information));
                    }
                }
                finally
                {
                    timeoutCts?.Dispose();
                    linkedCts?.Dispose();
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError(ex, $"轴{_id}归零操作失败: {ex.Message}", WaferAligner.EventIds.EventIds.Unhandled_Exception);
                SafeInvoke(() => 
                {
                    _statusUpdateService?.UpdateStatus("归零过程中产生错误:" + ex.Message);
                    MessageBox.Show("归零过程中产生错误:" + ex.Message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                });
            }
            finally
            {
                // 恢复按钮状态
                SafeInvoke(() => 
                {
                    BtnManage(true);
                    BtnZPause.Enabled = true;
                });
            }
        }
        #endregion 归零

        #region  定位&&停止
        // 取消令牌源 - 用于定位操作
        private CancellationTokenSource _posCts;
        
        private async void BtnZPos_Click(object sender, EventArgs e)
        {
            // 禁用按钮，避免重复操作
            BtnManage(false);
            
            // 创建新的取消令牌源，取消之前的操作
            if (_posCts != null)
            {
                _posCts.Cancel();
                _posCts.Dispose();
            }
            _posCts = new CancellationTokenSource();
            
            try
            {
                // 检查服务是否可用
                if (_axisFactory == null)
                {
                    _loggingService?.LogError($"轴{_id}定位操作失败：轴工厂未注入", WaferAligner.EventIds.EventIds.Service_Unavailable);
                    BtnManage(true);
                    return;
                }
                
                // 执行定位操作
                switch (_id)
                {
                    case 0://X
                        { 
                            var xAxis = _axisFactory.GetXAxisViewModel();
                            await xAxis.MoveToPositionAsync(Pos); 
                        }
                        break;
                    case 1://Y
                        { 
                            var yAxis = _axisFactory.GetYAxisViewModel();
                            await yAxis.MoveToPositionAsync(Pos); 
                        }
                        break;
                    case 2://R
                        { 
                            var rAxis = _axisFactory.GetRAxisViewModel();
                            await rAxis.MoveToPositionAsync(Pos); 
                        }
                        break;
                    case 3://Z
                        { 
                            var zAxis = _axisFactory.GetZAxisViewModel();
                            await zAxis.MoveToPositionAsync(Pos); 
                        }
                        break;
                    case 4://LX
                        { 
                            var lxAxis = _axisFactory.GetLXAxisViewModel();
                            await lxAxis.MoveToPositionAsync(Pos); 
                        }
                        break;
                    case 5://LY
                        { 
                            var lyAxis = _axisFactory.GetLYAxisViewModel();
                            await lyAxis.MoveToPositionAsync(Pos); 
                        }
                        break;
                    case 6://LZ
                        { 
                            var lzAxis = _axisFactory.GetLZAxisViewModel();
                            await lzAxis.MoveToPositionAsync(Pos); 
                        }
                        break;
                    case 7://RX 
                        { 
                            var rxAxis = _axisFactory.GetRXAxisViewModel();
                            await rxAxis.MoveToPositionAsync(Pos); 
                        }
                        break;
                    case 8://RY  
                        { 
                            var ryAxis = _axisFactory.GetRYAxisViewModel();
                            await ryAxis.MoveToPositionAsync(Pos); 
                        }
                        break;
                    case 9://RZ  
                        { 
                            var rzAxis = _axisFactory.GetRZAxisViewModel();
                            await rzAxis.MoveToPositionAsync(Pos); 
                        }
                        break;
                }
                
                // 等待定位完成
                await Task.Delay(400);
                
                CancellationTokenSource timeoutCts = null;
                CancellationTokenSource linkedCts = null;
                
                try
                {
                    timeoutCts = new CancellationTokenSource(60000); // 60秒超时
                    linkedCts = CancellationTokenSource.CreateLinkedTokenSource(_posCts.Token, timeoutCts.Token);
                    
                    // 等待定位完成
                    await Task.Run(async () => 
                    {
                        while (!LED_PosComplete.LedStatus)
                        {
                            linkedCts.Token.ThrowIfCancellationRequested();
                            await Task.Delay(50, linkedCts.Token);
                        }
                    }, linkedCts.Token);
                    
                    _loggingService?.LogInformation($"轴{_id}定位操作成功完成", WaferAligner.EventIds.EventIds.Axis_Move_Completed);
                }
                catch (OperationCanceledException)
                {
                    if (timeoutCts != null && timeoutCts.IsCancellationRequested)
                    {
                        _loggingService?.LogWarning($"轴{_id}定位操作超时", WaferAligner.EventIds.EventIds.Axis_Move_Timeout);
                        SafeInvoke(() => MessageBox.Show("定位操作超时，请检查轴状态！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Warning));
                    }
                    else
                    {
                        _loggingService?.LogInformation($"轴{_id}定位操作被取消", WaferAligner.EventIds.EventIds.Axis_Move_Cancelled);
                        SafeInvoke(() => MessageBox.Show("您取消了定位操作!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information));
                    }
                }
                finally
                {
                    timeoutCts?.Dispose();
                    linkedCts?.Dispose();
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError(ex, $"轴{_id}定位操作失败: {ex.Message}", WaferAligner.EventIds.EventIds.Unhandled_Exception);
                SafeInvoke(() => 
                {
                    _statusUpdateService?.UpdateStatus("定位过程中产生错误:" + ex.Message);
                    MessageBox.Show("定位过程中产生错误:" + ex.Message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                });
            }
            finally
            {
                // 恢复按钮状态
                SafeInvoke(() => BtnManage(true));
            }
        }
        private async void BtnZPause_Click(object sender, EventArgs e)
        {
            BtnManage(false);
            try
            {
                // 检查服务是否可用
                if (_axisFactory == null)
                {
                    _loggingService?.LogError($"轴{_id}停止操作失败：轴工厂未注入", WaferAligner.EventIds.EventIds.Service_Unavailable);
                    return;
                }
                
                // 取消之前的所有操作
                if (_zeroCts != null)
                {
                    _zeroCts.Cancel();
                }
                
                if (_posCts != null)
                {
                    _posCts.Cancel();
                }
                
                // 执行轴停止
                switch (_id)
                {
                    case 0://X
                        { 
                            var xAxis = _axisFactory.GetXAxisViewModel();
                            await xAxis.StopAsync(); 
                        }
                        break;
                    case 1://Y
                        { 
                            var yAxis = _axisFactory.GetYAxisViewModel();
                            await yAxis.StopAsync(); 
                        }
                        break;
                    case 2://R
                        { 
                            var rAxis = _axisFactory.GetRAxisViewModel();
                            await rAxis.StopAsync(); 
                        }
                        break;
                    case 3://Z
                        { 
                            var zAxis = _axisFactory.GetZAxisViewModel();
                            await zAxis.StopAsync(); 
                        }
                        break;
                    case 4://LX
                        { 
                            var lxAxis = _axisFactory.GetLXAxisViewModel();
                            await lxAxis.StopAsync(); 
                        }
                        break;
                    case 5://LY
                        { 
                            var lyAxis = _axisFactory.GetLYAxisViewModel();
                            await lyAxis.StopAsync(); 
                        }
                        break;
                    case 6://LZ
                        { 
                            var lzAxis = _axisFactory.GetLZAxisViewModel();
                            await lzAxis.StopAsync(); 
                        }
                        break;
                    case 7://RX 
                        { 
                            var rxAxis = _axisFactory.GetRXAxisViewModel();
                            await rxAxis.StopAsync(); 
                        }
                        break;
                    case 8://RY  
                        { 
                            var ryAxis = _axisFactory.GetRYAxisViewModel();
                            await ryAxis.StopAsync(); 
                        }
                        break;
                    case 9://RZ  
                        { 
                            var rzAxis = _axisFactory.GetRZAxisViewModel();
                            await rzAxis.StopAsync(); 
                        }
                        break;
                }
                
                _loggingService?.LogInformation($"轴{_id}停止操作成功执行", WaferAligner.EventIds.EventIds.Axis_Stop_Completed);
            }
            catch (Exception ex)
            {
                _loggingService?.LogError(ex, $"轴{_id}停止操作失败: {ex.Message}", WaferAligner.EventIds.EventIds.Axis_Stop_Error);
                SafeInvoke(() => MessageBox.Show($"停止操作失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error));
            }
            finally
            {
                SafeInvoke(() => BtnManage(true));
            }
        }
        #endregion  定位

        #region  JOG
        private async void BtnZFront_MouseDown(object sender, MouseEventArgs e)
        {
            BtnManage(false);
            try
            {
                // 检查服务是否可用
                if (_axisFactory == null)
                {
                    _loggingService?.LogError($"轴{_id}JOG正向操作失败：轴工厂未注入", WaferAligner.EventIds.EventIds.Service_Unavailable);
                    return;
                }
                
                switch (_id)
                {
                    case 0://X
                        { 
                            var xAxis = _axisFactory.GetXAxisViewModel();
                            await xAxis.JogForwardAsync(); 
                        }
                        break;
                    case 1://Y
                        { 
                            var yAxis = _axisFactory.GetYAxisViewModel();
                            await yAxis.JogForwardAsync(); 
                        }
                        break;
                    case 2://R
                        { 
                            var rAxis = _axisFactory.GetRAxisViewModel();
                            await rAxis.JogForwardAsync(); 
                        }
                        break;
                    case 3://Z
                        { 
                            var zAxis = _axisFactory.GetZAxisViewModel();
                            await zAxis.JogForwardAsync(); 
                        }
                        break;
                    case 4://LX
                        { 
                            var lxAxis = _axisFactory.GetLXAxisViewModel();
                            await lxAxis.JogForwardAsync(); 
                        }
                        break;
                    case 5://LY
                        { 
                            var lyAxis = _axisFactory.GetLYAxisViewModel();
                            await lyAxis.JogForwardAsync(); 
                        }
                        break;
                    case 6://LZ
                        { 
                            var lzAxis = _axisFactory.GetLZAxisViewModel();
                            await lzAxis.JogForwardAsync(); 
                        }
                        break;
                    case 7://RX 
                        { 
                            var rxAxis = _axisFactory.GetRXAxisViewModel();
                            await rxAxis.JogForwardAsync(); 
                        }
                        break;
                    case 8://RY  
                        { 
                            var ryAxis = _axisFactory.GetRYAxisViewModel();
                            await ryAxis.JogForwardAsync(); 
                        }
                        break;
                    case 9://RZ  
                        { 
                            var rzAxis = _axisFactory.GetRZAxisViewModel();
                            await rzAxis.JogForwardAsync(); 
                        }
                        break;
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError(ex, $"轴{_id}JOG正向操作失败", WaferAligner.EventIds.EventIds.Axis_Move_Error);
            }
            BtnManage(true);
        }
        private async void BtnZFront_MouseUp(object sender, MouseEventArgs e)
        {
            BtnManage(false);
            try
            {
                // 检查服务是否可用
                if (_axisFactory == null)
                {
                    _loggingService?.LogError($"轴{_id}JOG停止失败：轴工厂未注入", WaferAligner.EventIds.EventIds.Service_Unavailable);
                    return;
                }
                
                switch (_id)
                {
                    case 0://X
                        { 
                            var xAxis = _axisFactory.GetXAxisViewModel();
                            await xAxis.JogStopAsync(); 
                        }
                        break;
                    case 1://Y
                        { 
                            var yAxis = _axisFactory.GetYAxisViewModel();
                            await yAxis.JogStopAsync(); 
                        }
                        break;
                    case 2://R
                        { 
                            var rAxis = _axisFactory.GetRAxisViewModel();
                            await rAxis.JogStopAsync(); 
                        }
                        break;
                    case 3://Z
                        { 
                            var zAxis = _axisFactory.GetZAxisViewModel();
                            await zAxis.JogStopAsync(); 
                        }
                        break;
                    case 4://LX
                        { 
                            var lxAxis = _axisFactory.GetLXAxisViewModel();
                            await lxAxis.JogStopAsync(); 
                        }
                        break;
                    case 5://LY
                        { 
                            var lyAxis = _axisFactory.GetLYAxisViewModel();
                            await lyAxis.JogStopAsync(); 
                        }
                        break;
                    case 6://LZ
                        { 
                            var lzAxis = _axisFactory.GetLZAxisViewModel();
                            await lzAxis.JogStopAsync(); 
                        }
                        break;
                    case 7://RX 
                        { 
                            var rxAxis = _axisFactory.GetRXAxisViewModel();
                            await rxAxis.JogStopAsync(); 
                        }
                        break;
                    case 8://RY  
                        { 
                            var ryAxis = _axisFactory.GetRYAxisViewModel();
                            await ryAxis.JogStopAsync(); 
                        }
                        break;
                    case 9://RZ  
                        { 
                            var rzAxis = _axisFactory.GetRZAxisViewModel();
                            await rzAxis.JogStopAsync(); 
                        }
                        break;
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError(ex, $"轴{_id}JOG停止失败", WaferAligner.EventIds.EventIds.Axis_Stop_Error);
            }
            BtnManage(true);
        }

        private async void BtnZBack_MouseDown(object sender, MouseEventArgs e)
        {
            BtnManage(false);
            try
            {
                // 检查服务是否可用
                if (_axisFactory == null)
                {
                    _loggingService?.LogError($"轴{_id}JOG反向操作失败：轴工厂未注入", WaferAligner.EventIds.EventIds.Service_Unavailable);
                    return;
                }
                
                switch (_id)
                {
                    case 0://X
                        { 
                            var xAxis = _axisFactory.GetXAxisViewModel();
                            await xAxis.JogBackwardAsync(); 
                        }
                        break;
                    case 1://Y
                        { 
                            var yAxis = _axisFactory.GetYAxisViewModel();
                            await yAxis.JogBackwardAsync(); 
                        }
                        break;
                    case 2://R
                        { 
                            var rAxis = _axisFactory.GetRAxisViewModel();
                            await rAxis.JogBackwardAsync(); 
                        }
                        break;
                    case 3://Z
                        { 
                            var zAxis = _axisFactory.GetZAxisViewModel();
                            await zAxis.JogBackwardAsync(); 
                        }
                        break;
                    case 4://LX
                        { 
                            var lxAxis = _axisFactory.GetLXAxisViewModel();
                            await lxAxis.JogBackwardAsync(); 
                        }
                        break;
                    case 5://LY
                        { 
                            var lyAxis = _axisFactory.GetLYAxisViewModel();
                            await lyAxis.JogBackwardAsync(); 
                        }
                        break;
                    case 6://LZ
                        { 
                            var lzAxis = _axisFactory.GetLZAxisViewModel();
                            await lzAxis.JogBackwardAsync(); 
                        }
                        break;
                    case 7://RX 
                        { 
                            var rxAxis = _axisFactory.GetRXAxisViewModel();
                            await rxAxis.JogBackwardAsync(); 
                        }
                        break;
                    case 8://RY  
                        { 
                            var ryAxis = _axisFactory.GetRYAxisViewModel();
                            await ryAxis.JogBackwardAsync(); 
                        }
                        break;
                    case 9://RZ  
                        { 
                            var rzAxis = _axisFactory.GetRZAxisViewModel();
                            await rzAxis.JogBackwardAsync(); 
                        }
                        break;
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError(ex, $"轴{_id}JOG反向操作失败", WaferAligner.EventIds.EventIds.Axis_Move_Error);
            }
            BtnManage(true);
        }

        private async void BtnZBack_MouseUp(object sender, MouseEventArgs e)
        {
            BtnManage(false);
            try
            {
                // 检查服务是否可用
                if (_axisFactory == null)
                {
                    _loggingService?.LogError($"轴{_id}JOG停止失败：轴工厂未注入", WaferAligner.EventIds.EventIds.Service_Unavailable);
                    return;
                }
                
                switch (_id)
                {
                    case 0://X
                        { 
                            var xAxis = _axisFactory.GetXAxisViewModel();
                            await xAxis.JogStopAsync(); 
                        }
                        break;
                    case 1://Y
                        { 
                            var yAxis = _axisFactory.GetYAxisViewModel();
                            await yAxis.JogStopAsync(); 
                        }
                        break;
                    case 2://R
                        { 
                            var rAxis = _axisFactory.GetRAxisViewModel();
                            await rAxis.JogStopAsync(); 
                        }
                        break;
                    case 3://Z
                        { 
                            var zAxis = _axisFactory.GetZAxisViewModel();
                            await zAxis.JogStopAsync(); 
                        }
                        break;
                    case 4://LX
                        { 
                            var lxAxis = _axisFactory.GetLXAxisViewModel();
                            await lxAxis.JogStopAsync(); 
                        }
                        break;
                    case 5://LY
                        { 
                            var lyAxis = _axisFactory.GetLYAxisViewModel();
                            await lyAxis.JogStopAsync(); 
                        }
                        break;
                    case 6://LZ
                        { 
                            var lzAxis = _axisFactory.GetLZAxisViewModel();
                            await lzAxis.JogStopAsync(); 
                        }
                        break;
                    case 7://RX 
                        { 
                            var rxAxis = _axisFactory.GetRXAxisViewModel();
                            await rxAxis.JogStopAsync(); 
                        }
                        break;
                    case 8://RY  
                        { 
                            var ryAxis = _axisFactory.GetRYAxisViewModel();
                            await ryAxis.JogStopAsync(); 
                        }
                        break;
                    case 9://RZ  
                        { 
                            var rzAxis = _axisFactory.GetRZAxisViewModel();
                            await rzAxis.JogStopAsync(); 
                        }
                        break;
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError(ex, $"轴{_id}JOG停止失败", WaferAligner.EventIds.EventIds.Axis_Stop_Error);
            }
            BtnManage(true);
        }
        #endregion  JOG


        #endregion  Button

        #region 文本款输入 目标位置/极限位置   目标速度/JOG速度
        private async void TxtTargetPos_KeyUp(object sender, KeyEventArgs e)
        {
            if (e.KeyCode != Keys.Enter)
            {
                return;
            }
            
            UITextBox temp = (UITextBox)sender;
            double TarPos = 0;
            string TempText = temp.Text.Trim();
            if (TempText == "+" || TempText == "-" || TempText == "")
            {
                TarPos = 0;
                Pos = TarPos;
                //temp.Text = Convert.ToString(TarPos);
            }
            else if (TempText.EndsWith("."))
            {
                return;
            }
            else
            {
                TarPos = Convert.ToDouble(TempText);
                Pos = TarPos;
            }
            bool IsLt = this.IsLimit(TarPos);
            if (IsLt)
            {
                string name = IdToName(_id);
                MessageBox.Show(string.Format("输入数字超出{0}极限！\r\n    请重新输入！", name), "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                TarPos = 0;
                Pos = TarPos;
                temp.Text = Convert.ToString(TarPos);
            }
            
            try 
            {
                // 检查服务是否可用
                if (_axisFactory == null)
                {
                    _loggingService?.LogError($"轴{_id}位置设置失败：轴工厂未注入", WaferAligner.EventIds.EventIds.Service_Unavailable);
                    return;
                }

                switch (_id)
                {
                    case 0://X
                        { 
                            var xAxis = _axisFactory.GetXAxisViewModel();
                            await xAxis.SetPositionAsync((int)(TarPos * AxisConstants.AXIS_XY_MULTIPLE_CONVERTION)); 
                        }
                        break;
                    case 1://Y
                        { 
                            var yAxis = _axisFactory.GetYAxisViewModel();
                            await yAxis.SetPositionAsync((int)(TarPos * AxisConstants.AXIS_XY_MULTIPLE_CONVERTION)); 
                        }
                        break;
                    case 2://R
                        { 
                            var rAxis = _axisFactory.GetRAxisViewModel();
                            await rAxis.SetPositionAsync((int)(TarPos * AxisConstants.AXIS_R_MULTIPLE_CONVERTION)); 
                        }
                        break;
                    case 3://Z
                        { 
                            var zAxis = _axisFactory.GetZAxisViewModel();
                            await zAxis.SetPositionAsync(TarPos * AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION); 
                        }
                        break;
                    case 4://LX
                        { 
                            var lxAxis = _axisFactory.GetLXAxisViewModel();
                            await lxAxis.SetPositionAsync(TarPos * AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION); 
                        }
                        break;
                    case 5://LY
                        { 
                            var lyAxis = _axisFactory.GetLYAxisViewModel();
                            await lyAxis.SetPositionAsync(TarPos * AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION); 
                        }
                        break;
                    case 6://LZ
                        { 
                            var lzAxis = _axisFactory.GetLZAxisViewModel();
                            await lzAxis.SetPositionAsync(TarPos); 
                        }
                        break;
                    case 7://RX 
                        { 
                            var rxAxis = _axisFactory.GetRXAxisViewModel();
                            await rxAxis.SetPositionAsync(TarPos * AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION); 
                        }
                        break;
                    case 8://RY  
                        { 
                            var ryAxis = _axisFactory.GetRYAxisViewModel();
                            await ryAxis.SetPositionAsync(TarPos * AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION); 
                        }
                        break;
                    case 9://RZ  
                        { 
                            var rzAxis = _axisFactory.GetRZAxisViewModel();
                            await rzAxis.SetPositionAsync(TarPos); 
                        }
                        break;
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError(ex, $"轴{_id}位置设置失败", WaferAligner.EventIds.EventIds.Axis_Move_Error);
            }
        }

        private string IdToName(int id)
        {
            string name = "";
            switch (id)
            {
                case 0://U                    
                    { name = "X"; }
                    break;
                case 1://V
                    { name = "Y"; }
                    break;
                case 2://W
                    { name = "R"; }
                    break;
                case 3://Z
                    { name = "Z"; }
                    break;
                case 4://R
                    { name = "LX"; }
                    break;
                case 5://LX
                    { name = "LY"; }
                    break;
                case 6://LZ
                    { name = "LZ"; }
                    break;
                case 7://RX 
                    { name = "RX"; }
                    break;
                case 8://RZ  
                    { name = "RY"; }
                    break;
                case 9://RZ  
                    { name = "RZ"; }
                    break;
            }
            return name;

        }

        private async void TxtVel_KeyUp(object sender, KeyEventArgs e)
        {
            if (e.KeyCode != Keys.Enter)
            {
                return;
            }
            UITextBox temp = (UITextBox)sender;
            string TempText = temp.Text.Trim();
            double VelValue = 0;
            bool isJog = false;
            if (TempText == "+" || TempText == "-" || TempText == "")
            {
                return;
            }
            else if (TempText.EndsWith(".") || Convert.ToDouble(TempText) == 0)
            {
                return;
            }
            else
            {
                VelValue = Convert.ToDouble(TempText);
            }
            
            if (temp.Name.IndexOf("Jog") != -1)//JOG速度
            {
                if (VelValue > _JogSpeedMax || VelValue <= _JogSpeedMin)
                {
                    VelValue = 1;
                    temp.Text = Convert.ToString(1);
                    MessageBox.Show("速度设置超出速度极限，恢复默认值！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                }
                isJog = true;
                JogSpeed = VelValue;
            }
            else
            { //运行速度

                if (VelValue > _SpeedMax || VelValue <= _SpeedMin)
                {
                    VelValue = 5;
                    temp.Text = Convert.ToString(5);
                    MessageBox.Show("速度设置超出速度极限，恢复默认值！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                }
                isJog = false;
                Speed = VelValue;

            }//if (temp.Name.IndexOf("Jog") != -1)//JOG速度
            
            try 
            {
                // 检查服务是否可用
                if (_axisFactory == null)
                {
                    _loggingService?.LogError($"轴{_id}速度设置失败：轴工厂未注入", WaferAligner.EventIds.EventIds.Service_Unavailable);
                    return;
                }
                
                switch (_id)
                {
                    case 0://X
                        {
                            var xAxis = _axisFactory.GetXAxisViewModel();
                            if (isJog)
                                await xAxis.SetJogSpeedAsync(Convert.ToUInt32(VelValue * AxisConstants.AXIS_XY_MULTIPLE_CONVERTION));
                            else
                                await xAxis.SetRunSpeedAsync(Convert.ToUInt32(VelValue * AxisConstants.AXIS_XY_MULTIPLE_CONVERTION));
                        }
                        break;
                    case 1://Y
                        {
                            var yAxis = _axisFactory.GetYAxisViewModel();
                            if (isJog)
                                await yAxis.SetJogSpeedAsync(Convert.ToUInt32(VelValue * AxisConstants.AXIS_XY_MULTIPLE_CONVERTION));
                            else
                                await yAxis.SetRunSpeedAsync(Convert.ToUInt32(VelValue * AxisConstants.AXIS_XY_MULTIPLE_CONVERTION));
                        }
                        break;
                    case 2://R
                        {
                            var rAxis = _axisFactory.GetRAxisViewModel();
                            if (isJog)
                                await rAxis.SetJogSpeedAsync(Convert.ToUInt32(VelValue * AxisConstants.AXIS_R_MULTIPLE_CONVERTION));
                            else
                                await rAxis.SetRunSpeedAsync(Convert.ToUInt32(VelValue * AxisConstants.AXIS_R_MULTIPLE_CONVERTION));
                        }
                        break;
                    case 3://Z
                        {
                            var zAxis = _axisFactory.GetZAxisViewModel();
                            if (isJog)
                                await zAxis.SetJogSpeedAsync(VelValue * AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION);
                            else
                                await zAxis.SetRunSpeedAsync(VelValue * AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION);
                        }
                        break;
                    case 4://LX
                        {
                            var lxAxis = _axisFactory.GetLXAxisViewModel();
                            if (isJog)
                                await lxAxis.SetJogSpeedAsync(VelValue * AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION);
                            else
                                await lxAxis.SetRunSpeedAsync(VelValue * AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION);
                        }
                        break;
                    case 5://LY
                        {
                            var lyAxis = _axisFactory.GetLYAxisViewModel();
                            if (isJog)
                                await lyAxis.SetJogSpeedAsync(VelValue * AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION);
                            else
                                await lyAxis.SetRunSpeedAsync(VelValue * AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION);
                        }
                        break;
                    case 6://LZ
                        {
                            var lzAxis = _axisFactory.GetLZAxisViewModel();
                            if (isJog)
                                await lzAxis.SetJogSpeedAsync(VelValue);
                            else
                                await lzAxis.SetRunSpeedAsync(VelValue);
                        }
                        break;
                    case 7://RX 
                        {
                            var rxAxis = _axisFactory.GetRXAxisViewModel();
                            if (isJog)
                                await rxAxis.SetJogSpeedAsync(VelValue * AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION);
                            else
                                await rxAxis.SetRunSpeedAsync(VelValue * AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION);
                        }
                        break;
                    case 8://RY 
                        {
                            var ryAxis = _axisFactory.GetRYAxisViewModel();
                            if (isJog)
                                await ryAxis.SetJogSpeedAsync(VelValue * AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION);
                            else
                                await ryAxis.SetRunSpeedAsync(VelValue * AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION);
                        }
                        break;
                    case 9://RZ  
                        {
                            var rzAxis = _axisFactory.GetRZAxisViewModel();
                            if (isJog)
                                await rzAxis.SetJogSpeedAsync(VelValue);
                            else
                                await rzAxis.SetRunSpeedAsync(VelValue);
                        }
                        break;
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError(ex, $"轴{_id}速度设置失败", WaferAligner.EventIds.EventIds.Axis_Speed_Set_Failed);
            }
        }
        private void TxtLimit_KeyUp(object sender, KeyEventArgs e)
        {
            UITextBox temp = (UITextBox)sender;
            string TempText = temp.Text.Trim();
            float LimitPos = 0;
            if (TempText == "+" || TempText == "-" || TempText == "")
            {
                return;
            }
            else if (TempText.EndsWith(".") || Convert.ToDouble(TempText) == 0)
            {
                return;
            }
            else
            {
                LimitPos = (float)Convert.ToDouble(temp.Text.Trim());
            }

            if (!temp.Name.Contains("Pos"))//负
            {
                // 使用PosMin属性设置（它已经包含了对兼容性服务的调用）
                PosMin = LimitPos;
            }
            else
            { //正
                // 使用PosMax属性设置（它已经包含了对兼容性服务的调用）
                PosMax = LimitPos;
            }
        }


        private bool IsLimit(double num)
        {
            return num < _PosMin || num > _PosMax;
        }
        #endregion 文本款输入 目标位置 目标速度


        void BtnManage(bool EnableState)
        {
            BtnZero.Enabled = EnableState;
            BtnZPos.Enabled = EnableState;
            BtnZFront.Enabled = EnableState;
            BtnZBack.Enabled = EnableState;
            TxtTargetPos.Enabled = EnableState;
            TxtNegLimit.Enabled = EnableState;
            TxtPosLimit.Enabled = EnableState;
            TxtTargetVel.Enabled = EnableState;
            TxtJogVel.Enabled = EnableState;
        }

        // 新增：性能测试方法（可选，用于调试）
        public void TestPerformance()
        {
            if (_loggingService != null)
            {
                _loggingService.LogDebug($"Axis {_id} Performance Test:", WaferAligner.EventIds.EventIds.Plc_Performance_Warning);
                _loggingService.LogDebug($"Update Interval: {UPDATE_INTERVAL_MS}ms", WaferAligner.EventIds.EventIds.Plc_Performance_Warning);
                _loggingService.LogDebug($"Last Update Time: {_lastUpdateTime}", WaferAligner.EventIds.EventIds.Plc_Performance_Warning);
                _loggingService.LogDebug($"Timer Active: {_timer?.Enabled}", WaferAligner.EventIds.EventIds.Plc_Performance_Warning);
            }
        }

        // 添加辅助方法，根据轴ID获取对应的PLC名称
        /// <summary>
        /// 更准确地检测PLC连接状态
        /// </summary>
        private bool IsPlcReallyConnected(string plcConnectionName)
        {
            try
            {
                // 首先检查基本连接状态
                bool basicConnection = _plcVariableService?.IsConnectionOpen(plcConnectionName) ?? false;

                if (!basicConnection)
                {
                    return false;
                }

                // 如果基本连接正常，可以添加更多验证
                // 例如：尝试读取一个简单的PLC变量来验证通信是否正常
                // 这里暂时只使用基本连接检查，避免在Load事件中进行复杂操作

                return true;
            }
            catch (Exception ex)
            {
                _loggingService?.LogWarning($"检查PLC连接状态时发生异常: {ex.Message}", WaferAligner.EventIds.EventIds.Plc_Connection_Check_Error);
                return false;
            }
        }

        private string GetPlcNameFromAxisId(int axisId)
        {
            return axisId switch
            {
                0 => "XYRAxis", // X轴 - 串口通信
                1 => "XYRAxis", // Y轴 - 串口通信
                2 => "XYRAxis", // R轴 - 串口通信
                3 => "ZAxis", // Z轴 - PLC通信
                4 => "LeftX", // 左X轴 - PLC通信
                5 => "LeftY", // 左Y轴 - PLC通信
                6 => "LeftZ", // 左Z轴 - PLC通信
                7 => "RightX", // 右X轴 - PLC通信
                8 => "RightY", // 右Y轴 - PLC通信
                9 => "RightZ", // 右Z轴 - PLC通信
                _ => "XYRAxis"
            };
        }

        public Dictionary<String, Action<object>> VariableChangeActions = new();

        // 添加SafeInvoke方法实现
        protected bool SafeInvoke(Action action)
        {
            if (action == null)
                return false;

            try
            {
                // 检查是否正在清理中
                if (_isCleaningUp)
                {
                    _loggingService?.LogDebug($"控件正在清理中，忽略UI操作: {this.GetType().Name}", WaferAligner.EventIds.EventIds.UI_Update_Failed);
                    return false;
                }
                
                // 检查控件状态 - 使用本地变量捕获状态，避免竞态条件
                bool isDisposed, isDisposing, hasHandle;
                try
                {
                    isDisposed = this.IsDisposed;
                    isDisposing = this.Disposing;
                    hasHandle = this.IsHandleCreated;
                }
                catch (ObjectDisposedException)
                {
                    // 如果在获取状态时已经抛出异常，说明控件已被释放
                    _loggingService?.LogDebug($"控件已释放，无法执行UI操作: {this.GetType().Name}", WaferAligner.EventIds.EventIds.UI_Update_Failed);
                    return false;
                }
                
                // 如果控件已释放或正在释放，则不执行UI操作
                if (isDisposed || isDisposing || !hasHandle)
                {
                    _loggingService?.LogDebug($"控件已释放，无法执行UI操作: {this.GetType().Name}", WaferAligner.EventIds.EventIds.UI_Update_Failed);
                    return false;
                }

                // 使用BeginInvoke代替Invoke，避免死锁
                if (this.InvokeRequired)
                {
                    try
                    {
                        // 再次检查控件状态，防止在检查和调用之间控件被释放
                        if (this.IsDisposed || this.Disposing || !this.IsHandleCreated || _isCleaningUp)
                        {
                            _loggingService?.LogDebug($"控件已释放或正在清理中，取消UI操作", WaferAligner.EventIds.EventIds.UI_Update_Failed);
                            return false;
                        }
                        
                        // 使用BeginInvoke异步执行，避免死锁
                        this.BeginInvoke(action);
                    }
                    catch (ObjectDisposedException ex)
                    {
                        _loggingService?.LogDebug($"控件已释放，无法执行UI操作: {ex.Message}", WaferAligner.EventIds.EventIds.UI_Update_Failed);
                        return false;
                    }
                    catch (InvalidOperationException ex)
                    {
                        _loggingService?.LogDebug($"控件句柄无效，无法执行UI操作: {ex.Message}", WaferAligner.EventIds.EventIds.UI_Update_Failed);
                        return false;
                    }
                    catch (Exception ex)
                    {
                        _loggingService?.LogError(ex, $"在UI线程执行操作时发生错误: {ex.Message}", WaferAligner.EventIds.EventIds.UI_Update_Failed);
                        return false;
                    }
                }
                else
                {
                    try
                    {
                        // 再次检查控件状态
                        if (this.IsDisposed || this.Disposing || _isCleaningUp)
                        {
                            _loggingService?.LogDebug($"控件已释放或正在清理中，取消UI操作", WaferAligner.EventIds.EventIds.UI_Update_Failed);
                            return false;
                        }
                        
                        action();
                    }
                    catch (Exception ex)
                    {
                        _loggingService?.LogError(ex, $"执行UI操作时发生错误: {ex.Message}", WaferAligner.EventIds.EventIds.UI_Update_Failed);
                        return false;
                    }
                }
                return true;
            }
            catch (Exception ex)
            {
                _loggingService?.LogError(ex, $"SafeInvoke发生未处理异常: {ex.Message}", WaferAligner.EventIds.EventIds.UI_Update_Failed);
                return false;
            }
        }

        #region  load
        #endregion
    }
}
