﻿using System;
using System.Runtime.InteropServices;
using System.Text;
using WaferAligner.EventIds;
using WaferAligner.Communication.Serial.Interfaces;
using WaferAligner.Services.Logging.Abstractions;
using WaferAligner.Services.Logging.Extensions;
namespace WaferAligner.Communication.Serial.Implementation
{
    /// <summary>
    /// SerialCom.dll封装类
    /// </summary>
    public class SerialComWrapper : ISerialComWrapper
    {
        private readonly ILoggingService _loggingService;
        private bool _disposed = false;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="loggingService">日志服务</param>
        public SerialComWrapper(ILoggingService loggingService)
        {
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
            _loggingService.LogDebug("创建SerialComWrapper", WaferAligner.EventIds.EventIds.Serial_Axis_Operation);
        }

        #region DLL导入

        [DllImport("SerialCom.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern Int32 GetDLLVersion();
        
        [DllImport("SerialCom.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern Int32 DLL_OpenQK(Int32 controlNum);
        
        [DllImport("SerialCom.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern Int32 DLL_OpenCom(Int32 Com, Int32 Baud);
        
        [DllImport("SerialCom.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern Int32 DLL_CloseCom();
        
        [DllImport("SerialCom.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern Int32 DLL_SetControlAxis(UInt32 Axis_Shift);
        
        [DllImport("SerialCom.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern Int32 DLL_ReadPosition(UInt32 Address);
        
        [DllImport("SerialCom.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern Int32 DLL_AxisEnable(UInt32 Address, char KG);
        
        [DllImport("SerialCom.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern Int32 DLL_PositionRelativeMove(UInt32 Address, Int32 Position);
        
        [DllImport("SerialCom.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern Int32 DLL_PositionAbsoluteMove(UInt32 Address, Int32 Position);
        
        [DllImport("SerialCom.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern Int32 DLL_SetZeroPosition(UInt32 Address);
        
        [DllImport("SerialCom.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern Int32 DLL_JOG_Move(UInt32 Address, char CMD);
        
        [DllImport("SerialCom.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern Int32 DLL_GetError();
        
        [DllImport("SerialCom.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern Int32 DLL_SetSpeed4(UInt32 Address, char Type, UInt32 countsPerS);
        
        [DllImport("SerialCom.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern Int32 DLL_GetSpeed4(UInt32 Address, char Type);
        
        [DllImport("SerialCom.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern Int32 DLL_GetAxisStatus(Int32 Address, Int32 StatusNum);
        
        [DllImport("SerialCom.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern Int32 DLL_ClearMotorError(Int32 Address);
        
        [DllImport("SerialCom.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern Int32 DLL_AxisStop(UInt32 Address);

        #endregion

        #region 接口实现

        /// <inheritdoc/>
        public int GetVersion()
        {
            CheckDisposed();
            try
            {
                return GetDLLVersion();
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, "获取DLL版本失败", WaferAligner.EventIds.EventIds.Serial_Read_Error);
                return -1;
            }
        }

        /// <inheritdoc/>
        public int OpenDevice(int controlNum)
        {
            CheckDisposed();
            try
            {
                return DLL_OpenQK(controlNum);
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"打开设备失败: controlNum={controlNum}", WaferAligner.EventIds.EventIds.Serial_Connection_Failed);
                return -1;
            }
        }

        /// <inheritdoc/>
        public int OpenComPort(int comPort, int baudRate)
        {
            CheckDisposed();
            try
            {
                _loggingService.LogDebug($"尝试打开串口: COM{comPort}, {baudRate}波特率", WaferAligner.EventIds.EventIds.Serial_Connection_Started);
                return DLL_OpenCom(comPort, baudRate);
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"打开串口失败: COM{comPort}, {baudRate}波特率", WaferAligner.EventIds.EventIds.Serial_Connection_Failed);
                return -1;
            }
        }

        /// <inheritdoc/>
        public int CloseComPort()
        {
            CheckDisposed();
            try
            {
                return DLL_CloseCom();
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, "关闭串口失败", WaferAligner.EventIds.EventIds.Serial_Connection_Failed);
                return -1;
            }
        }

        /// <inheritdoc/>
        public int SetControlAxis(uint axisShift)
        {
            CheckDisposed();
            try
            {
                return DLL_SetControlAxis(axisShift);
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"设置控制轴失败: axisShift={axisShift}", WaferAligner.EventIds.EventIds.Serial_Axis_Operation);
                return -1;
            }
        }

        /// <inheritdoc/>
        public int AxisEnable(uint address, char kg)
        {
            CheckDisposed();
            try
            {
                return DLL_AxisEnable(address, kg);
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"设置轴使能失败: address={address}, kg={kg}", WaferAligner.EventIds.EventIds.Serial_Axis_Enabled);
                return -1;
            }
        }

        /// <inheritdoc/>
        public int ReadPosition(uint address)
        {
            CheckDisposed();
            try
            {
                return DLL_ReadPosition(address);
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"读取位置失败: address={address}", WaferAligner.EventIds.EventIds.Serial_Read_Error);
                return 0;
            }
        }

        /// <inheritdoc/>
        public int PositionAbsoluteMove(uint address, int position)
        {
            CheckDisposed();
            try
            {
                return DLL_PositionAbsoluteMove(address, position);
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"绝对位置移动失败: address={address}, position={position}", WaferAligner.EventIds.EventIds.Serial_Axis_Moving);
                return -1;
            }
        }

        /// <inheritdoc/>
        public int PositionRelativeMove(uint address, int position)
        {
            CheckDisposed();
            try
            {
                return DLL_PositionRelativeMove(address, position);
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"相对位置移动失败: address={address}, position={position}", WaferAligner.EventIds.EventIds.Serial_Axis_Moving);
                return -1;
            }
        }

        /// <inheritdoc/>
        public int AxisStop(uint address)
        {
            CheckDisposed();
            try
            {
                return DLL_AxisStop(address);
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"停止轴运动失败: address={address}", WaferAligner.EventIds.EventIds.Serial_Axis_Stopped);
                return -1;
            }
        }

        /// <inheritdoc/>
        public int GetAxisStatus(int address, int statusNum)
        {
            CheckDisposed();
            try
            {
                return DLL_GetAxisStatus(address, statusNum);
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"获取轴状态失败: address={address}, statusNum={statusNum}", WaferAligner.EventIds.EventIds.Serial_Read_Error);
                return -1;
            }
        }

        /// <inheritdoc/>
        public int ClearMotorError(int address)
        {
            CheckDisposed();
            try
            {
                return DLL_ClearMotorError(address);
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"清除电机错误失败: address={address}", WaferAligner.EventIds.EventIds.Serial_Axis_Operation);
                return -1;
            }
        }

        /// <inheritdoc/>
        public int SetSpeed(uint address, char type, uint countsPerS)
        {
            CheckDisposed();
            try
            {
                return DLL_SetSpeed4(address, type, countsPerS);
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"设置速度失败: address={address}, type={type}, countsPerS={countsPerS}", WaferAligner.EventIds.EventIds.Serial_Axis_Operation);
                return -1;
            }
        }

        /// <inheritdoc/>
        public int GetSpeed(uint address, char type)
        {
            CheckDisposed();
            try
            {
                return DLL_GetSpeed4(address, type);
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"获取速度失败: address={address}, type={type}", WaferAligner.EventIds.EventIds.Serial_Read_Error);
                return -1;
            }
        }

        /// <inheritdoc/>
        public int SetZeroPosition(uint address)
        {
            CheckDisposed();
            try
            {
                return DLL_SetZeroPosition(address);
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"设置零位置失败: address={address}", WaferAligner.EventIds.EventIds.Serial_Axis_Operation);
                return -1;
            }
        }

        /// <inheritdoc/>
        public int JogMove(uint address, char cmd)
        {
            CheckDisposed();
            try
            {
                return DLL_JOG_Move(address, cmd);
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"JOG移动失败: address={address}, cmd={cmd}", WaferAligner.EventIds.EventIds.Serial_Axis_Moving);
                return -1;
            }
        }

        /// <summary>
        /// 检查对象是否已释放
        /// </summary>
        private void CheckDisposed()
        {
            if (_disposed)
            {
                throw new ObjectDisposedException(nameof(SerialComWrapper));
            }
        }

        /// <inheritdoc/>
        public void Dispose()
        {
            if (_disposed)
                return;

            _disposed = true;
            
            try
            {
                // 关闭串口连接
                CloseComPort();
                _loggingService.LogDebug("释放SerialComWrapper资源", WaferAligner.EventIds.EventIds.Serial_Resource_Released);
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, "释放SerialComWrapper资源时发生错误", WaferAligner.EventIds.EventIds.Serial_Resource_Released);
            }
        }

        #endregion
    }
}
