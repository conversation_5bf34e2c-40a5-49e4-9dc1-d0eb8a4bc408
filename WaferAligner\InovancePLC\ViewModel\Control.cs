﻿using WaferAligner.Communication.Inovance.Abstractions;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using WaferAligner.Services.Logging.Extensions;
using WaferAligner.Services.Logging.Abstractions;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json.Linq;
using Sunny.UI.Demo;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;
using WaferAligner;
using WaferAligner.Common;
using WaferAligner.EventIds;
using WaferAligner.Interfaces;
using WaferAligner.Services;

namespace AlignerUI
{
    public enum CylinderStatus
    {
        Close = 0,
        Open = 1,
        None = 2
    }

    public enum TakePositionSelect
    {
        Up = 0,
        Down = 1,
        None = 2,
        TakeDown = 3,
        TakeUp = 4,
        InitialZ = 5
    }

    public enum WaferSeries
    {
        Normal = 0,
        Special = 1
    }

    public partial class MainWindowViewModel
    {
        // 删除重复的_loggingService字段定义和构造函数
        // 使用现有的_loggingService字段

        #region PLC variable manual control variable

        [ObservableProperty] private double topWaferPhotoZ = 0;//ZTakePhoto//上拍照位
        [ObservableProperty] private double bottomWaferPhotoZ = 0;//TakeDownWafer//下拍照位
        [ObservableProperty] private double topWaferTakeUpPos = 0;//TakeUpWafer//上吸合位
        [ObservableProperty] private double bottomWaferTakeDownPos = 0;//TakeDownWafer1//下贴片位
        [ObservableProperty] private double topWaferZLevel = 0;//ZLevel//调平位


        [ObservableProperty] private UInt16 topWaferState = (UInt16)CylinderStatus.None;
        [ObservableProperty] private UInt16 trayWaferOuterState = (UInt16)CylinderStatus.None;
        [ObservableProperty] private UInt16 trayWaferInnerState = (UInt16)CylinderStatus.None;
        [ObservableProperty] private UInt16 trayState = (UInt16)CylinderStatus.None;
        [ObservableProperty] private UInt16 chuckLockState = (UInt16)CylinderStatus.None;
        [ObservableProperty] private UInt16 horizontalAdjustState = (UInt16)CylinderStatus.None;

        [ObservableProperty] private bool isAuto = false;
        [ObservableProperty] private bool startExecute = false;
        [ObservableProperty] private bool resetExecute = false;
        [ObservableProperty] private bool stopExecute = false;
        [ObservableProperty] private bool pauseExecute = false;

        [ObservableProperty] private bool initAll = false;
        [ObservableProperty] private bool clearSystemTag = false;

        [ObservableProperty] private bool chuckReady = false;
        [ObservableProperty] private bool bottomWaferReady = false;
        [ObservableProperty] private bool topWaferReady = false;

        [ObservableProperty] private bool active6InchEnabled = false;
        [ObservableProperty] private bool active8InchEnabled = false;
        [ObservableProperty] private bool calUVW = false;
        [ObservableProperty] private bool calR = false;

        [ObservableProperty] private bool evaChuck = false;
        [ObservableProperty] private bool evaBottomWafer = false;

        [ObservableProperty] private bool evaTop8 = false;
        [ObservableProperty] private bool horAdjustLocked = false;
        [ObservableProperty] private bool hirAdjustUnLocked = false;
        [ObservableProperty] private bool clampPushed = false;
        [ObservableProperty] private bool clampUnpushed = false;
        [ObservableProperty] private bool clampUpMoving = false;
        [ObservableProperty] private bool clampDownMoving = false;
        [ObservableProperty] private bool spacerPushed = false;
        [ObservableProperty] private bool spacerPulled = false;
        [ObservableProperty] private bool qd4Reseted = false;
        [ObservableProperty] private bool qd5Reseted = false;
        [ObservableProperty] private bool targetPhotoed = false;
        [ObservableProperty] private bool objectedPhotoed = false;
        [ObservableProperty] private bool aligned = false;
        [ObservableProperty] private bool moreAligned = false;

        #endregion

        /// <summary>
        /// 更新状态文本的辅助方法
        /// </summary>
        /// <param name="status">状态文本</param>
        private void UpdateStatus(string status)
        {
            _statusUpdateService?.UpdateStatus(status);
        }


        #region Command manual control
        [RelayCommand]
        public async Task ChangeControlMode() => await WritePLCVariable($"{AxisConstants.AXIS_GVL}.hb_手自动", !IsAuto);

        [RelayCommand]
        public async Task Start() => await WritePLCVariable($"{AxisConstants.AXIS_GVL}.hb_启动", !StartExecute);
        [RelayCommand]
        public async Task Reset()
        {
            await WritePLCVariable($"{AxisConstants.AXIS_GVL}.hb_复位", !ResetExecute);
            await Task.Delay(2000);

        }
        [RelayCommand]
        public async Task Pause() => await WritePLCVariable($"{AxisConstants.AXIS_GVL}.hb_暂停", !PauseExecute);
        [RelayCommand]
        public async Task Stop() => await WritePLCVariable($"{AxisConstants.AXIS_GVL}.hb_停止", !StopExecute);

        [RelayCommand]
        public async Task ClearTag() => await WritePLCVariable($"{AxisConstants.AXIS_GVL}.hb_系统标记清除", !ClearSystemTag);

        [RelayCommand]
        public async Task ConfirmChuckReady() => await WritePLCVariable($"{AxisConstants.AXIS_GVL}.hb_人工确认卡盘OK", !ChuckReady);
        [RelayCommand]
        public async Task ConfirmTopWaferReady() => await WritePLCVariable($"{AxisConstants.AXIS_GVL}.hb_人工确认上晶圆OK", !TopWaferReady);
        [RelayCommand]
        public async Task ConfirmBottomWaferReady() => await WritePLCVariable($"{AxisConstants.AXIS_GVL}.hb_人工确认下晶圆OK", !BottomWaferReady);

        [RelayCommand]
        public async Task Active6Inch() => await WritePLCVariable($"{AxisConstants.AXIS_GVL}.hb_当前产品6寸", !Active6InchEnabled);
        [RelayCommand]
        public async Task Active8Inch() => await WritePLCVariable($"{AxisConstants.AXIS_GVL}.hb_当前产品8寸", !Active8InchEnabled);

        #region  初始化
        [RelayCommand]
        public async Task<bool> Init()
        {
            //LZ RZ回原点并运动至10
            #region 1:LZ RZ回原点
            UpdateStatus("1：LZ、RZ初始化中...");
            
            // 使用_axisEventService替代ConstValue静态引用
            ICameraAxisViewModel leftZAxis = null, rightZAxis = null;
            leftZAxis = await _axisEventService?.GetCameraAxisViewModelAsync("Left", "Z");
            rightZAxis = await _axisEventService?.GetCameraAxisViewModelAsync("Right", "Z");
            
            if (leftZAxis != null && rightZAxis != null)
            {
                try
                {
                    // 处理ICameraAxisViewModel没有SetZeroPoint方法的问题
                    // 方法1: 通过HomeAsync方法实现相同功能 (IAxisViewModel接口中的方法)
                    await leftZAxis.HomeAsync();
                    await rightZAxis.HomeAsync();
                    
                    PLCVarReadInfo ReadLZHomeDone = new() { Name = $"{AxisConstants.AXIS_GVL}.LZHomeSetDone", Type = typeof(bool) };
                    PLCVarReadInfo ReadRZHomeDone = new() { Name = $"{AxisConstants.AXIS_GVL}.RZHomeSetDone", Type = typeof(bool) };
                    
                    // 使用_plcVariableService替代直接PLC访问
                    object retReadLZHomeDone = await _plcVariableService?.ReadVariableSafelyAsync<object>(ReadLZHomeDone.Name, false);
                    object retReadRZHomeDone = await _plcVariableService?.ReadVariableSafelyAsync<object>(ReadRZHomeDone.Name, false);
                    
                    int i = 0;
                    while (!Convert.ToBoolean(retReadLZHomeDone) && !Convert.ToBoolean(retReadRZHomeDone))
                    {
                        await Task.Delay(50);
                        retReadLZHomeDone = await _plcVariableService?.ReadVariableSafelyAsync<object>(ReadLZHomeDone.Name, false);
                        retReadRZHomeDone = await _plcVariableService?.ReadVariableSafelyAsync<object>(ReadRZHomeDone.Name, false);
                        i++;
                        if (i > 300) break;
                    }
                    
                    if (i == 301)
                    {
                        UpdateStatus("1：LZ、RZ初始化失败");
                        _loggingService?.LogError($"1：LZ、RZ初始化失败", EventIds.Axis_Move_Error);
                        return false;
                    }
                    
                    UpdateStatus("1：LZ、RZ初始化完成");
                    await Task.Delay(10);
                }
                catch (Exception ex)
                {
                    _loggingService?.LogError(ex, "使用IAxisViewModel.HomeAsync初始化LZ、RZ时发生错误", EventIds.Axis_Move_Error);
                    
                    // 回退到使用兼容层
                    _loggingService?.LogWarning("尝试使用服务初始化LZ、RZ", EventIds.Service_Initialization_Failed);
                    // 只赋值，不再声明
                    leftZAxis = await _axisEventService?.GetCameraAxisViewModelAsync("Left", "Z");
                    rightZAxis = await _axisEventService?.GetCameraAxisViewModelAsync("Right", "Z");
                    
                    if (leftZAxis != null && rightZAxis != null)
                    {
                        await leftZAxis.HomeAsync();
                        await rightZAxis.HomeAsync();
                        
                        // 使用_plcVariableService替代ConstValue.PLC
                        bool retReadLZHomeDone = await _plcVariableService?.ReadVariableSafelyAsync<bool>($"{AxisConstants.AXIS_GVL}.LZHomeSetDone", false);
                        bool retReadRZHomeDone = await _plcVariableService?.ReadVariableSafelyAsync<bool>($"{AxisConstants.AXIS_GVL}.RZHomeSetDone", false);
                        int i = 0;
                        while (!(retReadLZHomeDone && retReadRZHomeDone))
                        {
                            await Task.Delay(50);
                            retReadLZHomeDone = await _plcVariableService?.ReadVariableSafelyAsync<bool>($"{AxisConstants.AXIS_GVL}.LZHomeSetDone", false);
                            retReadRZHomeDone = await _plcVariableService?.ReadVariableSafelyAsync<bool>($"{AxisConstants.AXIS_GVL}.RZHomeSetDone", false);
                            i++;
                            if (i > 300) break;
                        }
                        if (i == 301)
                        {
                            UpdateStatus("1：LZ、RZ初始化失败");
                            _loggingService?.LogError($"1：LZ、RZ初始化失败", EventIds.Axis_Move_Error);
                            return false;
                        }
                        UpdateStatus("1：LZ、RZ初始化完成");
                        await Task.Delay(10);
                    }
                    else
                    {
                        _loggingService?.LogError("无法获取相机Z轴，初始化失败", EventIds.Axis_Move_Error);
                        return false;
                    }
                }
            }
            else
            {
                // 如果无法获取轴服务，尝试使用更直接的方式
                _loggingService?.LogWarning("无法获取轴服务，尝试使用备用方案", EventIds.Service_Initialization_Failed);
                try
                {
                    // 使用_axisEventService
                    leftZAxis = await _axisEventService?.GetCameraAxisViewModelAsync("Left", "Z");
                    rightZAxis = await _axisEventService?.GetCameraAxisViewModelAsync("Right", "Z");
                    
                    if (leftZAxis != null && rightZAxis != null)
                    {
                        await leftZAxis.HomeAsync();
                        await rightZAxis.HomeAsync();
                        
                        // 使用_plcVariableService
                        bool retReadLZHomeDone = await _plcVariableService?.ReadVariableSafelyAsync<bool>($"{AxisConstants.AXIS_GVL}.LZHomeSetDone", false);
                        bool retReadRZHomeDone = await _plcVariableService?.ReadVariableSafelyAsync<bool>($"{AxisConstants.AXIS_GVL}.RZHomeSetDone", false);
                        int i = 0;
                        while (!(retReadLZHomeDone && retReadRZHomeDone))
                        {
                            await Task.Delay(50);
                            retReadLZHomeDone = await _plcVariableService?.ReadVariableSafelyAsync<bool>($"{AxisConstants.AXIS_GVL}.LZHomeSetDone", false);
                            retReadRZHomeDone = await _plcVariableService?.ReadVariableSafelyAsync<bool>($"{AxisConstants.AXIS_GVL}.RZHomeSetDone", false);
                            i++;
                            if (i > 300) break;
                        }
                        if (i == 301)
                        {
                            UpdateStatus("1：LZ、RZ初始化失败");
                            _loggingService?.LogError($"1：LZ、RZ初始化失败", EventIds.Axis_Move_Error);
                            return false;
                        }
                        UpdateStatus("1：LZ、RZ初始化完成");
                        await Task.Delay(10);
                    }
                    else
                    {
                        _loggingService?.LogError("无法获取相机Z轴，初始化失败", EventIds.Axis_Move_Error);
                        return false;
                    }
                }
                catch (Exception ex)
                {
                    _loggingService?.LogError(ex, "初始化LZ、RZ失败", EventIds.Axis_Move_Error);
                    return false;
                }
            }
            #endregion LZ RZ回原点

            #region 2:LZ RZ到10
            UpdateStatus("2：LY、LZ、RY、RZ运动至安全位置中...");

            try
            {
                // 使用依赖注入的服务替代ConstValue
                var leftYAxis = await _axisEventService?.GetCameraAxisViewModelAsync("Left", "Y");
                var rightYAxis = await _axisEventService?.GetCameraAxisViewModelAsync("Right", "Y");
                // 使用不同的变量名避免重复声明
                var leftZAxisMove = await _axisEventService?.GetCameraAxisViewModelAsync("Left", "Z");
                var rightZAxisMove = await _axisEventService?.GetCameraAxisViewModelAsync("Right", "Z");

                if (leftYAxis != null && rightYAxis != null && leftZAxisMove != null && rightZAxisMove != null)
                {
                    // 使用SetPositionAsync方法替代SetPosition (IAxisViewModel接口中的方法)
                    // 使用AxisConstants替代ConstValue
                    await leftYAxis.SetPositionAsync(30 * AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION);//30mm
                    await rightYAxis.SetPositionAsync(30 * AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION);//30mm
                    await leftZAxisMove.SetPositionAsync(10);//10mm
                    await rightZAxisMove.SetPositionAsync(10);//10mm
                    await Task.Delay(100);
                    
                    // MoveToPositionAsync触发轴运动，相当于GoPosition方法
                    await leftYAxis.MoveToPositionAsync(30 * AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION);
                    await rightYAxis.MoveToPositionAsync(30 * AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION);
                    await leftZAxisMove.MoveToPositionAsync(10);
                    await rightZAxisMove.MoveToPositionAsync(10);
                    
                    // 使用_plcVariableService替代直接PLC访问
                    bool retReadLZDone = await _plcVariableService?.ReadVariableSafelyAsync<bool>($"{AxisConstants.AXIS_GVL}.LZAbsDone", false);
                    bool retReadRZDone = await _plcVariableService?.ReadVariableSafelyAsync<bool>($"{AxisConstants.AXIS_GVL}.RZAbsDone", false);
                    bool retReadLYDone = await _plcVariableService?.ReadVariableSafelyAsync<bool>($"{AxisConstants.AXIS_GVL}.LYAbsDone", false);
                    bool retReadRYDone = await _plcVariableService?.ReadVariableSafelyAsync<bool>($"{AxisConstants.AXIS_GVL}.RYAbsDone", false);
                    
                    await Task.Delay(200);
                    int i = 0;
                    while (!(retReadLZDone && retReadRZDone && retReadLYDone && retReadRYDone))
                    {
                        await Task.Delay(50);
                        retReadLZDone = await _plcVariableService?.ReadVariableSafelyAsync<bool>($"{AxisConstants.AXIS_GVL}.LZAbsDone", false);
                        retReadRZDone = await _plcVariableService?.ReadVariableSafelyAsync<bool>($"{AxisConstants.AXIS_GVL}.RZAbsDone", false);
                        retReadLYDone = await _plcVariableService?.ReadVariableSafelyAsync<bool>($"{AxisConstants.AXIS_GVL}.LYAbsDone", false);
                        retReadRYDone = await _plcVariableService?.ReadVariableSafelyAsync<bool>($"{AxisConstants.AXIS_GVL}.RYAbsDone", false);
                        i++;
                        if (i > 600) break;
                    }
                    
                    if (i == 601)
                    {
                        UpdateStatus("2：LY、LZ、RY、RZ运动至安全位置失败");
                        _loggingService?.LogError($"2：LY、LZ、RY、RZ运动至安全位置失败", EventIds.Axis_Move_Error);
                        return false;
                    }
                    
                    UpdateStatus("2：LY、LZ、RY、RZ运动至安全位置完成");
                    await Task.Delay(10);
                }
                else
                {
                    // 如果无法获取轴服务，尝试使用备用方案
                    _loggingService?.LogWarning("无法获取轴服务，尝试使用备用方案", EventIds.Service_Initialization_Failed);
                    
                    // 如果_axisEventService有，但获取的相机轴对象为空，尝试再次获取不同的轴
                    if (_axisEventService != null)
                    {
                        try
                        {
                            await _axisEventService.MoveCameraAxesToPositionAsync(
                                0, // lx - 不移动
                                30 * AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION, // ly - 30mm
                                10, // lz - 10mm
                                0, // rx - 不移动
                                30 * AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION, // ry - 30mm
                                10 // rz - 10mm
                            );
                            
                            // 等待轴到达位置
                            bool retReadLZDone = false;
                            bool retReadRZDone = false;
                            bool retReadLYDone = false;
                            bool retReadRYDone = false;
                            int retryCount = 0;
                            
                            while (!(retReadLZDone && retReadRZDone && retReadLYDone && retReadRYDone))
                            {
                                await Task.Delay(50);
                                retReadLZDone = await _plcVariableService?.ReadVariableSafelyAsync<bool>($"{AxisConstants.AXIS_GVL}.LZAbsDone", false);
                                retReadRZDone = await _plcVariableService?.ReadVariableSafelyAsync<bool>($"{AxisConstants.AXIS_GVL}.RZAbsDone", false);
                                retReadLYDone = await _plcVariableService?.ReadVariableSafelyAsync<bool>($"{AxisConstants.AXIS_GVL}.LYAbsDone", false);
                                retReadRYDone = await _plcVariableService?.ReadVariableSafelyAsync<bool>($"{AxisConstants.AXIS_GVL}.RYAbsDone", false);
                                retryCount++;
                                if (retryCount > 600) break;
                            }
                            
                            if (retryCount > 600)
                            {
                                UpdateStatus("2：LY、LZ、RY、RZ运动至安全位置失败");
                                _loggingService?.LogError("2：LY、LZ、RY、RZ运动至安全位置失败", EventIds.Axis_Move_Error);
                                return false;
                            }
                            
                            UpdateStatus("2：LY、LZ、RY、RZ运动至安全位置完成");
                            await Task.Delay(10);
                        }
                        catch (Exception ex)
                        {
                            _loggingService?.LogError(ex, "使用MoveCameraAxesToPositionAsync移动轴失败", EventIds.Axis_Move_Error);
                            return false;
                        }
                    }
                    else
                    {
                        _loggingService?.LogError("轴服务不可用", EventIds.Service_Unavailable);
                        return false;
                    }
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError(ex, "LY、LZ、RY、RZ运动至安全位置失败", EventIds.Axis_Move_Error);
                return false;
            }
            #endregion LZ  RZ到10

            #region 3:Z回原点
            UpdateStatus("3：Z初始化中...");
            try
            {
                ICameraAxisViewModel zAxis = null;
                // 使用依赖注入的服务获取Z轴
                var zAxisPosition = await _axisEventService?.GetAxisPositionAsync(AxisConstants.AxisNames.Z_AXIS);
                
                if (_axisEventService != null)
                {
                    // 使用_axisEventService执行Z轴归零
                    await _axisEventService.MoveToPositionAsync(AxisConstants.AxisNames.Z_AXIS, 0);
                    await Task.Delay(100);
                    
                    // 使用_plcVariableService读取PLC变量
                    bool zHomeDone = false;
                    int i = 0;
                    
                    while (!zHomeDone && i < 4000)
                    {
                        await Task.Delay(50);
                        zHomeDone = await _plcVariableService?.ReadVariableSafelyAsync<bool>($"{AxisConstants.AXIS_GVL}.ZHomeFinished", false);
                        i++;
                    }
                    
                    if (i >= 4000)
                    {
                        UpdateStatus("3：Z初始化失败");
                        _loggingService?.LogError($"3：Z初始化失败", EventIds.Axis_Move_Error);
                        return false;
                    }
                    
                    UpdateStatus("3：Z初始化完成");
                    await Task.Delay(10);
                }
                else
                {
                    // 使用服务方式初始化Z轴
                    _loggingService?.LogWarning("尝试使用服务初始化Z轴", EventIds.Service_Initialization_Failed);
                    
                    // 使用_axisEventService获取Z轴视图模型
                    zAxis = await _axisEventService?.GetCameraAxisViewModelAsync("Left", "Z");
                    if (zAxis != null)
                    {
                        // 调用HomeAsync方法代替SetZeroPoint
                        await zAxis.HomeAsync();
                        await Task.Delay(100);
                        
                        // 使用_plcVariableService读取变量
                        bool retReadZDone = await _plcVariableService?.ReadVariableSafelyAsync<bool>($"{AxisConstants.AXIS_GVL}.ZHomeFinished", false);
                        int i = 0;
                        while (!retReadZDone)
                        {
                            await Task.Delay(50);
                            retReadZDone = await _plcVariableService?.ReadVariableSafelyAsync<bool>($"{AxisConstants.AXIS_GVL}.ZHomeFinished", false);
                            i++;
                            if (i > 4000) break;
                        }
                        
                        if (i == 4001)
                        {
                            UpdateStatus("3：Z初始化失败");
                            _loggingService?.LogError($"3：Z初始化失败", EventIds.Axis_Move_Error);
                            return false;
                        }
                        
                        UpdateStatus("3：Z初始化完成");
                        await Task.Delay(10);
                    }
                    else
                    {
                        _loggingService?.LogError("无法获取Z轴视图模型", EventIds.Axis_Move_Error);
                        return false;
                    }
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError(ex, "Z轴初始化失败", EventIds.Axis_Move_Error);
                return false;
            }
            #endregion 3:Z回原点


            #region 4:XYR初始化    
            UpdateStatus("4：XYR初始化中...");
            try
            {
                if (_axisEventService != null)
                {
                    // 使用轴事件服务初始化XYR轴
                    bool success = true;
                    
                    // 初始化XYR轴
                    success &= await InitAxisWithRetryAsync(AxisConstants.AxisNames.X_AXIS);
                    success &= await InitAxisWithRetryAsync(AxisConstants.AxisNames.Y_AXIS);
                    success &= await InitAxisWithRetryAsync(AxisConstants.AxisNames.R_AXIS);
                    
                    if (!success)
                    {
                        UpdateStatus("4：XYR初始化失败");
                        _loggingService?.LogError($"4：XYR初始化失败", EventIds.Axis_Move_Error);
                        return false;
                    }
                    
                    UpdateStatus("4：XYR初始化完成");
                    await Task.Delay(100);
                }
                else
                {
                    // 回退到备用初始化方式
                    _loggingService?.LogWarning("初始化XYR轴失败，尝试使用备用初始化方式", EventIds.Service_Initialization_Failed);
                    
                    try {
                        // 使用服务容器获取轴工厂并初始化各轴
                        var axisFactory = CommonFun.host?.Services.GetService<IAxisViewModelFactory>();
                        if (axisFactory != null)
                        {
                            int ret = 0;
                            var xAxis = axisFactory.GetXAxisViewModel();
                            var yAxis = axisFactory.GetYAxisViewModel();
                            var rAxis = axisFactory.GetRAxisViewModel();
                            
                            if (xAxis != null) await xAxis.InitAxisAsync();
                            if (yAxis != null) await yAxis.InitAxisAsync();
                            if (rAxis != null) await rAxis.InitAxisAsync();
                            
                            _loggingService?.LogInformation("使用备用方式初始化XYR轴成功", EventIds.Axis_Initialize_Succeeded);
                        }
                        else
                        {
                            _loggingService?.LogError("无法获取轴工厂服务，XYR轴初始化失败", EventIds.Service_Initialization_Failed);
                            return false;
                        }
                    }
                    catch (Exception ex) {
                        _loggingService?.LogError(ex, "备用初始化XYR轴失败", EventIds.Axis_Move_Error);
                        return false;
                    }
                    
                    UpdateStatus("4：XYR初始化完成");
                    await Task.Delay(100);
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError(ex, "XYR轴初始化失败", EventIds.Axis_Move_Error);
                return false;
            }
            #endregion 4:XYR初始化         
            return true;
        }
        
        /// <summary>
        /// 使用重试机制初始化轴
        /// </summary>
        /// <param name="axisName">轴名称</param>
        /// <returns>初始化是否成功</returns>
        private async Task<bool> InitAxisWithRetryAsync(string axisName)
        {
            try
            {
                // 尝试初始化轴
                var (xState, yState, rState) = _axisEventService.GetXYRRunStates();
                
                int retryCount = 0;
                const int maxRetry = 3;
                
                // 根据轴名称进行相应的初始化
                switch (axisName)
                {
                    case AxisConstants.AxisNames.X_AXIS:
                        // 如果X轴已在运行状态，等待它完成
                        while (xState != 0 && retryCount < maxRetry)
                        {
                            await Task.Delay(500);
                            (xState, _, _) = _axisEventService.GetXYRRunStates();
                            retryCount++;
                        }
                        return xState == 0;
                        
                    case AxisConstants.AxisNames.Y_AXIS:
                        // 如果Y轴已在运行状态，等待它完成
                        while (yState != 0 && retryCount < maxRetry)
                        {
                            await Task.Delay(500);
                            (_, yState, _) = _axisEventService.GetXYRRunStates();
                            retryCount++;
                        }
                        return yState == 0;
                        
                    case AxisConstants.AxisNames.R_AXIS:
                        // 如果R轴已在运行状态，等待它完成
                        while (rState != 0 && retryCount < maxRetry)
                        {
                            await Task.Delay(500);
                            (_, _, rState) = _axisEventService.GetXYRRunStates();
                            retryCount++;
                        }
                        return rState == 0;
                        
                    default:
                        _loggingService?.LogWarning($"未知轴名称: {axisName}", EventIds.Axis_Move_Error);
                        return false;
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError(ex, $"初始化轴失败: {axisName}", EventIds.Axis_Move_Error);
                return false;
            }
        }

        #endregion

        #region  IO控制

        [RelayCommand]//上晶圆吸附/释放
        public async Task TopWaferExecute()
        {
            try
            {
                await _plcVariableService.WriteVariableSafelyAsync($"{AxisConstants.AXIS_GVL}.UpperWaferState", TopWaferState);
                await Task.Delay(30);
                await _plcVariableService.WriteVariableSafelyAsync($"{AxisConstants.AXIS_GVL}.UpperWaferExecute", true);

                _loggingService?.LogInformation($"上晶圆气缸执行: 状态={TopWaferState}", EventIds.Cylinder_Operation_Started);
            }
            catch (Exception ex)
            {
                _loggingService?.LogError(ex, "上晶圆气缸执行失败");
            }
        }

        [RelayCommand]//托盘晶圆外吸附/释放
        public async Task TrayWaferOuterExecute()
        {
            try
            {
                await _plcVariableService.WriteVariableSafelyAsync($"{AxisConstants.AXIS_GVL}.LowerWaferChuckState", TrayWaferOuterState);
                await Task.Delay(30);
                await _plcVariableService.WriteVariableSafelyAsync($"{AxisConstants.AXIS_GVL}.LowerWaferChuckExecute", true);

                _loggingService?.LogInformation($"托盘晶圆外气缸执行: 状态={TrayWaferOuterState}", EventIds.Cylinder_Operation_Started);
            }
            catch (Exception ex)
            {
                _loggingService?.LogError(ex, "托盘晶圆外气缸执行失败");
            }
        }

        [RelayCommand]//托盘晶圆内吸附/释放
        public async Task TrayWaferInnerExecute()
        {
            try
            {
                await _plcVariableService.WriteVariableSafelyAsync($"{AxisConstants.AXIS_GVL}.LowerChuckState", TrayWaferInnerState);
                await Task.Delay(30);
                await _plcVariableService.WriteVariableSafelyAsync($"{AxisConstants.AXIS_GVL}.LowerChuckExecute", true);

                _loggingService?.LogInformation($"托盘晶圆内气缸执行: 状态={TrayWaferInnerState}", EventIds.Cylinder_Operation_Started);
            }
            catch (Exception ex)
            {
                _loggingService?.LogError(ex, "托盘晶圆内气缸执行失败");
            }
        }
        
        [RelayCommand]//托盘吸附
        public async Task TrayExecute()
        {
            try
            {
                await _plcVariableService.WriteVariableSafelyAsync($"{AxisConstants.AXIS_GVL}.LowerWaferState", TrayState);
                await Task.Delay(30);
                await _plcVariableService.WriteVariableSafelyAsync($"{AxisConstants.AXIS_GVL}.LowerWaferExecute", true);

                _loggingService?.LogInformation($"托盘气缸执行: 状态={TrayState}", EventIds.Cylinder_Operation_Started);
            }
            catch (Exception ex)
            {
                _loggingService?.LogError(ex, "托盘气缸执行失败");
            }
        }

        [RelayCommand]//卡盘锁紧
        public async Task ChuckLockExecute()
        {
            try
            {
                await _plcVariableService.WriteVariableSafelyAsync($"{AxisConstants.AXIS_GVL}.UpperChuckCylinderState", ChuckLockState);
                await Task.Delay(30);
                await _plcVariableService.WriteVariableSafelyAsync($"{AxisConstants.AXIS_GVL}.UpperChuckCylinderExecute", true);

                _loggingService?.LogInformation($"卡盘锁气缸执行: 状态={ChuckLockState}", EventIds.Cylinder_Operation_Started);
            }
            catch (Exception ex)
            {
                _loggingService?.LogError(ex, "卡盘锁气缸执行失败");
            }
        }
        
        [RelayCommand]//调平锁紧
        public async Task HorizontalAdjustLockExecute()
        {
            try
            {
                await _plcVariableService.WriteVariableSafelyAsync($"{AxisConstants.AXIS_GVL}.CylinderState", HorizontalAdjustState);
                await Task.Delay(30);
                await _plcVariableService.WriteVariableSafelyAsync($"{AxisConstants.AXIS_GVL}.CylinderExecute", true);

                _loggingService?.LogInformation($"水平调节气缸执行: 状态={HorizontalAdjustState}", EventIds.Cylinder_Operation_Started);
            }
            catch (Exception ex)
            {
                _loggingService?.LogError(ex, "水平调节气缸执行失败");
            }
        }
        #endregion  IO控制

        #region 视觉处理
        [RelayCommand]//视觉标定
        public async Task CalibrateXYR() => await SendMsg(1, 4, 1, 5, 0);

        //[RelayCommand]
        public async Task SendMsg(int P1, int P2, int P3, int P4, int P5)
        {
            await WritePLCVariable($"{AxisConstants.AXIS_GVL}.AMW1000", 0);
            await WritePLCVariable($"{AxisConstants.AXIS_GVL}.AMW1001", 0);
            await WritePLCVariable($"{AxisConstants.AXIS_GVL}.AMW1002", 0);
            await WritePLCVariable($"{AxisConstants.AXIS_GVL}.AMW1003", 0);
            await WritePLCVariable($"{AxisConstants.AXIS_GVL}.AMW1004", 0);
            await Task.Delay(1000);
            //标定开始            
            await WritePLCVariable($"{AxisConstants.AXIS_GVL}.AMW1001", P2);
            await WritePLCVariable($"{AxisConstants.AXIS_GVL}.AMW1002", P3);
            await WritePLCVariable($"{AxisConstants.AXIS_GVL}.AMW1003", P4);
            await WritePLCVariable($"{AxisConstants.AXIS_GVL}.AMW1004", P5);
            await WritePLCVariable($"{AxisConstants.AXIS_GVL}.AMW1000", P1);

        }
        public async Task<string> ReceiveMsg()
        {
            //标定开始
            PLCVarReadInfo ReadInfoAddress1020 = new() { Name = $"{AxisConstants.AXIS_GVL}.AMW1020", Type = typeof(Int32) };
            object retAddress1020 = await _plcVariableService.ReadVariableSafelyAsync<object>(ReadInfoAddress1020.Name);
            await Task.Delay(100);
            PLCVarReadInfo ReadInfoAddress1021 = new() { Name = $"{AxisConstants.AXIS_GVL}.AMW1021", Type = typeof(Int32) };
            object retAddress1021 = await _plcVariableService.ReadVariableSafelyAsync<object>(ReadInfoAddress1021.Name);
            await Task.Delay(100);
            PLCVarReadInfo ReadInfoAddress1022 = new() { Name = $"{AxisConstants.AXIS_GVL}.AMW1022", Type = typeof(Int32) };
            object retAddress1022 = await _plcVariableService.ReadVariableSafelyAsync<object>(ReadInfoAddress1022.Name);

            await WritePLCVariable($"{AxisConstants.AXIS_GVL}.AMW1020", 0);
            await Task.Delay(100);
            await WritePLCVariable($"{AxisConstants.AXIS_GVL}.AMW1021", 0);
            await Task.Delay(100);
            await WritePLCVariable($"{AxisConstants.AXIS_GVL}.AMW1022", 0);
            await Task.Delay(100);
            return Convert.ToString(retAddress1020) + "," + Convert.ToString(retAddress1021) + "," + Convert.ToString(retAddress1022);
        }

        public async Task SetXYRPos(float P1, float P2, float P3)
        {
            //标定开始
            await WritePLCVariable($"{AxisConstants.AXIS_GVL}.AMW1006", P1);
            await Task.Delay(100);
            await WritePLCVariable($"{AxisConstants.AXIS_GVL}.AMW1008", P2);
            await Task.Delay(100);
            await WritePLCVariable($"{AxisConstants.AXIS_GVL}.AMW1010", P3);
        }

        public async Task<string> GetXYRPos()
        {
            //标定开始
            PLCVarReadInfo ReadInfoAddress1026 = new() { Name = $"{AxisConstants.AXIS_GVL}.AMW1026", Type = typeof(float) };
            object retAddress1026 = await _plcVariableService.ReadVariableSafelyAsync<object>(ReadInfoAddress1026.Name);
            await Task.Delay(100);
            PLCVarReadInfo ReadInfoAddress1028 = new() { Name = $"{AxisConstants.AXIS_GVL}.AMW1028", Type = typeof(float) };
            object retAddress1028 = await _plcVariableService.ReadVariableSafelyAsync<object>(ReadInfoAddress1028.Name);
            await Task.Delay(100);
            PLCVarReadInfo ReadInfoAddress1030 = new() { Name = $"{AxisConstants.AXIS_GVL}.AMW1030", Type = typeof(float) };
            object retAddress1030 = await _plcVariableService.ReadVariableSafelyAsync<object>(ReadInfoAddress1030.Name);

            return Convert.ToString(retAddress1026) + "," + Convert.ToString(retAddress1028) + "," + Convert.ToString(retAddress1030);
        }

        [RelayCommand]//获取目标Mark位置
        public async Task TargetTakePhoto() => await SendMsg(1, 5, 5, 0, 0);
        [RelayCommand]//对象拍照
        public async Task ObjectTakePhoto() => await SendMsg(1, 6, 5, 0, 0);
        [RelayCommand]//一次对准
        public async Task AlignOnce() => await SendMsg(1, 11, 1, 11, 1);
        [RelayCommand]//多次对准
        public async Task AlignMore() => await SendMsg(1, 7, 1, 10, 1);
        [RelayCommand]//切换作业
        public async Task ChangeVsiualNum(int Num) => await SendMsg(1, 3, Num, 0, 0);
        #endregion  视觉处理

        #region  工艺操作
        public async Task<bool> RecipePareExcute()
        {
            try
            {
                // 优先使用已注入的服务
                if (_recipeService != null)
                {
                    // 使用注入的_recipeService服务
                    await WritePLCVariable($"{AxisConstants.AXIS_GVL}.SpacerThickness", 
                        _recipeService.SpacerThick * AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION);
                    await Task.Delay(30);
                    
                    await WritePLCVariable($"{AxisConstants.AXIS_GVL}.ZWaferThickness", 
                        _recipeService.TopWaferThick * AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION);
                    await Task.Delay(30);
                    
                    await WritePLCVariable($"{AxisConstants.AXIS_GVL}.ZWaferDownThickness", 
                        _recipeService.TopWaferThick * AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION);
                    await Task.Delay(30);
                    
                    await ChangeVsiualNum(_recipeService.VisualNumber);
                    return true;
                }
                
                // 如果注入的服务为null，尝试从服务容器获取
                var recipeService = CommonFun.host?.Services.GetService<IRecipeService>();
                if (recipeService != null)
                {
                    await WritePLCVariable($"{AxisConstants.AXIS_GVL}.SpacerThickness", 
                        recipeService.SpacerThick * AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION);
                    await Task.Delay(30);
                    
                    await WritePLCVariable($"{AxisConstants.AXIS_GVL}.ZWaferThickness", 
                        recipeService.TopWaferThick * AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION);
                    await Task.Delay(30);
                    
                    await WritePLCVariable($"{AxisConstants.AXIS_GVL}.ZWaferDownThickness", 
                        recipeService.TopWaferThick * AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION);
                    await Task.Delay(30);
                    
                    await ChangeVsiualNum(recipeService.VisualNumber);
                    return true;
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogWarning($"使用配方服务失败，回退到传统方式: {ex.Message}", EventIds.Recipe_Execute_Check);
            }
            
            // 回退到备用方式
            try {
                // 尝试从服务容器获取对准参数服务
                var alignerParaService = _alignerParaService ?? CommonFun.host?.Services.GetService<IAlignerParaService>();
                
                if (alignerParaService != null)
                {
                    var alignerPara = alignerParaService.Current;
                    
                    await WritePLCVariable($"{AxisConstants.AXIS_GVL}.SpacerThickness", 
                        alignerPara.SpacerThick * AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION);
                    await Task.Delay(30);
                    
                    await WritePLCVariable($"{AxisConstants.AXIS_GVL}.ZWaferThickness", 
                        alignerPara.TopWaferThick * AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION);
                    await Task.Delay(30);
                    
                    await WritePLCVariable($"{AxisConstants.AXIS_GVL}.ZWaferDownThickness", 
                        alignerPara.TopWaferThick * AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION);
                    await Task.Delay(30);
                    
                    await ChangeVsiualNum(alignerPara.VisualNumber);
                }
                else
                {
                    _loggingService?.LogError("无法获取对准参数服务，配方参数写入失败", EventIds.Service_Initialization_Failed);
                    return false;
                }
            }
            catch (Exception ex) {
                _loggingService?.LogError($"执行配方参数写入失败: {ex.Message}", EventIds.Recipe_Execute_Error);
                return false;
            }
            return true;
        }
        public async Task<bool> XYRMotion(double XTarget, double YTarget, double RTarget)
        {
            try
            {
                // 使用依赖注入的服务
                if (_axisEventService != null)
                {
                    // 使用轴事件服务执行XYR运动
                    await _axisEventService.MoveXYRAxesToPositionAsync(
                        (float)XTarget, 
                        (float)YTarget, 
                        (float)RTarget);
                    
                    // 获取轴运行状态
                    var (xState, yState, rState) = _axisEventService.GetXYRRunStates();
                    int i = 0;
                    
                    // 等待运动完成
                    while (xState != 0 && yState != 0 && rState != 0)
                    {
                        await Task.Delay(200);
                        i++;
                        if (i > 25) break;
                        
                        (xState, yState, rState) = _axisEventService.GetXYRRunStates();
                    }
                    
                    if (i >= 25)
                    {
                        _loggingService?.LogError("XYR轴运动至初始位超时", EventIds.Xyr_Initial_Location_Failed);
                        MessageBox.Show("XYR轴运动至初始位超时");
                        return false;
                    }
                    
                    return true;
                }
                else
                {
                    // 使用备用方式
                    _loggingService?.LogWarning("无法获取轴事件服务，尝试使用备用方式进行XYR运动", EventIds.Service_Initialization_Failed);
                    
                    try {
                        // 尝试从服务容器获取轴工厂
                        var axisFactory = CommonFun.host?.Services.GetService<IAxisViewModelFactory>();
                        if (axisFactory == null)
                        {
                            _loggingService?.LogError("无法获取轴工厂服务，XYR轴运动失败", EventIds.Service_Initialization_Failed);
                            return false;
                        }
                        
                        // 获取XYR轴控制器
                        var xAxis = axisFactory.GetXAxisViewModel();
                        var yAxis = axisFactory.GetYAxisViewModel();
                        var rAxis = axisFactory.GetRAxisViewModel();
                        
                        if (xAxis == null || yAxis == null || rAxis == null)
                        {
                            _loggingService?.LogError("无法获取XYR轴控制器，XYR轴运动失败", EventIds.Service_Initialization_Failed);
                            return false;
                        }
                        
                        // XYR运动至原点位置
                        await xAxis.SetPositionAsync(Convert.ToInt32(XTarget * AxisConstants.AXIS_XY_MULTIPLE_CONVERTION));
                        await xAxis.MoveToPositionAsync(Convert.ToInt32(XTarget * AxisConstants.AXIS_XY_MULTIPLE_CONVERTION));
                        
                        // Y轴
                        await yAxis.SetPositionAsync(Convert.ToInt32(YTarget * AxisConstants.AXIS_XY_MULTIPLE_CONVERTION));
                        await yAxis.MoveToPositionAsync(Convert.ToInt32(YTarget * AxisConstants.AXIS_XY_MULTIPLE_CONVERTION));
                        
                        // R轴
                        await rAxis.SetPositionAsync(Convert.ToInt32(RTarget * AxisConstants.AXIS_R_MULTIPLE_CONVERTION));
                        await rAxis.MoveToPositionAsync(Convert.ToInt32(RTarget * AxisConstants.AXIS_R_MULTIPLE_CONVERTION));
                        
                        // 监控运行状态
                        int RunStateX = await xAxis.GetRunStateAsync();
                        int RunStateY = await yAxis.GetRunStateAsync();
                        int RunStateR = await rAxis.GetRunStateAsync();
                        int i = 0;
                        while (RunStateX != 0 && RunStateY != 0 && RunStateR != 0)
                        {
                            await Task.Delay(200);
                            i++;
                            if (i > 25)
                            {
                                break;
                            }
                            RunStateX = await xAxis.GetRunStateAsync();
                            RunStateY = await yAxis.GetRunStateAsync();
                            RunStateR = await rAxis.GetRunStateAsync();
                        }
                        
                        if (i == 26)
                        {
                            MessageBox.Show("XYR轴运动至初始位超时");
                            _loggingService?.LogError($"XYR轴运动至初始位超时", EventIds.Xyr_Initial_Location_Failed);
                            return false;
                        }
                        return true;
                    }
                    catch (Exception ex)
                    {
                        _loggingService?.LogError(ex, "使用备用方式进行XYR运动失败", EventIds.Xyr_Initial_Location_Failed);
                        return false;
                    }
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError(ex, "XYR轴运动失败", EventIds.Xyr_Initial_Location_Failed);
                return false;
            }
        }
        public async Task<bool> TopWaferUp(bool IsTopHorizontalAdjust, bool IsTopHorizontalPhoto)
        {
            bool Res = false;
            Res = await WritePLCVariable($"{AxisConstants.AXIS_GVL}.ZLevelEnable", IsTopHorizontalAdjust);
            if (!Res) { return false; }
            await Task.Delay(200);
            Res = await WritePLCVariable($"{AxisConstants.AXIS_GVL}.ZlevelDebug", IsTopHorizontalPhoto);
            if (!Res) { return false; }
            await Task.Delay(200);

            Res = await CommonTask("ZTakePhotoFinished", (uint)TakePositionSelect.Up, (uint)WaferSeries.Normal);
            return true;
        }

        public async Task<bool> TopWaferTakeUp()
        {
            bool Res = false;
            Res = await CommonTask("TakeConfirm", (uint)TakePositionSelect.TakeUp, (uint)WaferSeries.Normal);
            if (!Res) { return false; }
            return true;
        }
        public async Task<bool> TakeXYRConfirmed()
        {
            //TakeConfirmed
            await WritePLCVariable($"{AxisConstants.AXIS_GVL}.TakeConfirmed", true);
            await Task.Delay(200);
            //await WritePLCVariable($"{AxisConstants.AXIS_GVL}.TakeConfirmed", true);
            return true;
        }


        public async Task<bool> BottomWaferDown(uint WaferSeries)
        {
            bool Res = false;
            Res = await CommonTask("TakeDownWaferFinished", (uint)TakePositionSelect.Down, WaferSeries);
            if (!Res) { return false; }
            //完成后的变量ZTakePhotoFinished:true
            return true;
        }

        public async Task<bool> BottomWaferTakeDown(uint WaferSeries)
        {
            bool Res = false;
            Res = await CommonTask("TakeDownConfirm", (uint)TakePositionSelect.TakeDown, WaferSeries);
            if (!Res) { return false; }
            //完成后的变量ZTakePhotoFinished:true
            return true;
        }

        public async Task<bool> TakeDownConfirmed()
        {
            //TakeConfirmed
            await WritePLCVariable($"{AxisConstants.AXIS_GVL}.TakeDownConfirmed", true);
            await Task.Delay(200);
            return true;
        }

        public async Task<bool> WaferInitialZ()
        {
            bool Res = false;
            Res = await CommonTask("TakeConfirm", (uint)TakePositionSelect.InitialZ, (uint)WaferSeries.Normal);
            if (!Res) { return false; }
            return true;
        }
        public async Task<bool> VariableConfirm(string FinshVariable)
        {
            try 
            {
                // 优先使用依赖注入的服务
                if (_plcVariableService != null)
                {
                    string variablePath = $"{AxisConstants.AXIS_GVL}.{FinshVariable}";
                    bool variableValue = false;
                    int i = 0;
                    
                    // 使用服务循环读取变量直到成功或超时
                    while (!variableValue && i <= 50)
                    {
                        variableValue = await _plcVariableService.ReadVariableSafelyAsync<bool>(variablePath);
                        if (variableValue) break;
                        
                        await Task.Delay(200);
                        i++;
                    }
                    
                    if (i > 50)
                    {
                        _loggingService?.LogError($"{variablePath}超时", EventIds.Plc_Variable_Read_Failed);
                        return false;
                    }
                    
                    return true;
                }
                else
                {
                    // 回退到备用方式
                    _loggingService?.LogWarning($"无法获取PLC变量服务，尝试使用备用方式读取变量: {FinshVariable}", EventIds.Service_Initialization_Failed);
                    
                    // 尝试从服务容器获取PLC连接管理器
                    var plcConnectionManager = CommonFun.host?.Services.GetService<IPlcConnectionManager>();
                    if (plcConnectionManager == null)
                    {
                        _loggingService?.LogError("无法获取PLC连接管理器，读取变量失败", EventIds.Service_Initialization_Failed);
                        return false;
                    }
                    
                    // 获取PLC实例
                    var plc = plcConnectionManager.GetPlcInstance("Main");
                    if (plc == null)
                    {
                        _loggingService?.LogError("无法获取PLC实例，读取变量失败", EventIds.Service_Initialization_Failed);
                        return false;
                    }
                    
                    // 使用PLC实例直接读取变量
                    PLCVarReadInfo ReadFinished = new() { Name = $"{AxisConstants.AXIS_GVL}.{FinshVariable}", Type = typeof(bool) };
                    object retReadFinished = await plc.ReadVariableAsync(ReadFinished, CancellationToken.None);
                    int i = 0;
                    while (!(bool)retReadFinished && i <= 50)
                    {
                        await Task.Delay(200);
                        i++;
                        retReadFinished = await plc.ReadVariableAsync(ReadFinished, CancellationToken.None);
                    }
                    
                    if (i > 50)
                    {
                        _loggingService?.LogError($"{AxisConstants.AXIS_GVL}.{FinshVariable}超时", EventIds.Plc_Variable_Read_Failed);
                        return false;
                    }
                    
                    return true;
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError(ex, $"读取变量{FinshVariable}失败", EventIds.Plc_Variable_Read_Failed);
                return false;
            }
        }
        private async Task<bool> CommonTask(string Finished, uint TakeWaferSelect, uint WaferSeriesSelect)
        {
            try
            {
                // 写入PLC变量
                await WritePLCVariable($"{AxisConstants.AXIS_GVL}.TakeWaferSelect", TakeWaferSelect);
                await Task.Delay(200);
                await WritePLCVariable($"{AxisConstants.AXIS_GVL}.WaferSeriesSelect", WaferSeriesSelect);
                await Task.Delay(200);
                await WritePLCVariable($"{AxisConstants.AXIS_GVL}.TakeExecute", true);
                await Task.Delay(200);
                
                // 优先使用依赖注入的服务
                if (_plcVariableService != null)
                {
                    string variablePath = $"{AxisConstants.AXIS_GVL}.{Finished}";
                    bool variableValue = false;
                    int i = 0;
                    
                    // 使用服务循环读取变量直到成功或超时
                    while (!variableValue && i <= 1000)
                    {
                        variableValue = await _plcVariableService.ReadVariableSafelyAsync<bool>(variablePath);
                        if (variableValue) break;
                        
                        await Task.Delay(200);
                        i++;
                    }
                    
                    if (i > 1000)
                    {
                        _loggingService?.LogError($"{variablePath}超时", EventIds.Plc_Variable_Read_Failed);
                        return false;
                    }
                    
                    return true;
                }
                else
                {
                    // 回退到备用方式
                    _loggingService?.LogWarning($"无法获取PLC变量服务，尝试使用备用方式读取变量: {Finished}", EventIds.Service_Initialization_Failed);
                    
                    // 尝试从服务容器获取PLC连接管理器
                    var plcConnectionManager = CommonFun.host?.Services.GetService<IPlcConnectionManager>();
                    if (plcConnectionManager == null)
                    {
                        _loggingService?.LogError("无法获取PLC连接管理器，读取变量失败", EventIds.Service_Initialization_Failed);
                        return false;
                    }
                    
                    // 获取PLC实例
                    var plc = plcConnectionManager.GetPlcInstance("Main");
                    if (plc == null)
                    {
                        _loggingService?.LogError("无法获取PLC实例，读取变量失败", EventIds.Service_Initialization_Failed);
                        return false;
                    }
                    
                    // 使用PLC实例直接读取变量
                    PLCVarReadInfo ReadFinished = new() { Name = $"{AxisConstants.AXIS_GVL}.{Finished}", Type = typeof(bool) };
                    object retReadFinished = await plc.ReadVariableAsync(ReadFinished, CancellationToken.None);
                    int i = 0;
                    while (!(bool)retReadFinished && i <= 1000)
                    {
                        await Task.Delay(200);
                        i++;
                        retReadFinished = await plc.ReadVariableAsync(ReadFinished, CancellationToken.None);
                    }
                    
                    if (i > 1000)
                    {
                        _loggingService?.LogError($"{AxisConstants.AXIS_GVL}.{Finished}超时", EventIds.Plc_Variable_Read_Failed);
                        return false;
                    }
                    
                    return true;
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError(ex, $"执行CommonTask操作失败: {Finished}", EventIds.Plc_Variable_Read_Failed);
                return false;
            }
        }

        public async void Test(string Finished)
        {
            try
            {
                // 优先使用依赖注入的服务
                if (_plcVariableService != null)
                {
                    string variablePath = $"{AxisConstants.AXIS_GVL}.{Finished}";
                    double value = await _plcVariableService.ReadVariableSafelyAsync<double>(variablePath);
                    _loggingService?.LogInformation($"测试读取变量 {variablePath} 值: {value}", EventIds.Plc_Variable_Read_Succeeded);
                }
                else
                {
                    // 回退到备用方式
                    _loggingService?.LogWarning($"无法获取PLC变量服务，尝试使用备用方式读取变量: {Finished}", EventIds.Service_Initialization_Failed);
                    
                    // 尝试从服务容器获取PLC连接管理器
                    var plcConnectionManager = CommonFun.host?.Services.GetService<IPlcConnectionManager>();
                    if (plcConnectionManager == null)
                    {
                        _loggingService?.LogError("无法获取PLC连接管理器，读取变量失败", EventIds.Service_Initialization_Failed);
                        return;
                    }
                    
                    // 获取PLC实例
                    var plc = plcConnectionManager.GetPlcInstance("Main");
                    if (plc == null)
                    {
                        _loggingService?.LogError("无法获取PLC实例，读取变量失败", EventIds.Service_Initialization_Failed);
                        return;
                    }
                    
                    // 使用PLC实例直接读取变量
                    PLCVarReadInfo ReadFinished = new() { Name = $"{AxisConstants.AXIS_GVL}.{Finished}", Type = typeof(double) };
                    object retReadFinished = await plc.ReadVariableAsync(ReadFinished, CancellationToken.None);
                    
                    if (retReadFinished != null)
                    {
                        _loggingService?.LogInformation($"测试读取变量 {ReadFinished.Name} 值: {retReadFinished}", EventIds.Plc_Variable_Read_Succeeded);
                    }
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError(ex, $"测试读取变量失败: {Finished}", EventIds.Plc_Variable_Read_Failed);
            }
        }
        #endregion

        #region 系统参数写       

        [RelayCommand]
        public async Task<bool> TopGapSystemPara(double value) => await WritePLCVariable($"{AxisConstants.AXIS_GVL}.TakeUpOffset", value * AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION);
        [RelayCommand]
        public async Task<bool> BottomGapSystemPara(double value) => await WritePLCVariable($"{AxisConstants.AXIS_GVL}.TakeDownOffset", value * AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION);
        [RelayCommand]
        public async Task<bool> BottomPhotoSystemPara(double value) =>
            await WritePLCVariable($"{AxisConstants.AXIS_GVL}.TakeSapcerOffset", value * AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION);
        [RelayCommand]
        public async Task<bool> CameraOffset(double value) => await WritePLCVariable($"{AxisConstants.AXIS_GVL}.ZWaferOffSet", value * AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION);
        public async Task<bool> SystemPareExcute(double cameraOffset, double bottomPhoto, double TopGap, double BottomGap)
        {
            bool res = false;
            res = await CameraOffset(cameraOffset);
            res = await BottomPhotoSystemPara(bottomPhoto);
            res = await TopGapSystemPara(TopGap);
            res = await BottomGapSystemPara(BottomGap);
            return res;
        }
        #endregion 系统参数写


        #endregion
    }
}
