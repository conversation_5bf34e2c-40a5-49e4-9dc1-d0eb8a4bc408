using Microsoft.Extensions.Logging;
using System.Collections.Generic;

namespace WaferAligner.Services.Core
{
    /// <summary>
    /// 日志配置类
    /// 支持灵活的日志配置管理，包括全局和模块级别的日志控制
    /// </summary>
    public class LoggingConfiguration
    {
        /// <summary>
        /// 全局最低日志级别
        /// </summary>
        public LogLevel GlobalMinimumLevel { get; set; } = LogLevel.Information;
        
        /// <summary>
        /// 按模块设置的日志级别
        /// </summary>
        public Dictionary<string, LogLevel> ModuleLevels { get; set; } = new Dictionary<string, LogLevel>();
        
        /// <summary>
        /// 是否启用结构化日志
        /// </summary>
        public bool EnableStructuredLogging { get; set; } = true;
        
        /// <summary>
        /// 是否启用性能日志
        /// </summary>
        public bool EnablePerformanceLogging { get; set; } = false;
        
        /// <summary>
        /// 日志文件路径
        /// </summary>
        public string LogFilePath { get; set; } = "logs/wafer-aligner.log";
        
        /// <summary>
        /// 日志文件最大大小（MB）
        /// </summary>
        public int MaxLogFileSizeMB { get; set; } = 100;
        
        /// <summary>
        /// 保留的日志文件数量
        /// </summary>
        public int RetainedLogFileCount { get; set; } = 10;
        
        /// <summary>
        /// 创建开发环境配置
        /// </summary>
        public static LoggingConfiguration CreateDevelopmentConfiguration()
        {
            return new LoggingConfiguration
            {
                GlobalMinimumLevel = LogLevel.Debug,
                EnableStructuredLogging = true,
                EnablePerformanceLogging = true,
                ModuleLevels = new Dictionary<string, LogLevel>
                {
                    { "PLC", LogLevel.Debug },
                    { "Axis", LogLevel.Debug },
                    { "UI", LogLevel.Information },
                    { "Cylinder", LogLevel.Debug },
                    { "User", LogLevel.Information },
                    { "Config", LogLevel.Debug },
                    { "Serial", LogLevel.Debug }
                }
            };
        }
        
        /// <summary>
        /// 创建生产环境配置
        /// </summary>
        public static LoggingConfiguration CreateProductionConfiguration()
        {
            return new LoggingConfiguration
            {
                GlobalMinimumLevel = LogLevel.Warning,
                EnableStructuredLogging = false,
                EnablePerformanceLogging = false,
                ModuleLevels = new Dictionary<string, LogLevel>
                {
                    { "PLC", LogLevel.Warning },
                    { "Axis", LogLevel.Warning },
                    { "UI", LogLevel.Error },
                    { "Cylinder", LogLevel.Warning },
                    { "User", LogLevel.Warning },
                    { "Config", LogLevel.Warning },
                    { "Serial", LogLevel.Warning }
                }
            };
        }
        
        /// <summary>
        /// 创建默认配置
        /// </summary>
        public static LoggingConfiguration CreateDefaultConfiguration()
        {
            return new LoggingConfiguration
            {
                GlobalMinimumLevel = LogLevel.Information,
                EnableStructuredLogging = true,
                EnablePerformanceLogging = false,
                ModuleLevels = new Dictionary<string, LogLevel>
                {
                    { "PLC", LogLevel.Information },
                    { "Axis", LogLevel.Information },
                    { "UI", LogLevel.Information },
                    { "Cylinder", LogLevel.Information },
                    { "User", LogLevel.Information },
                    { "Config", LogLevel.Information },
                    { "Serial", LogLevel.Information }
                }
            };
        }
        
        /// <summary>
        /// 验证配置的有效性
        /// </summary>
        /// <returns>配置是否有效</returns>
        public bool IsValid()
        {
            // 检查日志级别是否有效
            if (!System.Enum.IsDefined(typeof(LogLevel), GlobalMinimumLevel))
                return false;
                
            // 检查模块级别配置
            foreach (var kvp in ModuleLevels)
            {
                if (string.IsNullOrEmpty(kvp.Key) || !System.Enum.IsDefined(typeof(LogLevel), kvp.Value))
                    return false;
            }
            
            // 检查文件配置
            if (MaxLogFileSizeMB <= 0 || RetainedLogFileCount <= 0)
                return false;
                
            return true;
        }
        
        /// <summary>
        /// 克隆配置
        /// </summary>
        /// <returns>配置的副本</returns>
        public LoggingConfiguration Clone()
        {
            return new LoggingConfiguration
            {
                GlobalMinimumLevel = GlobalMinimumLevel,
                EnableStructuredLogging = EnableStructuredLogging,
                EnablePerformanceLogging = EnablePerformanceLogging,
                LogFilePath = LogFilePath,
                MaxLogFileSizeMB = MaxLogFileSizeMB,
                RetainedLogFileCount = RetainedLogFileCount,
                ModuleLevels = new Dictionary<string, LogLevel>(ModuleLevels)
            };
        }
    }
}
