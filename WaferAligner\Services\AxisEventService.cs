using System;
using System.Threading.Tasks;
using WaferAligner.Communication.Inovance.Abstractions;
using WaferAligner.Services;
using WaferAligner.Services.Abstractions;
using WaferAligner.Services.Extensions;
using WaferAligner.Interfaces;

namespace WaferAligner.Services
{
    public class AxisEventService : IAxisEventService
    {
        private readonly IAxisViewModelFactory _axisFactory;
        private readonly IMainWindowViewModel _mainWindowViewModel;
        private readonly ILoggingService _loggingService;
        private readonly IPlcConnectionManager _plcConnectionManager;
        private readonly ICylinderService _cylinderService;
        
        // 添加存储当前轴ID的字段
        private int _currentAxisId = -1;

        public AxisEventService(
            IAxisViewModelFactory axisFactory,
            IMainWindowViewModel mainWindowViewModel,
            ILoggingService loggingService,
            IPlcConnectionManager plcConnectionManager,
            ICylinderService cylinderService)
        {
            _axisFactory = axisFactory;
            _mainWindowViewModel = mainWindowViewModel;
            _loggingService = loggingService;
            _plcConnectionManager = plcConnectionManager;
            _cylinderService = cylinderService;
        }

        public async Task RegisterAxisEventAsync(string axisName, string variableName, Action<object> handler)
        {
            try
            {
                _loggingService.LogDebug($"开始注册轴事件: {axisName} - {variableName}", WaferAligner.EventIds.EventIds.Axis_Operation_Info);

                IAxisViewModel axisViewModel = axisName.ToUpper() switch
                {
                    "X" => await _axisFactory.GetXAxisViewModelAsync(),
                    "Y" => await _axisFactory.GetYAxisViewModelAsync(),
                    "R" => await _axisFactory.GetRAxisViewModelAsync(),
                    "Z" => await _axisFactory.GetZAxisViewModelAsync(),
                    "LX" => await _axisFactory.GetLXAxisViewModelAsync(),
                    "LY" => await _axisFactory.GetLYAxisViewModelAsync(),
                    "LZ" => await _axisFactory.GetLZAxisViewModelAsync(),
                    "RX" => await _axisFactory.GetRXAxisViewModelAsync(),
                    "RY" => await _axisFactory.GetRYAxisViewModelAsync(),
                    "RZ" => await _axisFactory.GetRZAxisViewModelAsync(),
                    _ => throw new ArgumentException($"未知轴名称: {axisName}")
                };

                if (axisViewModel != null)
                {
                    axisViewModel.RegisterAction(variableName, handler);
                    _loggingService.LogDebug($"成功注册轴事件: {axisName} - {variableName}", WaferAligner.EventIds.EventIds.Events_Registration_Success);
                }
                else
                {
                    _loggingService.LogWarning($"轴ViewModel为null，无法注册事件: {axisName} - {variableName}", WaferAligner.EventIds.EventIds.Axis_Event_Registration_Failed);
                }
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"注册轴事件失败: {axisName} {variableName}");
            }
        }

        public void RegisterMainWindowEvent(string variableName, Action<object> handler)
        {
            try
            {
                _mainWindowViewModel?.RegistryAction(variableName, handler);
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"注册主窗口事件失败: {variableName}");
            }
        }

        public async Task<double> GetAxisPositionAsync(string axisName)
        {
            try
            {
                IAxisViewModel axisViewModel = axisName.ToUpper() switch
                {
                    "X" => await _axisFactory.CreateXAxisAsync(),
                    "Y" => await _axisFactory.CreateYAxisAsync(),
                    "R" => await _axisFactory.CreateRAxisAsync(),
                    "Z" => await _axisFactory.CreateZAxisAsync(),
                    _ => throw new ArgumentException($"未知轴名称: {axisName}")
                };
                return axisViewModel != null ? await axisViewModel.GetCurrentPositionAsync() : 0.0;
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"获取轴位置失败: {axisName}");
                return 0.0;
            }
        }

        public async Task<(double X, double Y, double R)> GetXYRPositionsAsync()
        {
            try
            {
                var x = await GetAxisPositionAsync("X");
                var y = await GetAxisPositionAsync("Y");
                var r = await GetAxisPositionAsync("R");
                return (x, y, r);
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, "获取XYR轴位置失败");
                return (0.0, 0.0, 0.0);
            }
        }

        public async Task MoveCameraAxesToPositionAsync(double lx, double ly, double lz, double rx, double ry, double rz)
        {
            try
            {
                var leftX = _axisFactory.GetLXAxisViewModel();
                var leftY = _axisFactory.GetLYAxisViewModel();
                var leftZ = _axisFactory.GetLZAxisViewModel();
                var rightX = _axisFactory.GetRXAxisViewModel();
                var rightY = _axisFactory.GetRYAxisViewModel();
                var rightZ = _axisFactory.GetRZAxisViewModel();

                double lxTarget = lx * AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION;
                double lyTarget = ly * AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION;
                double rxTarget = rx * AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION;
                double ryTarget = ry * AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION;

                await leftX.MoveToPositionAsync(lxTarget);
                await leftY.MoveToPositionAsync(lyTarget);
                await leftZ.MoveToPositionAsync(lz);

                await rightX.MoveToPositionAsync(rxTarget);
                await rightY.MoveToPositionAsync(ryTarget);
                await rightZ.MoveToPositionAsync(rz);
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, "相机轴批量定位失败");
                throw;
            }
        }

        public async Task MoveXYRAxesToPositionAsync(float x, float y, float r)
        {
            try
            {
                var axisX = _axisFactory.GetXAxisViewModel();
                var axisY = _axisFactory.GetYAxisViewModel();
                var axisR = _axisFactory.GetRAxisViewModel();

                double xTarget = x * AxisConstants.AXIS_XY_MULTIPLE_CONVERTION;
                double yTarget = y * AxisConstants.AXIS_XY_MULTIPLE_CONVERTION;
                double rTarget = r * AxisConstants.AXIS_R_MULTIPLE_CONVERTION;

                await axisX.MoveToPositionAsync(xTarget);
                await axisY.MoveToPositionAsync(yTarget);
                await axisR.MoveToPositionAsync(rTarget);
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"XYR轴批量定位失败: X={x}, Y={y}, R={r}");
                throw;
            }
        }

        public async Task MoveToPositionAsync(string axisName, double position)
        {
            try
            {
                IAxisViewModel axisViewModel = axisName.ToUpper() switch
                {
                    "X" => _axisFactory.GetXAxisViewModel(),
                    "Y" => _axisFactory.GetYAxisViewModel(),
                    "R" => _axisFactory.GetRAxisViewModel(),
                    "Z" => _axisFactory.GetZAxisViewModel(),
                    "LX" => _axisFactory.GetLXAxisViewModel(),
                    "LY" => _axisFactory.GetLYAxisViewModel(),
                    "LZ" => _axisFactory.GetLZAxisViewModel(),
                    "RX" => _axisFactory.GetRXAxisViewModel(),
                    "RY" => _axisFactory.GetRYAxisViewModel(),
                    "RZ" => _axisFactory.GetRZAxisViewModel(),
                    _ => throw new ArgumentException($"未知轴名称: {axisName}")
                };
                await axisViewModel.MoveToPositionAsync(position);
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"轴{axisName}定位失败: {position}");
                throw;
            }
        }

        public async Task SetXYRPositionAsync(float x, float y, float r)
        {
            try
            {
                var axisX = _axisFactory.GetXAxisViewModel();
                var axisY = _axisFactory.GetYAxisViewModel();
                var axisR = _axisFactory.GetRAxisViewModel();

                double xTarget = x * AxisConstants.AXIS_XY_MULTIPLE_CONVERTION;
                double yTarget = y * AxisConstants.AXIS_XY_MULTIPLE_CONVERTION;
                double rTarget = r * AxisConstants.AXIS_R_MULTIPLE_CONVERTION;

                await axisX.MoveToPositionAsync(xTarget);
                await axisY.MoveToPositionAsync(yTarget);
                await axisR.MoveToPositionAsync(rTarget);

                _loggingService.LogDebug($"XYR轴批量定位完成: X={x}, Y={y}, R={r}", WaferAligner.EventIds.EventIds.Axis_Move_Completed);
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"XYR轴批量定位失败: X={x}, Y={y}, R={r}", WaferAligner.EventIds.EventIds.Axis_Move_Error);
                throw;
            }
        }

        public bool CheckCameraAxesArrived()
        {
            try
            {
                var leftX = _axisFactory.GetLXAxisViewModel();
                var leftY = _axisFactory.GetLYAxisViewModel();
                var leftZ = _axisFactory.GetLZAxisViewModel();
                var rightX = _axisFactory.GetRXAxisViewModel();
                var rightY = _axisFactory.GetRYAxisViewModel();
                var rightZ = _axisFactory.GetRZAxisViewModel();

                return (leftX?.IsReady ?? false) &&
                       (leftY?.IsReady ?? false) &&
                       (leftZ?.IsReady ?? false) &&
                       (rightX?.IsReady ?? false) &&
                       (rightY?.IsReady ?? false) &&
                       (rightZ?.IsReady ?? false);
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, "检查相机轴到位状态失败");
                return false;
            }
        }

        public async Task SendMsgAsync(int p1, int p2, int p3, int p4, int p5)
        {
            try
            {
                await _mainWindowViewModel.SendMsg(p1, p2, p3, p4, p5);
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, "发送消息失败");
                throw;
            }
        }

        public async Task<string> ReceiveMsgAsync()
        {
            try
            {
                return await _mainWindowViewModel.ReceiveMsg();
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, "接收消息失败");
                throw;
            }
        }

        public async Task<string> GetXYRPosAsync()
        {
            try
            {
                return await _mainWindowViewModel.GetXYRPos();
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, "获取XYR轴位置失败");
                throw;
            }
        }

        public (int XState, int YState, int RState) GetXYRRunStates()
        {
            try
            {
                var axisX = _axisFactory.GetXAxisViewModel();
                var axisY = _axisFactory.GetYAxisViewModel();
                var axisR = _axisFactory.GetRAxisViewModel();

                int retX = (axisX?.IsReady ?? false) ? 0 : 1;
                int retY = (axisY?.IsReady ?? false) ? 0 : 1;
                int retR = (axisR?.IsReady ?? false) ? 0 : 1;

                return (retX, retY, retR);
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, "获取XYR轴运动状态失败");
                return (0, 0, 0);
            }
        }

        public async Task StopAllAxesAsync()
        {
            try
            {
                _loggingService.LogDebug("停止所有轴运动", WaferAligner.EventIds.EventIds.Axis_Move_Completed);

                // 停止主轴
                var zAxis = _axisFactory.GetZAxisViewModel();
                await zAxis.StopAsync();

                // 停止相机轴
                var leftX = _axisFactory.GetLXAxisViewModel();
                var leftY = _axisFactory.GetLYAxisViewModel();
                var leftZ = _axisFactory.GetLZAxisViewModel();
                var rightX = _axisFactory.GetRXAxisViewModel();
                var rightY = _axisFactory.GetRYAxisViewModel();
                var rightZ = _axisFactory.GetRZAxisViewModel();

                await leftZ.StopAsync();
                await rightZ.StopAsync();
                await leftX.StopAsync();
                await rightX.StopAsync();
                await leftY.StopAsync();
                await rightY.StopAsync();

                _loggingService.LogDebug("所有轴停止命令发送完成", WaferAligner.EventIds.EventIds.Axis_Move_Completed);
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, "停止轴运动失败", WaferAligner.EventIds.EventIds.Axis_Move_Error);
                throw;
            }
        }

        public async Task SetZAxisHomeOffsetAsync(double offset)
        {
            try
            {
                _loggingService.LogDebug($"设置Z轴归零偏移: {offset}", WaferAligner.EventIds.EventIds.Axis_Move_Completed);
                var zAxis = _axisFactory.GetZAxisViewModel();
                await zAxis.SetHomeOffsetAsync(offset);
                _loggingService.LogDebug("Z轴归零偏移设置完成", WaferAligner.EventIds.EventIds.Axis_Move_Completed);
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, "设置Z轴归零偏移失败", WaferAligner.EventIds.EventIds.Axis_Move_Error);
                throw;
            }
        }

        public async Task SetTopGapParameterAsync(double value)
        {
            try
            {
                _loggingService.LogDebug($"设置上间隙参数: {value}", WaferAligner.EventIds.EventIds.Configuration_Loaded);
                if (_mainWindowViewModel != null)
                {
                    await _mainWindowViewModel.TopGapSystemParaAsync(value);
                }
                _loggingService.LogDebug("上间隙参数设置完成", WaferAligner.EventIds.EventIds.Configuration_Loaded);
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, "设置上间隙参数失败", WaferAligner.EventIds.EventIds.Configuration_Error);
                throw;
            }
        }

        public async Task SetCameraOffsetParameterAsync(double value)
        {
            try
            {
                _loggingService.LogDebug($"设置相机偏移参数: {value}", WaferAligner.EventIds.EventIds.Configuration_Loaded);
                if (_mainWindowViewModel != null)
                {
                    await _mainWindowViewModel.CameraOffsetAsync(value);
                }
                _loggingService.LogDebug("相机偏移参数设置完成", WaferAligner.EventIds.EventIds.Configuration_Loaded);
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, "设置相机偏移参数失败", WaferAligner.EventIds.EventIds.Configuration_Error);
                throw;
            }
        }

        public async Task SetBottomPhotoParameterAsync(double value)
        {
            try
            {
                _loggingService.LogDebug($"设置下拍照参数: {value}", WaferAligner.EventIds.EventIds.Configuration_Loaded);
                if (_mainWindowViewModel != null)
                {
                    await _mainWindowViewModel.BottomPhotoSystemParaAsync(value);
                }
                _loggingService.LogDebug("下拍照参数设置完成", WaferAligner.EventIds.EventIds.Configuration_Loaded);
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, "设置下拍照参数失败", WaferAligner.EventIds.EventIds.Configuration_Error);
                throw;
            }
        }

        public async Task SetBottomGapParameterAsync(double value)
        {
            try
            {
                _loggingService.LogDebug($"设置下间隙参数: {value}", WaferAligner.EventIds.EventIds.Configuration_Loaded);
                if (_mainWindowViewModel != null)
                {
                    await _mainWindowViewModel.BottomGapSystemParaAsync(value);
                }
                _loggingService.LogDebug("下间隙参数设置完成", WaferAligner.EventIds.EventIds.Configuration_Loaded);
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, "设置下间隙参数失败", WaferAligner.EventIds.EventIds.Configuration_Error);
                throw;
            }
        }

        public async Task SetAxisSpeedsAsync(
            uint xRunSpeed, uint xJogSpeed,
            uint yRunSpeed, uint yJogSpeed,
            uint rRunSpeed, uint rJogSpeed,
            uint zRunSpeed, uint zJogSpeed)
        {
            try
            {
                _loggingService.LogDebug("设置轴运动速度", WaferAligner.EventIds.EventIds.Axis_Move_Completed);

                var axisX = _axisFactory.GetXAxisViewModel();
                var axisY = _axisFactory.GetYAxisViewModel();
                var axisR = _axisFactory.GetRAxisViewModel();
                var axisZ = _axisFactory.GetZAxisViewModel();

                // 设置XYR轴速度（使用异步方法）
                await axisX?.SetRunSpeedAsync(xRunSpeed);
                await axisX?.SetJogSpeedAsync(xJogSpeed);

                await axisY?.SetRunSpeedAsync(yRunSpeed);
                await axisY?.SetJogSpeedAsync(yJogSpeed);

                await axisR?.SetRunSpeedAsync(rRunSpeed);
                await axisR?.SetJogSpeedAsync(rJogSpeed);

                // 设置Z轴速度
                await axisZ?.SetRunSpeedAsync(zRunSpeed);
                await axisZ?.SetJogSpeedAsync(zJogSpeed);

                _loggingService.LogDebug("轴运动速度设置完成", WaferAligner.EventIds.EventIds.Axis_Move_Completed);
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, "设置轴运动速度失败", WaferAligner.EventIds.EventIds.Axis_Move_Error);
                throw;
            }
        }

        public object GetMainPLCInstance()
        {
            try
            {
                return _plcConnectionManager.GetPlcInstance("Main");
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, "获取主PLC实例失败", WaferAligner.EventIds.EventIds.Plc_Connection_Failed);
                return null;
            }
        }

        public int GetCylinderState(string cylinderType)
        {
            // 直接调用兼容性服务或主窗体ViewModel的相关方法
            // 这里假设兼容性服务有对应实现
            return _cylinderService.GetCylinderState(cylinderType);
        }

        public async Task<ICameraAxisViewModel> GetCameraAxisViewModelAsync(string position, string axis)
        {
            // 根据位置和轴名称动态获取相机轴视图模型
            position = position.ToLower();
            axis = axis.ToUpper();
            if (position == "left")
            {
                return axis switch
                {
                    "X" => await _axisFactory.GetLXAxisViewModelAsync(),
                    "Y" => await _axisFactory.GetLYAxisViewModelAsync(),
                    "Z" => await _axisFactory.GetLZAxisViewModelAsync(),
                    _ => throw new ArgumentException($"未知左相机轴: {axis}")
                };
            }
            else if (position == "right")
            {
                return axis switch
                {
                    "X" => await _axisFactory.GetRXAxisViewModelAsync(),
                    "Y" => await _axisFactory.GetRYAxisViewModelAsync(),
                    "Z" => await _axisFactory.GetRZAxisViewModelAsync(),
                    _ => throw new ArgumentException($"未知右相机轴: {axis}")
                };
            }
            else
            {
                throw new ArgumentException($"未知相机位置: {position}");
            }
        }

        public async Task CylinderControlAsync(string cylinderType, int targetState)
        {
            try
            {
                _loggingService.LogDebug($"控制气缸: {cylinderType}, 目标状态: {targetState}", WaferAligner.EventIds.EventIds.Axis_Move_Completed);
                await _cylinderService.ControlCylinderAsync(cylinderType, targetState);
                _loggingService.LogDebug($"气缸控制完成: {cylinderType}, 状态: {targetState}", WaferAligner.EventIds.EventIds.Axis_Move_Completed);
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"气缸控制失败: {cylinderType}, 状态: {targetState}", WaferAligner.EventIds.EventIds.Axis_Move_Error);
                throw;
            }
        }

        public async Task CalibrateXYRAsync()
        {
            try
            {
                _loggingService.LogDebug("开始XYR标定", WaferAligner.EventIds.EventIds.Axis_Move_Completed);
                if (_mainWindowViewModel != null)
                {
                    await _mainWindowViewModel.CalibrateXYR();
                    _loggingService.LogDebug("XYR标定完成", WaferAligner.EventIds.EventIds.Axis_Move_Completed);
                }
                else
                {
                    _loggingService.LogWarning("MainWindowViewModel未初始化，无法执行XYR标定", WaferAligner.EventIds.EventIds.Axis_Move_Error);
                }
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, "XYR标定失败", WaferAligner.EventIds.EventIds.Axis_Move_Error);
                throw;
            }
        }
        
        /// <summary>
        /// 设置当前选择的轴ID（替代ConstValue.AXISPOSNUM）
        /// </summary>
        /// <param name="axisId">轴ID</param>
        public void SetCurrentAxisId(int axisId)
        {
            _currentAxisId = axisId;
            _loggingService.LogDebug($"当前选择轴ID已更新为: {axisId}", WaferAligner.EventIds.EventIds.Axis_Operation_Info);
        }
        
        /// <summary>
        /// 获取当前选择的轴ID（替代ConstValue.AXISPOSNUM）
        /// </summary>
        /// <returns>当前轴ID</returns>
        public int GetCurrentAxisId()
        {
            return _currentAxisId;
        }
        
        public async Task UnregisterAxisEventAsync(string axisName, string variableName)
        {
            try
            {
                // 使用辅助方法获取轴视图模型，该方法处理同步/异步混合模式
                IAxisViewModel axisViewModel = await GetAxisViewModelAsync(axisName);
                
                if (axisViewModel != null)
                {
                    // 检查IAxisViewModel接口是否定义了UnregisterAction方法
                    var unregisterMethod = axisViewModel.GetType().GetMethod("UnregisterAction");
                    if (unregisterMethod != null)
                    {
                        // 使用反射调用，但增加错误处理和日志
                        try
                        {
                            unregisterMethod.Invoke(axisViewModel, new object[] { variableName });
                            _loggingService.LogDebug($"已注销轴事件: {axisName} {variableName}", WaferAligner.EventIds.EventIds.Axis_Event_Unregistered);
                        }
                        catch (Exception ex)
                        {
                            _loggingService.LogWarning(ex, $"反射调用UnregisterAction失败: {axisName} {variableName}", WaferAligner.EventIds.EventIds.Axis_Event_Unregister_Failed);
                            
                            // 尝试使用替代方法：用null处理程序替换现有处理程序
                            try
                            {
                                axisViewModel.RegisterAction(variableName, null);
                                _loggingService.LogDebug($"已用null处理程序替换轴事件: {axisName} {variableName}", WaferAligner.EventIds.EventIds.Axis_Event_Unregistered);
                            }
                            catch (Exception innerEx)
                            {
                                _loggingService.LogError(innerEx, $"替代方法注销轴事件失败: {axisName} {variableName}", WaferAligner.EventIds.EventIds.Axis_Event_Unregister_Failed);
                            }
                        }
                    }
                    else
                    {
                        // 如果没有UnregisterAction方法，尝试用null处理程序替换现有处理程序
                        try
                        {
                            axisViewModel.RegisterAction(variableName, null);
                            _loggingService.LogDebug($"已用null处理程序替换轴事件: {axisName} {variableName}", WaferAligner.EventIds.EventIds.Axis_Event_Unregistered);
                        }
                        catch (Exception ex)
                        {
                            _loggingService.LogError(ex, $"替代方法注销轴事件失败: {axisName} {variableName}", WaferAligner.EventIds.EventIds.Axis_Event_Unregister_Failed);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"注销轴事件失败: {axisName} {variableName}", WaferAligner.EventIds.EventIds.Axis_Event_Unregister_Failed);
            }
        }
        
        // 辅助方法获取轴视图模型，处理同步/异步混合模式
        private async Task<IAxisViewModel> GetAxisViewModelAsync(string axisName)
        {
            try
            {
                // 注意：这里混合使用了异步方法和同步方法
                // 这是设计上的有意选择，基于不同轴的通信特性
                return axisName.ToUpper() switch
                {
                    // 主轴（XYR轴）：使用异步方法，通过串口通信
                    // 使用GetXAxisViewModelAsync而非CreateXAxisAsync以利用单例模式缓存
                    "X" => await _axisFactory.GetXAxisViewModelAsync(), 
                    "Y" => await _axisFactory.GetYAxisViewModelAsync(),
                    "R" => await _axisFactory.GetRAxisViewModelAsync(),
                    "Z" => await _axisFactory.GetZAxisViewModelAsync(),
                    
                    // 辅助轴（LX、LY等）：使用同步方法，通过PLC通信
                    "LX" => _axisFactory.GetLXAxisViewModel(),
                    "LY" => _axisFactory.GetLYAxisViewModel(),
                    "LZ" => _axisFactory.GetLZAxisViewModel(),
                    "RX" => _axisFactory.GetRXAxisViewModel(),
                    "RY" => _axisFactory.GetRYAxisViewModel(),
                    "RZ" => _axisFactory.GetRZAxisViewModel(),
                    _ => throw new ArgumentException($"未知轴名称: {axisName}")
                };
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"获取轴视图模型失败: {axisName}", WaferAligner.EventIds.EventIds.Axis_Operation_Error);
                return null;
            }
        }
    }
} 