using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using WaferAligner.Communication.Inovance.Abstractions;

namespace WaferAligner.Communication.Inovance.Abstractions
{
    /// <summary>
    /// PLC连接管理器接口
    /// </summary>
    public interface IPlcConnectionManager
    {
        // 连接管理
        Task<bool> ConnectAsync(string connectionName, string address, int port);
        Task<bool> DisconnectAsync(string connectionName);
        Task DisconnectAllAsync();
        bool IsConnected(string connectionName);
        
        // PLC实例管理
        IPlcInstance GetPlcInstance(string connectionName);
        IEnumerable<string> GetAllConnectionNames();
        
        // 批量操作
        Task<bool> ConnectAllAsync();
        Task<Dictionary<string, bool>> GetAllConnectionStatesAsync();
        
        // 事件
        event EventHandler<PlcConnectionEventArgs> ConnectionStateChanged;
        
        // 资源管理
        Task InitializeAsync();
        Task CleanupAsync();
    }
    
    /// <summary>
    /// PLC连接事件参数
    /// </summary>
    public class PlcConnectionEventArgs : EventArgs
    {
        public string ConnectionName { get; set; }
        public bool IsConnected { get; set; }
        public string Address { get; set; }
        public int Port { get; set; }
        public Exception Exception { get; set; }
    }
} 