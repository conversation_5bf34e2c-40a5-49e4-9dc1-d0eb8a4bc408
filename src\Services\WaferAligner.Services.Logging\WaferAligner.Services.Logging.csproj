<Project Sdk="Microsoft.NET.Sdk">
	<PropertyGroup>
		<TargetFrameworks>net6.0-windows</TargetFrameworks>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
		<PackageReference Include="System.Reactive" Version="5.0.0" />
		<PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.6" />
		<PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="9.0.6" />
		<PackageReference Include="System.Diagnostics.DiagnosticSource" Version="9.0.6" />
	</ItemGroup>
	
	<ItemGroup>
		<ProjectReference Include="..\..\Core\WaferAligner.EventIds\WaferAligner.EventIds.csproj" />
	</ItemGroup>
	
	<ItemGroup>
		<Compile Update="Properties\Resources.Designer.cs">
			<DesignTime>True</DesignTime>
			<AutoGen>True</AutoGen>
			<DependentUpon>Resources.resx</DependentUpon>
		</Compile>
	</ItemGroup>
	
	<ItemGroup>
		<EmbeddedResource Update="Properties\Resources.resx">
			<Generator>ResXFileCodeGenerator</Generator>
			<LastGenOutput>Resources.Designer.cs</LastGenOutput>
		</EmbeddedResource>
	</ItemGroup>
	
	<ItemGroup>
		<None Update="system_configuration.json">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
	</ItemGroup>
</Project>
