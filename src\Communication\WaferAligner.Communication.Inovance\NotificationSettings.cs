using System;
using System.Collections.Generic;

namespace WaferAligner.Communication.Inovance
{
    public class NotificationSettings
    {
        #region 字段
        private static NotificationSettings s_immediately = new(TransMode.OnChange, 0, 0);
        private TransMode _mode;
        private int _cycleTime;
        private int _maxDelay;
        //
        // Summary:
        //     Gets the default Settings (AdsTransMode.OnChange, CycleTime 200 ms, MaxDelay:
        //     off)
        //
        // Value:
        //     The default.
        public static NotificationSettings Default { get; } = new(TransMode.Cyclic, 200, 0);
        //
        // Summary:
        //     Gets the settings for a 'Immediate on change' notification.
        //
        // Value:
        //     The immediately on change.
        //
        // Remarks:
        //     AdsTransMode.OnChange, CycleTime: 0 ms, MaxDelay: off)
        public static NotificationSettings ImmediatelyOnChange => s_immediately;
        //
        // Summary:
        //     Gets the ADS Transmission mode.
        //
        // Value:
        //     The Transmission mode.
        public TransMode NotificationMode => _mode;
        //
        // Summary:
        //     Gets the cycle time (in milliseconds) for AdsNotifications.
        //
        // Value:
        //     The cycle time.
        //
        // Remarks:
        //     The ADS server checks if the value changes in this time slice. The unit is 1ms
        public int CycleTime => _cycleTime;

        //
        // Summary:
        //     Gets the Maximum Delay Time (in milliseconds) for AdsNotifications.
        //
        // Value:
        //     The maximum Delay time for ADS Notifications.
        public int MaxDelay => _maxDelay;
        #endregion
        //
        // Summary:
        //     Initializes a new instance of the TwinCAT.Ads.NotificationSettings class.
        //
        // Parameters:
        //   mode:
        //     The ADS Transmission mode.
        //
        //   cycleTime:
        //     The cycle time in ms.
        //
        //   maxDelay:
        //     The maximum delay in ms
        public NotificationSettings(TransMode mode, int cycleTime, int maxDelay)
        {
            _mode = mode;
            _cycleTime = cycleTime;
            _maxDelay = maxDelay;
        }
        //
        // Summary:
        //     Initializes a new instance of the TwinCAT.Ads.NotificationSettings class.
        //
        // Parameters:
        //   mode:
        //     The ADS Transmission mode.
        //
        //   cycleTime:
        //     The cycle time in ms.
        //
        //   maxDelay:
        //     The maximum delay in ms
        public NotificationSettings(TransMode mode, TimeSpan cycleTime, TimeSpan maxDelay)
        {
            _mode = mode;
            _cycleTime = (int)cycleTime.TotalMilliseconds;
            _maxDelay = (int)maxDelay.TotalMilliseconds;
        }

        public int CompareTo(NotificationSettings? other)
        {
            IComparer<NotificationSettings> comparer = new NotificationSettingsPriorityComparer();
            return comparer.Compare(this, (NotificationSettings)other!);
        }

        //
        // Summary:
        //     Determines whether the specified System.Object is equal to this instance.
        //
        // Parameters:
        //   obj:
        //     The object to compare with the current object.
        //
        // Returns:
        //     true if the specified System.Object is equal to this instance; otherwise, false.
        public override bool Equals(object? obj)
        {
            if (obj == null)
            {
                return false;
            }

            if (GetType() != obj!.GetType())
            {
                return false;
            }

            NotificationSettings notificationSettings = (NotificationSettings)obj;
            if (NotificationMode != notificationSettings.NotificationMode)
            {
                return false;
            }

            if (CycleTime != notificationSettings.CycleTime)
            {
                return false;
            }

            if (MaxDelay != notificationSettings.MaxDelay)
            {
                return false;
            }

            return true;
        }

        //
        // Summary:
        //     Implements the == operator.
        //
        // Parameters:
        //   settings1:
        //     The settings1.
        //
        //   settings2:
        //     The settings2.
        //
        // Returns:
        //     The result of the operator.
        public static bool operator ==(NotificationSettings? settings1, NotificationSettings? settings2)
        {
            if (object.Equals(settings1, null))
            {
                if (object.Equals(settings2, null))
                {
                    return true;
                }

                return false;
            }

            return settings1!.Equals(settings2);
        }

        //
        // Summary:
        //     Implements the != operator.
        //
        // Parameters:
        //   settings1:
        //     The settings1.
        //
        //   settings2:
        //     The settings2.
        //
        // Returns:
        //     The result of the operator.
        public static bool operator !=(NotificationSettings? settings1, NotificationSettings? settings2)
        {
            return !(settings1 == settings2);
        }

        //
        // Summary:
        //     Gets the HashCode of the Address
        //
        // Returns:
        //     A hash code for this instance, suitable for use in hashing algorithms and data
        //     structures like a hash table.
        public override int GetHashCode()
        {
            int num = 10;
            num = (int)(91 * num + NotificationMode);
            num = 91 * num + CycleTime;
            return 91 * num + MaxDelay;
        }
    }

}
