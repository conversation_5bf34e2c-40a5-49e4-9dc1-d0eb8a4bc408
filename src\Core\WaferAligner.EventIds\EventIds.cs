﻿using Microsoft.Extensions.Logging;

namespace WaferAligner.EventIds
{
    /// <summary>
    /// 统一管理系统事件ID的静态类 - 重构版本
    /// 采用分段式管理，每个模块分配1000个ID空间
    /// 命名规范：[Module]_[Action]_[Result]
    /// </summary>
    public static class EventIds
    {
        #region 分段常量定义

        // 系统级事件 (0-999)
        public const int SYSTEM_BASE = 0;

        // PLC通信 (1000-1999)
        public const int PLC_BASE = 1000;

        // 轴控制 (2000-2999)
        public const int AXIS_BASE = 2000;

        // UI操作 (3000-3999)
        public const int UI_BASE = 3000;

        // 气缸控制 (4000-4999)
        public const int CYLINDER_BASE = 4000;

        // 用户管理 (5000-5999)
        public const int USER_BASE = 5000;

        // 配置管理 (6000-6999)
        public const int CONFIG_BASE = 6000;

        // 串口通信 (7000-7999)
        public const int SERIAL_BASE = 7000;

        // 错误和异常 (8000-8999)
        public const int ERROR_BASE = 8000;

        // 性能监控 (9000-9999)
        public const int PERFORMANCE_BASE = 9000;

        #endregion

        #region 系统级事件 (0-999)

        public static readonly EventId Application_Started = new(1, "Application_Started");
        public static readonly EventId Application_Stopped = new(2, "Application_Stopped");
        public static readonly EventId Application_Error = new(3, "Application_Error");
        public static readonly EventId Configuration_Loaded = new(10, "Configuration_Loaded");
        public static readonly EventId Configuration_Saved = new(11, "Configuration_Saved");
        public static readonly EventId Configuration_Error = new(12, "Configuration_Error");
        public static readonly EventId Configuration_Changed = new(13, "Configuration_Changed");
        public static readonly EventId Service_Initialized = new(20, "Service_Initialized");
        public static readonly EventId Service_Disposed = new(21, "Service_Disposed");
        public static readonly EventId Service_Initialization_Failed = new(22, "Service_Initialization_Failed");
        public static readonly EventId Service_Using_Compatibility = new(23, "Service_Using_Compatibility");
        public static readonly EventId Service_Unavailable = new(24, "Service_Unavailable");
        public static readonly EventId Service_Resolved = new(25, "Service_Resolved");

        #endregion

        #region PLC通信事件 (1000-1999)

        public static readonly EventId Plc_Connection_Started = new(PLC_BASE + 1, "Plc_Connection_Started");
        public static readonly EventId Plc_Connection_Succeeded = new(PLC_BASE + 2, "Plc_Connection_Succeeded");
        public static readonly EventId Plc_Connection_Failed = new(PLC_BASE + 3, "Plc_Connection_Failed");
        public static readonly EventId Plc_Connection_Lost = new(PLC_BASE + 4, "Plc_Connection_Lost");
        public static readonly EventId Plc_Already_Connected = new(PLC_BASE + 5, "Plc_Already_Connected");
        public static readonly EventId Plc_Connecting = new(PLC_BASE + 6, "Plc_Connecting");
        public static readonly EventId Plc_Disconnection_Started = new(PLC_BASE + 7, "Plc_Disconnection_Started");
        public static readonly EventId Plc_Disconnection_Completed = new(PLC_BASE + 8, "Plc_Disconnection_Completed");
        public static readonly EventId Plc_Not_Connected = new(PLC_BASE + 9, "Plc_Not_Connected");
        public static readonly EventId Plc_Unavailable = new(PLC_BASE + 10, "Plc_Unavailable");

        public static readonly EventId Plc_Variable_Read_Started = new(PLC_BASE + 20, "Plc_Variable_Read_Started");
        public static readonly EventId Plc_Variable_Read_Succeeded = new(PLC_BASE + 21, "Plc_Variable_Read_Succeeded");
        public static readonly EventId Plc_Variable_Read_Failed = new(PLC_BASE + 22, "Plc_Variable_Read_Failed");
        public static readonly EventId Plc_Variables_Read_Failed = new(PLC_BASE + 23, "Plc_Variables_Read_Failed");
        public static readonly EventId Plc_Variable_Read_Error = new(PLC_BASE + 24, "Plc_Variable_Read_Error");
        public static readonly EventId Plc_Read_Internal_Error = new(PLC_BASE + 25, "Plc_Read_Internal_Error");
        public static readonly EventId Plc_Read_Timeout = new(PLC_BASE + 26, "Plc_Read_Timeout");
        public static readonly EventId Plc_Read_Cancelled = new(PLC_BASE + 27, "Plc_Read_Cancelled");

        public static readonly EventId Plc_Variable_Write_Started = new(PLC_BASE + 30, "Plc_Variable_Write_Started");
        public static readonly EventId Plc_Variable_Write_Succeeded = new(PLC_BASE + 31, "Plc_Variable_Write_Succeeded");
        public static readonly EventId Plc_Variable_Write_Failed = new(PLC_BASE + 32, "Plc_Variable_Write_Failed");
        public static readonly EventId Plc_Variables_Write_Failed = new(PLC_BASE + 33, "Plc_Variables_Write_Failed");
        public static readonly EventId Plc_Variable_Write_Error = new(PLC_BASE + 34, "Plc_Variable_Write_Error");

        public static readonly EventId Plc_Variables_Registry_Failed = new(PLC_BASE + 40, "Plc_Variables_Registry_Failed");
        public static readonly EventId Plc_Connection_Check_Error = new(PLC_BASE + 41, "Plc_Connection_Check_Error");
        public static readonly EventId Plc_Performance_Warning = new(PLC_BASE + 42, "Plc_Performance_Warning");
        public static readonly EventId Plc_Timer_Restart_Error = new(PLC_BASE + 43, "Plc_Timer_Restart_Error");
        public static readonly EventId Plc_Invalid_Results_Array = new(PLC_BASE + 44, "Plc_Invalid_Results_Array");
        public static readonly EventId Plc_State_Check_Exception = new(PLC_BASE + 45, "Plc_State_Check_Exception");
        public static readonly EventId Plc_Reconnect_Exception = new(PLC_BASE + 46, "Plc_Reconnect_Exception");
        public static readonly EventId Plc_Connect_Too_Frequent = new(PLC_BASE + 47, "Plc_Connect_Too_Frequent");
        public static readonly EventId Plc_Client_Created = new(PLC_BASE + 48, "Plc_Client_Created");
        public static readonly EventId Timer_Interval_Adjusted = new(PLC_BASE + 49, "Timer_Interval_Adjusted");

        // InvoancePLC特定事件
        public static readonly EventId Plc_Connect_Exception = new(PLC_BASE + 50, "Plc_Connect_Exception");
        public static readonly EventId Plc_Disconnect_Exception = new(PLC_BASE + 51, "Plc_Disconnect_Exception");
        public static readonly EventId Plc_Connection_Lost_Async = new(PLC_BASE + 52, "Plc_Connection_Lost_Async");
        public static readonly EventId Plc_Not_Connected_Async = new(PLC_BASE + 53, "Plc_Not_Connected_Async");
        public static readonly EventId Plc_Unavailable_Async = new(PLC_BASE + 54, "Plc_Unavailable_Async");
        public static readonly EventId Plc_State_Check_Exception_Async = new(PLC_BASE + 55, "Plc_State_Check_Exception_Async");
        public static readonly EventId Plc_Reconnect_Exception_Async = new(PLC_BASE + 56, "Plc_Reconnect_Exception_Async");

        // PLC实例相关事件
        public static readonly EventId Plc_Instance_Null = new(PLC_BASE + 60, "Plc_Instance_Null");
        public static readonly EventId Z_Home_Confirm_Chuck_Timeout = new(PLC_BASE + 61, "Z_Home_Confirm_Chuck_Timeout");
        public static readonly EventId Plc_Instance_Null_Skip_Registry = new(PLC_BASE + 62, "Plc_Instance_Null_Skip_Registry");
        public static readonly EventId Plc_Export_Change_Action_Get_Failed = new(PLC_BASE + 63, "Plc_Export_Change_Action_Get_Failed");
        public static readonly EventId Plc_Instance_Null_Skip_Write = new(PLC_BASE + 64, "Plc_Instance_Null_Skip_Write");
        public static readonly EventId Plc_Instance_Null_Skip_Read = new(PLC_BASE + 65, "Plc_Instance_Null_Skip_Read");

        // PLC通知相关事件
        public static readonly EventId PLC_Notification_Added = new(PLC_BASE + 70, "PLC_Notification_Added");
        public static readonly EventId PLC_Variables_Registered = new(PLC_BASE + 71, "PLC_Variables_Registered");
        public static readonly EventId PLC_Variables_Registration_Failed = new(PLC_BASE + 72, "PLC_Variables_Registration_Failed");
        public static readonly EventId PLC_Variable_Registered = new(PLC_BASE + 73, "PLC_Variable_Registered");
        public static readonly EventId PLC_Variable_Registration_Failed = new(PLC_BASE + 74, "PLC_Variable_Registration_Failed");
        public static readonly EventId PLC_Variables_Unregistered = new(PLC_BASE + 75, "PLC_Variables_Unregistered");
        public static readonly EventId PLC_Variables_Unregistration_Failed = new(PLC_BASE + 76, "PLC_Variables_Unregistration_Failed");
        public static readonly EventId PLC_Variable_Read = new(PLC_BASE + 77, "PLC_Variable_Read");
        public static readonly EventId PLC_Variable_Read_Failed = new(PLC_BASE + 78, "PLC_Variable_Read_Failed");
        public static readonly EventId PLC_Variable_Write = new(PLC_BASE + 79, "PLC_Variable_Write");
        public static readonly EventId PLC_Variable_Write_Failed = new(PLC_BASE + 80, "PLC_Variable_Write_Failed");

        #endregion

        #region 轴控制事件 (2000-2999)

        public static readonly EventId Axis_Initialize_Started = new(AXIS_BASE + 1, "Axis_Initialize_Started");
        public static readonly EventId Axis_Initialize_Succeeded = new(AXIS_BASE + 2, "Axis_Initialize_Succeeded");
        public static readonly EventId Axis_Initialize_Failed = new(AXIS_BASE + 3, "Axis_Initialize_Failed");
        public static readonly EventId Axis_Instance_Created = new(AXIS_BASE + 4, "Axis_Instance_Created");
        public static readonly EventId Axis_Instance_Create_Failed = new(AXIS_BASE + 5, "Axis_Instance_Create_Failed");

        public static readonly EventId Axis_Move_Started = new(AXIS_BASE + 10, "Axis_Move_Started");
        public static readonly EventId Axis_Move_Completed = new(AXIS_BASE + 11, "Axis_Move_Completed");
        public static readonly EventId Axis_Move_Failed = new(AXIS_BASE + 12, "Axis_Move_Failed");
        public static readonly EventId Axis_Move_Error = new(AXIS_BASE + 13, "Axis_Move_Error");
        public static readonly EventId Axis_Move_Timeout = new(AXIS_BASE + 14, "Axis_Move_Timeout");
        public static readonly EventId Axis_Move_Cancelled = new(AXIS_BASE + 15, "Axis_Move_Cancelled");

        public static readonly EventId Axis_Home_Started = new(AXIS_BASE + 20, "Axis_Home_Started");
        public static readonly EventId Axis_Home_Completed = new(AXIS_BASE + 21, "Axis_Home_Completed");
        public static readonly EventId Axis_Home_Failed = new(AXIS_BASE + 22, "Axis_Home_Failed");
        public static readonly EventId Axis_Home_Success = new(AXIS_BASE + 23, "Axis_Home_Success");
        public static readonly EventId Axis_Home_Timeout = new(AXIS_BASE + 24, "Axis_Home_Timeout");
        public static readonly EventId Axis_Home_Cancelled = new(AXIS_BASE + 25, "Axis_Home_Cancelled");

        public static readonly EventId Axis_Stop_Started = new(AXIS_BASE + 30, "Axis_Stop_Started");
        public static readonly EventId Axis_Stop_Completed = new(AXIS_BASE + 31, "Axis_Stop_Completed");
        public static readonly EventId Axis_Stop_Error = new(AXIS_BASE + 32, "Axis_Stop_Error");

        public static readonly EventId Axis_Enable_Changed = new(AXIS_BASE + 40, "Axis_Enable_Changed");
        public static readonly EventId Axis_Enable_Failed = new(AXIS_BASE + 41, "Axis_Enable_Failed");
        public static readonly EventId Axis_Speed_Set = new(AXIS_BASE + 42, "Axis_Speed_Set");
        public static readonly EventId Axis_Speed_Set_Failed = new(AXIS_BASE + 43, "Axis_Speed_Set_Failed");

        public static readonly EventId Axis_Position_Update_Error = new(AXIS_BASE + 50, "Axis_Position_Update_Error");
        public static readonly EventId Axis_Position_Read_Error = new(AXIS_BASE + 51, "Axis_Position_Read_Error");
        public static readonly EventId Axis_Safety_Check = new(AXIS_BASE + 52, "Axis_Safety_Check");
        public static readonly EventId Axis_Operation_Failed = new(AXIS_BASE + 53, "Axis_Operation_Failed");
        public static readonly EventId Axis_Operation_Error = new(AXIS_BASE + 54, "Axis_Operation_Error");
        public static readonly EventId Axis_Operation_Info = new(AXIS_BASE + 55, "Axis_Operation_Info");

        public static readonly EventId Axis_Alarm_Occurred = new(AXIS_BASE + 60, "Axis_Alarm_Occurred");
        public static readonly EventId Axis_Alarm_Cleared = new(AXIS_BASE + 61, "Axis_Alarm_Cleared");

        // 轴Jog操作事件
        public static readonly EventId Axis_Jog_Forward_Started = new(AXIS_BASE + 70, "Axis_Jog_Forward_Started");
        public static readonly EventId Axis_Jog_Backward_Started = new(AXIS_BASE + 71, "Axis_Jog_Backward_Started");
        public static readonly EventId Axis_Jog_Stop_Started = new(AXIS_BASE + 72, "Axis_Jog_Stop_Started");
        public static readonly EventId Axis_Jog_Error = new(AXIS_BASE + 73, "Axis_Jog_Error");
        public static readonly EventId Axis_Id_Error = new(AXIS_BASE + 74, "Axis_Id_Error");
        public static readonly EventId Axis_Positioning_Started = new(AXIS_BASE + 75, "Axis_Positioning_Started");

        // 轴事件注册相关
        public static readonly EventId Axis_Event_Unregistered = new(AXIS_BASE + 80, "Axis_Event_Unregistered");
        public static readonly EventId Axis_Event_Unregister_Failed = new(AXIS_BASE + 81, "Axis_Event_Unregister_Failed");
        public static readonly EventId Axis_Control_Cleanup_Error = new(AXIS_BASE + 82, "Axis_Control_Cleanup_Error");

        // 轴重置相关事件
        public static readonly EventId Axis_Reset_Start = new(AXIS_BASE + 85, "Axis_Reset_Start");
        public static readonly EventId Axis_Reset_Complete = new(AXIS_BASE + 86, "Axis_Reset_Complete");
        public static readonly EventId Axis_Reset_Timeout = new(AXIS_BASE + 87, "Axis_Reset_Timeout");

        // 轴实例管理事件
        public static readonly EventId Axis_Instance_Created_General = new(AXIS_BASE + 88, "Axis_Instance_Created_General");
        public static readonly EventId Axis_Enable_Failed_Not_Connected = new(AXIS_BASE + 89, "Axis_Enable_Failed_Not_Connected");
        public static readonly EventId Set_Jog_Speed_Failed = new(AXIS_BASE + 90, "Set_Jog_Speed_Failed");

        // 轴连接相关事件
        public static readonly EventId Connect_Start_General = new(AXIS_BASE + 91, "Connect_Start_General");
        public static readonly EventId Connect_Success_General = new(AXIS_BASE + 92, "Connect_Success_General");
        public static readonly EventId Connect_Success_General2 = new(AXIS_BASE + 93, "Connect_Success_General2");
        public static readonly EventId Connect_Failed_General = new(AXIS_BASE + 94, "Connect_Failed_General");
        public static readonly EventId Connect_Failed_General2 = new(AXIS_BASE + 95, "Connect_Failed_General2");

        // 特定轴操作事件
        public static readonly EventId Xyr_Position_Update_Error = new(AXIS_BASE + 100, "Xyr_Position_Update_Error");
        public static readonly EventId Top_Wafer_Move_Failed = new(AXIS_BASE + 101, "Top_Wafer_Move_Failed");
        public static readonly EventId Top_Wafer_Move_Started = new(AXIS_BASE + 102, "Top_Wafer_Move_Started");
        public static readonly EventId Xyr_Photo_Location_Failed = new(AXIS_BASE + 103, "Xyr_Photo_Location_Failed");
        public static readonly EventId Platform_Photo_Location_Failed = new(AXIS_BASE + 104, "Platform_Photo_Location_Failed");
        public static readonly EventId Xyr_Initial_Location_Failed = new(AXIS_BASE + 105, "Xyr_Initial_Location_Failed");
        public static readonly EventId Xyr_Motion_To_Initial_Location_Failed = new(AXIS_BASE + 106, "Xyr_Motion_To_Initial_Location_Failed");
        public static readonly EventId Xyr_Motion_To_Bottom_Wafer_Photo_Location_Failed = new(AXIS_BASE + 107, "Xyr_Motion_To_Bottom_Wafer_Photo_Location_Failed");

        // 轴连接扩展事件
        public static readonly EventId Connect_Timeout_General = new(AXIS_BASE + 150, "Connect_Timeout_General");
        public static readonly EventId Connect_Exception_General = new(AXIS_BASE + 151, "Connect_Exception_General");
        public static readonly EventId Connect_Process_Exception_General = new(AXIS_BASE + 152, "Connect_Process_Exception_General");
        public static readonly EventId Connect_Process_Start_General = new(AXIS_BASE + 153, "Connect_Process_Start_General");
        public static readonly EventId Already_Connected_General = new(AXIS_BASE + 154, "Already_Connected_General");
        public static readonly EventId Already_Connected_General2 = new(AXIS_BASE + 155, "Already_Connected_General2");

        // 轴控制相关事件
        public static readonly EventId Set_Control_Axis_Start_General = new(AXIS_BASE + 160, "Set_Control_Axis_Start_General");
        public static readonly EventId Set_Control_Axis_Failed_General = new(AXIS_BASE + 161, "Set_Control_Axis_Failed_General");
        public static readonly EventId Open_Device_Start_General = new(AXIS_BASE + 162, "Open_Device_Start_General");
        public static readonly EventId Com_Connect_Failed = new(AXIS_BASE + 163, "Com_Connect_Failed");

        // 多轴操作事件
        public static readonly EventId Multi_Axis_Start_General = new(AXIS_BASE + 200, "Multi_Axis_Start_General");
        public static readonly EventId Multi_Axis_Warning = new(AXIS_BASE + 201, "Multi_Axis_Warning");
        public static readonly EventId Development_Mode_Connect_General = new(AXIS_BASE + 202, "Development_Mode_Connect_General");

        // 轴事件注册
        public static readonly EventId Axis_Event_Registration_Failed = new(AXIS_BASE + 300, "Axis_Event_Registration_Failed");
        public static readonly EventId Events_Registration_Success = new(AXIS_BASE + 301, "Events_Registration_Success");
        public static readonly EventId Events_Registration_Failed = new(AXIS_BASE + 302, "Events_Registration_Failed");
        public static readonly EventId State_Watch_Init_Failed = new(AXIS_BASE + 303, "State_Watch_Init_Failed");

        #endregion

        #region UI操作事件 (3000-3999)

        public static readonly EventId Page_Initialize_Started = new(UI_BASE + 1, "Page_Initialize_Started");
        public static readonly EventId Page_Initialize_Completed = new(UI_BASE + 2, "Page_Initialize_Completed");
        public static readonly EventId Page_Initialize_Failed = new(UI_BASE + 3, "Page_Initialize_Failed");
        public static readonly EventId Page_Closed = new(UI_BASE + 4, "Page_Closed");
        public static readonly EventId Page_Load_Started = new(UI_BASE + 5, "Page_Load_Started");
        public static readonly EventId Page_Load_Completed = new(UI_BASE + 6, "Page_Load_Completed");
        public static readonly EventId Page_Load_Phase = new(UI_BASE + 7, "Page_Load_Phase");
        public static readonly EventId Page_Cleanup_Complete = new(UI_BASE + 8, "Page_Cleanup_Complete");

        public static readonly EventId Button_Clicked = new(UI_BASE + 20, "Button_Clicked");
        public static readonly EventId UI_Update_Started = new(UI_BASE + 21, "UI_Update_Started");
        public static readonly EventId UI_Update_Completed = new(UI_BASE + 22, "UI_Update_Completed");
        public static readonly EventId UI_Update_Failed = new(UI_BASE + 23, "UI_Update_Failed");
        public static readonly EventId UI_Update_Error = new(UI_BASE + 24, "UI_Update_Error");
        public static readonly EventId UI_Thread_Invoke_Failed = new(UI_BASE + 25, "UI_Thread_Invoke_Failed");
        public static readonly EventId UI_Service_Exception = new(UI_BASE + 26, "UI_Service_Exception");

        public static readonly EventId Timer_Started = new(UI_BASE + 40, "Timer_Started");
        public static readonly EventId Timer_Stopped = new(UI_BASE + 41, "Timer_Stopped");
        public static readonly EventId Timer_Error = new(UI_BASE + 42, "Timer_Error");
        public static readonly EventId Timer_Resumed = new(UI_BASE + 43, "Timer_Resumed");
        public static readonly EventId Input_Timer_Null_At_Load = new(UI_BASE + 44, "Input_Timer_Null_At_Load");

        public static readonly EventId Form_Closing_Skip_Visibility = new(UI_BASE + 50, "Form_Closing_Skip_Visibility");
        public static readonly EventId Form_Closing_In_Update_Timer = new(UI_BASE + 51, "Form_Closing_In_Update_Timer");
        public static readonly EventId Form_Closing_In_Timer = new(UI_BASE + 52, "Form_Closing_In_Timer");
        public static readonly EventId Form_Disposed_In_Timer = new(UI_BASE + 53, "Form_Disposed_In_Timer");
        public static readonly EventId Form_Load_Complete = new(UI_BASE + 54, "Form_Load_Complete");

        public static readonly EventId Tab_Switched = new(UI_BASE + 60, "Tab_Switched");
        public static readonly EventId Tab_Initialization_Failed = new(UI_BASE + 61, "Tab_Initialization_Failed");
        public static readonly EventId Control_Initialization_Failed = new(UI_BASE + 62, "Control_Initialization_Failed");
        public static readonly EventId Control_Initialization_Timeout = new(UI_BASE + 63, "Control_Initialization_Timeout");

        // 操作相关事件
        public static readonly EventId Operation_Cancelled = new(UI_BASE + 100, "Operation_Cancelled");
        public static readonly EventId Operation_Complete = new(UI_BASE + 101, "Operation_Complete");
        public static readonly EventId Operation_Started = new(UI_BASE + 102, "Operation_Started");
        public static readonly EventId Operation_Failed = new(UI_BASE + 103, "Operation_Failed");

        // 特定页面事件
        public static readonly EventId Login_Page_Loaded = new(UI_BASE + 200, "Login_Page_Loaded");
        public static readonly EventId Alignment_Page_Accessed = new(UI_BASE + 201, "Alignment_Page_Accessed");
        public static readonly EventId User_Management_Page_Accessed = new(UI_BASE + 202, "User_Management_Page_Accessed");
        public static readonly EventId Unauthorized_Access_Attempt = new(UI_BASE + 203, "Unauthorized_Access_Attempt");
        public static readonly EventId Unauthorized_Alignment_Access = new(UI_BASE + 204, "Unauthorized_Alignment_Access");

        // 晶圆操作UI事件
        public static readonly EventId Top_Wafer_Photo_Started = new(UI_BASE + 300, "Top_Wafer_Photo_Started");
        public static readonly EventId Top_Wafer_Photo_Completed = new(UI_BASE + 301, "Top_Wafer_Photo_Completed");
        public static readonly EventId Top_Wafer_Photo_Cancelled = new(UI_BASE + 302, "Top_Wafer_Photo_Cancelled");
        public static readonly EventId Top_Wafer_Photo_Error = new(UI_BASE + 303, "Top_Wafer_Photo_Error");
        public static readonly EventId Top_Wafer_Photo_Move_Error = new(UI_BASE + 304, "Top_Wafer_Photo_Move_Error");
        public static readonly EventId Bottom_Wafer_Photo_Started = new(UI_BASE + 305, "Bottom_Wafer_Photo_Started");
        public static readonly EventId Bottom_Wafer_Photo_Completed = new(UI_BASE + 306, "Bottom_Wafer_Photo_Completed");
        public static readonly EventId Alignment_Started = new(UI_BASE + 307, "Alignment_Started");
        public static readonly EventId Single_Alignment_Started = new(UI_BASE + 308, "Single_Alignment_Started");
        public static readonly EventId Single_Alignment_Error = new(UI_BASE + 309, "Single_Alignment_Error");
        public static readonly EventId Top_Wafer_Mark_Recognition_Started = new(UI_BASE + 310, "Top_Wafer_Mark_Recognition_Started");

        // 清理和关闭事件
        public static readonly EventId Program_Closing_Started = new(UI_BASE + 400, "Program_Closing_Started");
        public static readonly EventId Cleanup_Timeout = new(UI_BASE + 401, "Cleanup_Timeout");
        public static readonly EventId Normal_Cleanup_Complete = new(UI_BASE + 402, "Normal_Cleanup_Complete");
        public static readonly EventId Cleanup_Error = new(UI_BASE + 403, "Cleanup_Error");
        public static readonly EventId Force_Exit = new(UI_BASE + 404, "Force_Exit");
        public static readonly EventId Force_Exit_Started = new(UI_BASE + 405, "Force_Exit_Started");
        public static readonly EventId Worker_Cancel_Error = new(UI_BASE + 406, "Worker_Cancel_Error");
        public static readonly EventId Worker_Dispose_Error = new(UI_BASE + 407, "Worker_Dispose_Error");
        public static readonly EventId Worker_Cleanup_Error = new(UI_BASE + 408, "Worker_Cleanup_Error");

        // 设备控制UI事件
        public static readonly EventId Emergency_Stop_Pressed = new(UI_BASE + 500, "Emergency_Stop_Pressed");
        public static readonly EventId Emergency_Stop_Completed = new(UI_BASE + 501, "Emergency_Stop_Completed");
        public static readonly EventId Device_Init_Started = new(UI_BASE + 502, "Device_Init_Started");
        public static readonly EventId Device_Init_Completed = new(UI_BASE + 503, "Device_Init_Completed");
        public static readonly EventId Device_Init_Failed = new(UI_BASE + 504, "Device_Init_Failed");
        public static readonly EventId Device_Init_Cancelled = new(UI_BASE + 505, "Device_Init_Cancelled");
        public static readonly EventId Clear_Alarm_Started = new(UI_BASE + 506, "Clear_Alarm_Started");
        public static readonly EventId Clear_Alarm_Completed = new(UI_BASE + 507, "Clear_Alarm_Completed");
        public static readonly EventId Clear_Alarm_Failed = new(UI_BASE + 508, "Clear_Alarm_Failed");

        // 页面访问和权限事件
        public static readonly EventId Skip_Permission_Update = new(UI_BASE + 600, "Skip_Permission_Update");
        public static readonly EventId Page_Permissions_Updated = new(UI_BASE + 601, "Page_Permissions_Updated");
        public static readonly EventId Update_Page_Permissions_Error = new(UI_BASE + 602, "Update_Page_Permissions_Error");

        // 表单和控件事件
        public static readonly EventId Header_Auto_Selection = new(UI_BASE + 700, "Header_Auto_Selection");
        public static readonly EventId Control_Initialization_Started = new(UI_BASE + 701, "Control_Initialization_Started");
        public static readonly EventId Control_Initialization_In_Progress = new(UI_BASE + 702, "Control_Initialization_In_Progress");
        public static readonly EventId Control_Initialization_Completed = new(UI_BASE + 703, "Control_Initialization_Completed");

        // 晶圆操作事件
        public static readonly EventId Top_Wafer_Control_Requested = new(UI_BASE + 800, "Top_Wafer_Control_Requested");
        public static readonly EventId Top_Wafer_Control_Success = new(UI_BASE + 801, "Top_Wafer_Control_Success");
        public static readonly EventId Chuck_Lock_Closed_Failed = new(UI_BASE + 802, "Chuck_Lock_Closed_Failed");
        public static readonly EventId Chuck_Lock_Open_Failed_Specific = new(UI_BASE + 803, "Chuck_Lock_Open_Failed_Specific");
        public static readonly EventId Top_Wafer_Tray_Out_Error = new(UI_BASE + 804, "Top_Wafer_Tray_Out_Error");
        public static readonly EventId Top_Wafer_Fix_Error = new(UI_BASE + 805, "Top_Wafer_Fix_Error");
        public static readonly EventId Top_Wafer_Take_Up_Started = new(UI_BASE + 806, "Top_Wafer_Take_Up_Started");
        public static readonly EventId Top_Wafer_Take_Up_Failed = new(UI_BASE + 807, "Top_Wafer_Take_Up_Failed");
        public static readonly EventId Top_Wafer_Pickup_Error = new(UI_BASE + 808, "Top_Wafer_Pickup_Error");

        // 底部晶圆操作事件
        public static readonly EventId Bottom_Wafer_Down_Started = new(UI_BASE + 820, "Bottom_Wafer_Down_Started");
        public static readonly EventId Bottom_Wafer_Down_Error = new(UI_BASE + 821, "Bottom_Wafer_Down_Error");
        public static readonly EventId Bottom_Wafer_Take_Down_Started = new(UI_BASE + 822, "Bottom_Wafer_Take_Down_Started");
        public static readonly EventId Bottom_Wafer_Take_Down_Failed = new(UI_BASE + 823, "Bottom_Wafer_Take_Down_Failed");
        public static readonly EventId Bottom_Wafer_Place_Error = new(UI_BASE + 824, "Bottom_Wafer_Place_Error");
        public static readonly EventId Bottom_Wafer_Photo_Move_Error = new(UI_BASE + 825, "Bottom_Wafer_Photo_Move_Error");
        public static readonly EventId Bottom_Wafer_Photo_Error = new(UI_BASE + 826, "Bottom_Wafer_Photo_Error");
        public static readonly EventId Bottom_Wafer_Move_Started = new(UI_BASE + 827, "Bottom_Wafer_Move_Started");
        public static readonly EventId Bottom_Wafer_Move_Failed = new(UI_BASE + 828, "Bottom_Wafer_Move_Failed");

        // 晶圆标记识别事件
        public static readonly EventId Top_Wafer_Mark_Identified_Failed_Specific = new(UI_BASE + 840, "Top_Wafer_Mark_Identified_Failed_Specific");

        // 拍照相关事件
        public static readonly EventId Target_Take_Photo_Started = new(UI_BASE + 850, "Target_Take_Photo_Started");
        public static readonly EventId Target_Take_Photo_Failed = new(UI_BASE + 851, "Target_Take_Photo_Failed");

        // 对齐相关事件
        public static readonly EventId Alignment_Stopped = new(UI_BASE + 860, "Alignment_Stopped");
        public static readonly EventId Alignment_Error = new(UI_BASE + 861, "Alignment_Error");
        public static readonly EventId Alignment_Cancelled = new(UI_BASE + 862, "Alignment_Cancelled");

        // 确认流程事件
        public static readonly EventId Xyr_Confirm_Started = new(UI_BASE + 870, "Xyr_Confirm_Started");
        public static readonly EventId Xyr_Confirm_Failed = new(UI_BASE + 871, "Xyr_Confirm_Failed");
        public static readonly EventId Take_Down_Confirm_Started = new(UI_BASE + 872, "Take_Down_Confirm_Started");
        public static readonly EventId Take_Down_Confirm_Failed = new(UI_BASE + 873, "Take_Down_Confirm_Failed");
        public static readonly EventId Variable_Confirm_Started = new(UI_BASE + 874, "Variable_Confirm_Started");
        public static readonly EventId Variable_Confirm_Failed = new(UI_BASE + 875, "Variable_Confirm_Failed");
        public static readonly EventId Set_Take_Confirm_Variables_Failed = new(UI_BASE + 876, "Set_Take_Confirm_Variables_Failed");

        // 轴初始化事件
        public static readonly EventId Z_Axis_Initial_Started = new(UI_BASE + 880, "Z_Axis_Initial_Started");
        public static readonly EventId Z_Axis_Initial_Location_Failed = new(UI_BASE + 881, "Z_Axis_Initial_Location_Failed");

        // 位置失败事件
        public static readonly EventId Location_Failed_Specific = new(UI_BASE + 890, "Location_Failed_Specific");
        public static readonly EventId Get_Take_Confirm_Variables_Failed = new(UI_BASE + 891, "Get_Take_Confirm_Variables_Failed");

        // 清理相关扩展事件
        public static readonly EventId Normal_Cleanup_Complete_Specific = new(UI_BASE + 900, "Normal_Cleanup_Complete_Specific");
        public static readonly EventId Cleanup_Error_Specific = new(UI_BASE + 901, "Cleanup_Error_Specific");
        public static readonly EventId Start_Main_Window_View_Model_Cleanup = new(UI_BASE + 902, "Start_Main_Window_View_Model_Cleanup");
        public static readonly EventId Main_Window_View_Model_Cleanup_Complete_Specific = new(UI_BASE + 903, "Main_Window_View_Model_Cleanup_Complete_Specific");
        public static readonly EventId Main_Window_View_Model_Cleanup_Error_Specific = new(UI_BASE + 904, "Main_Window_View_Model_Cleanup_Error_Specific");
        public static readonly EventId Plc_Cleanup_Complete_Specific = new(UI_BASE + 905, "Plc_Cleanup_Complete_Specific");
        public static readonly EventId Plc_Cleanup_Error_Specific = new(UI_BASE + 906, "Plc_Cleanup_Error_Specific");
        public static readonly EventId Plc_Cleanup_Timeout = new(UI_BASE + 907, "Plc_Cleanup_Timeout");
        public static readonly EventId Component_Cleanup_Error = new(UI_BASE + 908, "Component_Cleanup_Error");
        public static readonly EventId Object_Dispose_Error = new(UI_BASE + 909, "Object_Dispose_Error");
        public static readonly EventId Cleanup_Process_Complete_Specific = new(UI_BASE + 910, "Cleanup_Process_Complete_Specific");
        public static readonly EventId Async_Cleanup_Error = new(UI_BASE + 911, "Async_Cleanup_Error");
        public static readonly EventId Page_Cleanup_Timeout = new(UI_BASE + 912, "Page_Cleanup_Timeout");
        public static readonly EventId Page_Cleanup_Complete_Header = new(UI_BASE + 913, "Page_Cleanup_Complete_Header");
        public static readonly EventId Page_Cleanup_Error_Header = new(UI_BASE + 914, "Page_Cleanup_Error_Header");

        // 系统退出相关事件
        public static readonly EventId Environment_Exit = new(UI_BASE + 920, "Environment_Exit");
        public static readonly EventId Process_Kill = new(UI_BASE + 921, "Process_Kill");

        // 后台任务管理事件
        public static readonly EventId Stop_All_Background_Tasks = new(UI_BASE + 930, "Stop_All_Background_Tasks");
        public static readonly EventId Stop_Background_Tasks_Error = new(UI_BASE + 931, "Stop_Background_Tasks_Error");

        // 线程管理事件
        public static readonly EventId Thread_Count = new(UI_BASE + 940, "Thread_Count");

        // 定时器相关事件
        public static readonly EventId Xyr_Timer_Paused = new(UI_BASE + 950, "Xyr_Timer_Paused");

        // 配置保存错误事件（避免重复定义）

        #endregion

        #region 气缸控制事件 (4000-4999)

        public static readonly EventId Cylinder_Operation_Started = new(CYLINDER_BASE + 1, "Cylinder_Operation_Started");
        public static readonly EventId Cylinder_Operation_Completed = new(CYLINDER_BASE + 2, "Cylinder_Operation_Completed");
        public static readonly EventId Cylinder_Operation_Failed = new(CYLINDER_BASE + 3, "Cylinder_Operation_Failed");
        public static readonly EventId Cylinder_Operation_Error = new(CYLINDER_BASE + 4, "Cylinder_Operation_Error");
        public static readonly EventId Cylinder_State_Changed = new(CYLINDER_BASE + 5, "Cylinder_State_Changed");
        public static readonly EventId Cylinder_Safety_Check_Failed = new(CYLINDER_BASE + 6, "Cylinder_Safety_Check_Failed");

        // 具体气缸控制事件
        public static readonly EventId Top_Wafer_Control_Failed = new(CYLINDER_BASE + 10, "Top_Wafer_Control_Failed");
        public static readonly EventId Tray_Wafer_Outer_Control_Failed = new(CYLINDER_BASE + 11, "Tray_Wafer_Outer_Control_Failed");
        public static readonly EventId Tray_Wafer_Inner_Control_Failed = new(CYLINDER_BASE + 12, "Tray_Wafer_Inner_Control_Failed");
        public static readonly EventId Tray_Control_Failed = new(CYLINDER_BASE + 13, "Tray_Control_Failed");
        public static readonly EventId Chuck_Lock_Control_Failed = new(CYLINDER_BASE + 14, "Chuck_Lock_Control_Failed");
        public static readonly EventId Horizontal_Adjust_Control_Failed = new(CYLINDER_BASE + 15, "Horizontal_Adjust_Control_Failed");

        #endregion

        #region 用户管理事件 (5000-5999)

        public static readonly EventId User_Login_Started = new(USER_BASE + 1, "User_Login_Started");
        public static readonly EventId User_Login_Succeeded = new(USER_BASE + 2, "User_Login_Succeeded");
        public static readonly EventId User_Login_Failed = new(USER_BASE + 3, "User_Login_Failed");
        public static readonly EventId User_Login_Disabled = new(USER_BASE + 4, "User_Login_Disabled");
        public static readonly EventId User_Logout = new(USER_BASE + 5, "User_Logout");
        public static readonly EventId Login_Exception = new(USER_BASE + 6, "Login_Exception");
        public static readonly EventId Login_Cancelled = new(USER_BASE + 7, "Login_Cancelled");
        public static readonly EventId Login_Form_Closing = new(USER_BASE + 8, "Login_Form_Closing");
        public static readonly EventId Default_Operator_Created = new(USER_BASE + 9, "Default_Operator_Created");

        // 用户数据管理事件
        public static readonly EventId User_Data_Load_Success = new(USER_BASE + 10, "User_Data_Load_Success");
        public static readonly EventId User_Data_Load_Failed = new(USER_BASE + 11, "User_Data_Load_Failed");
        public static readonly EventId User_Create_Success = new(USER_BASE + 12, "User_Create_Success");
        public static readonly EventId User_Create_Failed = new(USER_BASE + 13, "User_Create_Failed");
        public static readonly EventId User_Create_Error = new(USER_BASE + 14, "User_Create_Error");
        public static readonly EventId User_Update_Success = new(USER_BASE + 15, "User_Update_Success");
        public static readonly EventId User_Update_Failed = new(USER_BASE + 16, "User_Update_Failed");
        public static readonly EventId User_Update_Error = new(USER_BASE + 17, "User_Update_Error");
        public static readonly EventId User_Password_Update_Success = new(USER_BASE + 18, "User_Password_Update_Success");
        public static readonly EventId User_Password_Update_Failed = new(USER_BASE + 19, "User_Password_Update_Failed");
        public static readonly EventId User_Password_Update_Error = new(USER_BASE + 20, "User_Password_Update_Error");
        public static readonly EventId User_Delete_Success = new(USER_BASE + 21, "User_Delete_Success");
        public static readonly EventId User_Delete_Failed = new(USER_BASE + 22, "User_Delete_Failed");
        public static readonly EventId User_Delete_Error = new(USER_BASE + 23, "User_Delete_Error");

        // 角色数据管理事件
        public static readonly EventId Role_Create_Success = new(USER_BASE + 24, "Role_Create_Success");
        public static readonly EventId Role_Create_Failed = new(USER_BASE + 25, "Role_Create_Failed");
        public static readonly EventId Role_Update_Success = new(USER_BASE + 26, "Role_Update_Success");
        public static readonly EventId Role_Update_Failed = new(USER_BASE + 27, "Role_Update_Failed");
        public static readonly EventId Role_Delete_Success = new(USER_BASE + 28, "Role_Delete_Success");
        public static readonly EventId Role_Delete_Failed = new(USER_BASE + 29, "Role_Delete_Failed");

        public static readonly EventId User_Permission_Denied = new(USER_BASE + 40, "User_Permission_Denied");
        public static readonly EventId User_List_Loaded = new(USER_BASE + 41, "User_List_Loaded");
        public static readonly EventId Load_User_List_Error = new(USER_BASE + 42, "Load_User_List_Error");
        public static readonly EventId User_Deleted = new(USER_BASE + 43, "User_Deleted");
        public static readonly EventId Delete_User_Error = new(USER_BASE + 44, "Delete_User_Error");
        public static readonly EventId User_Created = new(USER_BASE + 45, "User_Created");
        public static readonly EventId Add_User_Error = new(USER_BASE + 46, "Add_User_Error");
        public static readonly EventId User_Search = new(USER_BASE + 47, "User_Search");
        public static readonly EventId User_Search_Error = new(USER_BASE + 48, "User_Search_Error");
        public static readonly EventId User_Information_Updated = new(USER_BASE + 49, "User_Information_Updated");
        public static readonly EventId Edit_User_Error = new(USER_BASE + 50, "Edit_User_Error");
        public static readonly EventId User_Auto_Selected = new(USER_BASE + 51, "User_Auto_Selected");
        public static readonly EventId User_Password_Changed = new(USER_BASE + 52, "User_Password_Changed");
        public static readonly EventId Change_Password_Error = new(USER_BASE + 53, "Change_Password_Error");
        public static readonly EventId User_Self_Password_Changed = new(USER_BASE + 54, "User_Self_Password_Changed");
        public static readonly EventId Change_Password_Dialog_Error = new(USER_BASE + 55, "Change_Password_Dialog_Error");

        // 角色管理事件
        public static readonly EventId Role_Created = new(USER_BASE + 70, "Role_Created");
        public static readonly EventId Role_Create_Error = new(USER_BASE + 71, "Role_Create_Error");
        public static readonly EventId Role_Updated = new(USER_BASE + 72, "Role_Updated");
        public static readonly EventId Role_Update_Error = new(USER_BASE + 73, "Role_Update_Error");
        public static readonly EventId Role_Deleted = new(USER_BASE + 74, "Role_Deleted");
        public static readonly EventId Role_Delete_Error = new(USER_BASE + 75, "Role_Delete_Error");

        // 用户管理扩展事件（避免重复定义）

        #endregion

        #region 配置管理事件 (6000-6999)

        public static readonly EventId Config_Load_Started = new(CONFIG_BASE + 1, "Config_Load_Started");
        public static readonly EventId Config_Load_Succeeded = new(CONFIG_BASE + 2, "Config_Load_Succeeded");
        public static readonly EventId Config_Load_Failed = new(CONFIG_BASE + 3, "Config_Load_Failed");
        public static readonly EventId Config_Loading = new(CONFIG_BASE + 4, "Config_Loading");

        // JsonFileConfiguration特定事件
        public static readonly EventId Load_Default_Configuration_Failed = new(CONFIG_BASE + 5, "Load_Default_Configuration_Failed");
        public static readonly EventId Load_User_Configuration_Failed = new(CONFIG_BASE + 6, "Load_User_Configuration_Failed");

        public static readonly EventId Config_Save_Started = new(CONFIG_BASE + 10, "Config_Save_Started");
        public static readonly EventId Config_Save_Succeeded = new(CONFIG_BASE + 11, "Config_Save_Succeeded");
        public static readonly EventId Config_Save_Failed = new(CONFIG_BASE + 12, "Config_Save_Failed");

        public static readonly EventId Recipe_Load_Started = new(CONFIG_BASE + 20, "Recipe_Load_Started");
        public static readonly EventId Recipe_Load_Succeeded = new(CONFIG_BASE + 21, "Recipe_Load_Succeeded");
        public static readonly EventId Recipe_Load_Failed = new(CONFIG_BASE + 22, "Recipe_Load_Failed");
        public static readonly EventId Recipe_Save_Started = new(CONFIG_BASE + 23, "Recipe_Save_Started");
        public static readonly EventId Recipe_Save_Succeeded = new(CONFIG_BASE + 24, "Recipe_Save_Succeeded");
        public static readonly EventId Recipe_Save_Failed = new(CONFIG_BASE + 25, "Recipe_Save_Failed");
        public static readonly EventId Recipe_Execute_Check = new(CONFIG_BASE + 26, "Recipe_Execute_Check");
        public static readonly EventId Operate_Recipe_Event = new(CONFIG_BASE + 27, "Operate_Recipe_Event");
        public static readonly EventId Abort_Recipe = new(CONFIG_BASE + 28, "Abort_Recipe");
        public static readonly EventId Stop_Recipe = new(CONFIG_BASE + 29, "Stop_Recipe");
        public static readonly EventId Restart_Recipe = new(CONFIG_BASE + 30, "Restart_Recipe");
        public static readonly EventId Pause_Recipe = new(CONFIG_BASE + 31, "Pause_Recipe");

        // 校准配置事件
        public static readonly EventId Platform_Calibrate_Failed = new(CONFIG_BASE + 40, "Platform_Calibrate_Failed");
        public static readonly EventId Calibrated_Moving_Failed = new(CONFIG_BASE + 41, "Calibrated_Moving_Failed");
        public static readonly EventId Save_Calibration_Config_Failed = new(CONFIG_BASE + 42, "Save_Calibration_Config_Failed");
        public static readonly EventId Load_Calibration_Config_Failed = new(CONFIG_BASE + 43, "Load_Calibration_Config_Failed");
        public static readonly EventId Save_Equip_Movement_Config_Failed = new(CONFIG_BASE + 44, "Save_Equip_Movement_Config_Failed");

        // 轴速度配置事件
        public static readonly EventId Axis_Speed_Config_Started = new(CONFIG_BASE + 50, "Axis_Speed_Config_Started");
        public static readonly EventId All_Axis_Speed_Config_Success = new(CONFIG_BASE + 51, "All_Axis_Speed_Config_Success");
        public static readonly EventId Some_Axis_Speed_Config_Timeout = new(CONFIG_BASE + 52, "Some_Axis_Speed_Config_Timeout");
        public static readonly EventId Task_Creation_Failed = new(CONFIG_BASE + 53, "Task_Creation_Failed");
        public static readonly EventId Xyr_Axis_Speed_Config_Success = new(CONFIG_BASE + 54, "Xyr_Axis_Speed_Config_Success");
        public static readonly EventId Xyr_Axis_Speed_Config_Failed = new(CONFIG_BASE + 55, "Xyr_Axis_Speed_Config_Failed");
        public static readonly EventId Left_Camera_Axis_Speed_Config_Success = new(CONFIG_BASE + 56, "Left_Camera_Axis_Speed_Config_Success");
        public static readonly EventId Left_Camera_Axis_Speed_Config_Failed = new(CONFIG_BASE + 57, "Left_Camera_Axis_Speed_Config_Failed");
        public static readonly EventId Right_Camera_Axis_Speed_Config_Success = new(CONFIG_BASE + 58, "Right_Camera_Axis_Speed_Config_Success");
        public static readonly EventId Right_Camera_Axis_Speed_Config_Failed = new(CONFIG_BASE + 59, "Right_Camera_Axis_Speed_Config_Failed");

        // 配置保存事件
        public static readonly EventId Save_Calibration_Config_Success = new(CONFIG_BASE + 60, "Save_Calibration_Config_Success");
        public static readonly EventId Save_Equip_Movement_Config_Success = new(CONFIG_BASE + 61, "Save_Equip_Movement_Config_Success");
        public static readonly EventId Save_Configuration_Error = new(CONFIG_BASE + 62, "Save_Configuration_Error");

        // 配置加载事件（注意：Load_Calibration_Config_Failed 已在上面定义）
        public static readonly EventId Load_Calibration_Config_Success = new(CONFIG_BASE + 70, "Load_Calibration_Config_Success");
        public static readonly EventId Load_Equip_Movement_Config_Success = new(CONFIG_BASE + 71, "Load_Equip_Movement_Config_Success");
        public static readonly EventId Load_Equip_Movement_Config_Failed = new(CONFIG_BASE + 72, "Load_Equip_Movement_Config_Failed");
        public static readonly EventId Load_Alignment_Config_Success = new(CONFIG_BASE + 73, "Load_Alignment_Config_Success");
        public static readonly EventId Load_Alignment_Config_Failed = new(CONFIG_BASE + 74, "Load_Alignment_Config_Failed");
        public static readonly EventId Save_Alignment_Config_Success = new(CONFIG_BASE + 75, "Save_Alignment_Config_Success");
        public static readonly EventId Save_Alignment_Config_Failed = new(CONFIG_BASE + 76, "Save_Alignment_Config_Failed");

        // 校准相关事件
        public static readonly EventId Calibration_Failed = new(CONFIG_BASE + 80, "Calibration_Failed");
        public static readonly EventId Calibration_Warning = new(CONFIG_BASE + 81, "Calibration_Warning");
        public static readonly EventId Calibrate_Timer_Error = new(CONFIG_BASE + 82, "Calibrate_Timer_Error");

        #endregion

        #region 串口通信事件 (7000-7999)

        public static readonly EventId Serial_Connection_Started = new(SERIAL_BASE + 1, "Serial_Connection_Started");
        public static readonly EventId Serial_Connection_Succeeded = new(SERIAL_BASE + 2, "Serial_Connection_Succeeded");
        public static readonly EventId Serial_Connection_Failed = new(SERIAL_BASE + 3, "Serial_Connection_Failed");
        public static readonly EventId Serial_Connection_Stopped = new(SERIAL_BASE + 4, "Serial_Connection_Stopped");
        public static readonly EventId Serial_Data_Sent = new(SERIAL_BASE + 10, "Serial_Data_Sent");
        public static readonly EventId Serial_Data_Received = new(SERIAL_BASE + 11, "Serial_Data_Received");
        public static readonly EventId Serial_Communication_Error = new(SERIAL_BASE + 20, "Serial_Communication_Error");
        public static readonly EventId Serial_Read_Error = new(SERIAL_BASE + 21, "Serial_Read_Error");
        public static readonly EventId Serial_Write_Error = new(SERIAL_BASE + 22, "Serial_Write_Error");

        // 串口轴操作事件
        public static readonly EventId Serial_Axis_Operation = new(SERIAL_BASE + 30, "Serial_Axis_Operation");
        public static readonly EventId Serial_Position_Changed = new(SERIAL_BASE + 31, "Serial_Position_Changed");
        public static readonly EventId Serial_State_Changed = new(SERIAL_BASE + 32, "Serial_State_Changed");
        public static readonly EventId Serial_Axis_Enabled = new(SERIAL_BASE + 33, "Serial_Axis_Enabled");
        public static readonly EventId Serial_Axis_Disabled = new(SERIAL_BASE + 34, "Serial_Axis_Disabled");
        public static readonly EventId Serial_Axis_Moving = new(SERIAL_BASE + 35, "Serial_Axis_Moving");
        public static readonly EventId Serial_Axis_Stopped = new(SERIAL_BASE + 36, "Serial_Axis_Stopped");
        public static readonly EventId Serial_Resource_Released = new(SERIAL_BASE + 37, "Serial_Resource_Released");

        #endregion

        #region 错误和异常事件 (8000-8999)

        public static readonly EventId Unhandled_Exception = new(ERROR_BASE + 1, "Unhandled_Exception");
        public static readonly EventId Operation_Timeout = new(ERROR_BASE + 10, "Operation_Timeout");
        public static readonly EventId Resource_Not_Found = new(ERROR_BASE + 20, "Resource_Not_Found");
        public static readonly EventId Invalid_Parameter = new(ERROR_BASE + 30, "Invalid_Parameter");
        public static readonly EventId Permission_Denied = new(ERROR_BASE + 40, "Permission_Denied");
        public static readonly EventId Connection_Error = new(ERROR_BASE + 50, "Connection_Error");
        public static readonly EventId Control_Error = new(ERROR_BASE + 60, "Control_Error");

        // 资源管理错误
        public static readonly EventId Resource_Registered = new(ERROR_BASE + 100, "Resource_Registered");
        public static readonly EventId Resource_Released = new(ERROR_BASE + 101, "Resource_Released");
        public static readonly EventId Resource_Registration_Failed = new(ERROR_BASE + 102, "Resource_Registration_Failed");
        public static readonly EventId Resource_Release_Error = new(ERROR_BASE + 103, "Resource_Release_Error");
        public static readonly EventId Resource_Access_Error = new(ERROR_BASE + 104, "Resource_Access_Error");
        public static readonly EventId Resource_Status = new(ERROR_BASE + 105, "Resource_Status");
        public static readonly EventId Deprecated_Api_Usage = new(ERROR_BASE + 106, "Deprecated_Api_Usage");
        public static readonly EventId Thread_Aborted = new(ERROR_BASE + 107, "Thread_Aborted");
        public static readonly EventId Resource_Cleanup_Error = new(ERROR_BASE + 108, "Resource_Cleanup_Error");
        public static readonly EventId Form_Closing_Error = new(ERROR_BASE + 109, "Form_Closing_Error");
        public static readonly EventId Timer_Stop_Error_In_Form_Closing = new(ERROR_BASE + 110, "Timer_Stop_Error_In_Form_Closing");

        // 系统清理和操作错误
        public static readonly EventId Recipe_Execute_Error = new(ERROR_BASE + 115, "Recipe_Execute_Error");
        public static readonly EventId Sys_Pare_Execute_Failed = new(ERROR_BASE + 116, "Sys_Pare_Execute_Failed");
        public static readonly EventId Sys_Pare_Excute_Failed = new(ERROR_BASE + 117, "Sys_Pare_Excute_Failed");

        // 事件注册错误
        public static readonly EventId Main_Window_Event_Registration_Failed = new(ERROR_BASE + 120, "Main_Window_Event_Registration_Failed");
        public static readonly EventId Cancel_Error = new(ERROR_BASE + 121, "Cancel_Error");

        // 定时器相关错误
        public static readonly EventId Update_Timer_Start_Failed = new(ERROR_BASE + 130, "Update_Timer_Start_Failed");
        public static readonly EventId Xyr_Timer_Resumed = new(ERROR_BASE + 131, "Xyr_Timer_Resumed");
        public static readonly EventId Xyr_Timer_Resume_Error = new(ERROR_BASE + 132, "Xyr_Timer_Resume_Error");
        public static readonly EventId Update_Xyr_Position_Error = new(ERROR_BASE + 133, "Update_Xyr_Position_Error");
        public static readonly EventId Update_Tick_Error = new(ERROR_BASE + 134, "Update_Tick_Error");
        public static readonly EventId Timer_Stop_Error = new(ERROR_BASE + 135, "Timer_Stop_Error");

        // 轴位置读取错误（已在AXIS模块中定义，避免重复）

        // 清理和关闭错误
        public static readonly EventId Cleanup_Error_General = new(ERROR_BASE + 200, "Cleanup_Error_General");
        public static readonly EventId MainWindow_ViewModel_Cleanup_Start = new(ERROR_BASE + 201, "MainWindow_ViewModel_Cleanup_Start");
        public static readonly EventId MainWindow_ViewModel_Cleanup_Complete = new(ERROR_BASE + 202, "MainWindow_ViewModel_Cleanup_Complete");
        public static readonly EventId Plc_Cleanup_Complete = new(ERROR_BASE + 203, "Plc_Cleanup_Complete");
        public static readonly EventId Additional_Cleanup_Complete = new(ERROR_BASE + 204, "Additional_Cleanup_Complete");
        public static readonly EventId Cleanup_Process_Complete = new(ERROR_BASE + 205, "Cleanup_Process_Complete");
        public static readonly EventId Background_Tasks_Stopped = new(ERROR_BASE + 206, "Background_Tasks_Stopped");

        #endregion

        #region 性能监控事件 (9000-9999)

        public static readonly EventId Performance_Operation_Started = new(PERFORMANCE_BASE + 1, "Performance_Operation_Started");
        public static readonly EventId Performance_Operation_Completed = new(PERFORMANCE_BASE + 2, "Performance_Operation_Completed");
        public static readonly EventId Performance_Threshold_Exceeded = new(PERFORMANCE_BASE + 10, "Performance_Threshold_Exceeded");
        public static readonly EventId Memory_Usage_High = new(PERFORMANCE_BASE + 20, "Memory_Usage_High");
        public static readonly EventId CPU_Usage_High = new(PERFORMANCE_BASE + 21, "CPU_Usage_High");
        public static readonly EventId Performance_Warning = new(PERFORMANCE_BASE + 22, "Performance_Warning");
        public static readonly EventId Performance_Monitoring_Summary = new(PERFORMANCE_BASE + 23, "Performance_Monitoring_Summary");
        public static readonly EventId Page_Performance_Monitoring = new(PERFORMANCE_BASE + 24, "Page_Performance_Monitoring");

        // 页面性能和懒加载事件
        public static readonly EventId Lazy_Load_Triggered = new(PERFORMANCE_BASE + 30, "Lazy_Load_Triggered");
        public static readonly EventId Lazy_Load_Completed = new(PERFORMANCE_BASE + 31, "Lazy_Load_Completed");
        public static readonly EventId Visible_Changed_Error = new(PERFORMANCE_BASE + 32, "Visible_Changed_Error");

        // 定时器相关事件
        public static readonly EventId Timer_Paused = new(PERFORMANCE_BASE + 40, "Timer_Paused");
        public static readonly EventId Input_Timer_Null = new(PERFORMANCE_BASE + 41, "Input_Timer_Null");
        public static readonly EventId Timer_Disposed_On_Stop = new(PERFORMANCE_BASE + 42, "Timer_Disposed_On_Stop");
        public static readonly EventId Timer_Performance_Warning = new(PERFORMANCE_BASE + 43, "Timer_Performance_Warning");
        public static readonly EventId Timer_Restart_Error = new(PERFORMANCE_BASE + 44, "Timer_Restart_Error");

        // 页面清理事件
        public static readonly EventId FTitle_Page5_Cleanup_Complete = new(PERFORMANCE_BASE + 50, "FTitle_Page5_Cleanup_Complete");

        // 主窗体加载事件
        public static readonly EventId Main_Form_Load_Phase = new(PERFORMANCE_BASE + 60, "Main_Form_Load_Phase");
        public static readonly EventId Main_Form_Load_Completed = new(PERFORMANCE_BASE + 61, "Main_Form_Load_Completed");

        // 系统服务事件
        public static readonly EventId SubSystem_Init_Success = new(PERFORMANCE_BASE + 100, "SubSystem_Init_Success");
        public static readonly EventId SubSystem_Init_Start = new(PERFORMANCE_BASE + 101, "SubSystem_Init_Start");
        public static readonly EventId SubSystem_Reset_Start = new(PERFORMANCE_BASE + 102, "SubSystem_Reset_Start");
        public static readonly EventId Record_Data_Start = new(PERFORMANCE_BASE + 103, "Record_Data_Start");
        public static readonly EventId Ping_InfluxDb_Event = new(PERFORMANCE_BASE + 104, "Ping_InfluxDb_Event");
        public static readonly EventId All_System_Init_Complete = new(PERFORMANCE_BASE + 105, "All_System_Init_Complete");

        // 状态更新服务事件
        public static readonly EventId Status_Updated = new(PERFORMANCE_BASE + 200, "Status_Updated");
        public static readonly EventId Status_Update_Warning = new(PERFORMANCE_BASE + 201, "Status_Update_Warning");
        public static readonly EventId Status_Update_Error = new(PERFORMANCE_BASE + 202, "Status_Update_Error");
        public static readonly EventId Status_Update_Handler_Registered = new(PERFORMANCE_BASE + 203, "Status_Update_Handler_Registered");
        public static readonly EventId Status_Update_Handler_Unregistered = new(PERFORMANCE_BASE + 204, "Status_Update_Handler_Unregistered");
        public static readonly EventId Status_Update_Handler_Error = new(PERFORMANCE_BASE + 205, "Status_Update_Handler_Error");

        #endregion

       

        

        
    }
}