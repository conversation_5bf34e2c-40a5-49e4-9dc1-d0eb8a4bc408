namespace WaferAligner.Communication.Inovance
{
    public enum EnumErrorCode
    {
        ER_READ_WRITE_FAIL = 0,   //读写失败
        ER_READ_WRITE_SUCCEED = 1,  //读写成功
        ER_NOT_CONNECT = 2,  //未连接
        ER_ELEM_TYPE_WRONG = 3,  //元件类型错误
        ER_ELEM_ADDR_OVER = 4,  //元件地址溢出
        ER_ELEM_COUNT_OVER = 5,  //元件个数超限
        ER_COMM_EXCEPT = 6,  //通讯异常
        ER_SYMBOLNOTFOUND = 7,
        None = 10
    };
}
