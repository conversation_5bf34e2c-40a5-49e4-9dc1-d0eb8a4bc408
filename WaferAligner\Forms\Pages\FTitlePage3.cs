// using AlignerUI; // 已迁移到WaferAligner.Models
using WaferAligner.Communication.Inovance.Abstractions;
using WaferAligner.Services.Abstractions;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using WaferAligner.Communication.Inovance;
using WaferAligner.Communication.Inovance.Client;
using Sunny.UI.Win32;
using System.ComponentModel;
using System.Linq.Expressions;
using System.Security.Cryptography;
using System.Text.RegularExpressions;
using System.Xml.Linq;
using WaferAligner;
using WaferAligner.EventIds;
using WaferAligner.CustomClass;
using WaferAligner.Services.Extensions;
using WaferAligner.Common;
using WaferAligner.Services;
using WaferAligner.Interfaces;
using WaferAligner.Infrastructure.Common;
using System.Text;
using AlignerUI; // 添加用于StringBuilder

namespace Sunny.UI.Demo
{
    public partial class FTitlePage3 : BasePage
    {
        private IAxisEventService _axisEventService;
        private IPlcVariableService _plcVariableService;
        private IUIUpdateService _uiUpdateService;
        private IMainWindowViewModel _mainWindowViewModel;
        private CancellationTokenSource _cts;  // 主取消令牌源
        private volatile bool _isClosing = false;  // 添加关闭标志，使用volatile确保线程安全
        private IAxisViewModelFactory _axisFactory;
        private ICylinderService _cylinderService;
        private IPlcConnectionManager _plcConnectionManager;
        private readonly IStatusUpdateService _statusUpdateService;

        // 性能监控相关字段
        private Dictionary<string, long> _perfTimings = new Dictionary<string, long>();
        private System.Diagnostics.Stopwatch _perfTotalStopwatch = new System.Diagnostics.Stopwatch();
        private System.Diagnostics.Stopwatch _perfStepStopwatch = new System.Diagnostics.Stopwatch();
        private bool _perfEnabled = false; // 默认关闭性能监控，避免影响UI响应性
        
        // 添加已初始化标签页记录集合
        private HashSet<string> _initializedTabs = new HashSet<string>();
        
        #region  Load
        public FTitlePage3(IStatusUpdateService statusUpdateService)
        {
            _statusUpdateService = statusUpdateService ?? throw new ArgumentNullException(nameof(statusUpdateService));
            try
            {
                // 开始性能监控
                StartPerformanceMonitoring("总初始化时间");
                StartStepTiming("构造函数");

                InitializeComponent();

                RecordTiming("InitializeComponent");

                // 使用批量服务获取方法，一次获取所有需要的服务
                var failedServices = GetServices<IAxisEventService, IPlcVariableService, IUIUpdateService,
                    IMainWindowViewModel, IAxisViewModelFactory, ICylinderService, IPlcConnectionManager>(
                    out _axisEventService,
                    out _plcVariableService,
                    out _uiUpdateService,
                    out _mainWindowViewModel,
                    out _axisFactory,
                    out _cylinderService,
                    out _plcConnectionManager
                );
                
                // 检查是否有获取失败的服务
                if (failedServices.Count > 0)
                {
                    var serviceNames = string.Join(", ", failedServices.Select(t => t.Name));
                    Logger?.LogWarning($"以下服务获取失败: {serviceNames}", WaferAligner.EventIds.EventIds.Service_Unavailable);
                }

                // 创建主取消令牌源
                _cts = CreateCancellationTokenSource("MainCTS");

                RecordTiming("服务获取");

                LoadInitialConfigsAsync();
                RecordTiming("配置加载");

                // 移动SysPareExcute到这里，延长延迟时间，确保UI完全加载和渲染后再执行
                _ = Task.Delay(1000).ContinueWith(_ =>
                {
                    try
                    {
                        if (!_isClosing && !this.IsDisposed)
                        {
                            // 正确调用返回Task的方法
                            return SysPareExcute();
                        }
                        return Task.CompletedTask;
                    }
                    catch (Exception ex)
                    {
                        Logger?.LogError(ex, "延迟执行SysPareExcute失败", WaferAligner.EventIds.EventIds.Sys_Pare_Excute_Failed);
                        return Task.CompletedTask;
                    }
                }).Unwrap();

                RecordTiming("启动配置加载");
                EndStepTiming();

                Logger?.LogInformation("服务和配置初始化完成");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"页面初始化失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 异步加载初始化配置文件
        /// </summary>
        private async Task LoadInitialConfigsAsync()
        {
            try
            {
                StartStepTiming("加载配置文件");
                
                // 加载标定文件
                string path = Application.StartupPath + "CalPara\\CalPara.json";
                bool fileExists = File.Exists(path);
                if (!fileExists)
                {
                    await SafeInvokeAsync(() => {
                        UpdateStatus("标定文件不存在！");
                        MessageBox.Show("标定文件不存在!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                        Application.Exit();
                    });
                    return;
                }

                RecordTiming("标定文件检查");

                bool res = await Task.Run(() => ReadFromCalJSON(path));
                RecordTiming("标定文件读取");
                
                if (res)
                {
                    await SafeInvokeAsync(() => {
                        UpdateStatus("标定文件打开成功");
                    });
                    Logger.LogInformation($"加载标定参数文件成功", EventIds.Load_Calibration_Config_Success);
                }
                else
                {
                    await SafeInvokeAsync(() => {
                        UpdateStatus("标定文件打开失败");
                        MessageBox.Show("标定文件打开失败!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    });
                    Logger.LogError($"加载标定参数文件发生错误", EventIds.Load_Calibration_Config_Failed);
                }

                // 加载系统文件
                path = Application.StartupPath + "SysPara\\ConfPara_Page3.json";
                fileExists = File.Exists(path);
                if (!fileExists)
                {
                    await SafeInvokeAsync(() => {
                        UpdateStatus("标定文件不存在！");
                        MessageBox.Show("运动界面的系统文件不存在!\r\n请退出软件", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                        Application.Exit();
                    });
                    return;
                }

                RecordTiming("系统文件检查");

                res = await Task.Run(() => ReadFromSysJSON(path));
                RecordTiming("系统文件读取");
                
                if (res)
                {
                    await SafeInvokeAsync(() => {
                        UpdateStatus("运动界面的系统文件打开成功");
                    });
                    Logger.LogInformation($"加载运动界面的系统文件成功", EventIds.Load_Equip_Movement_Config_Success);
                }
                else
                {
                    await SafeInvokeAsync(() => {
                        UpdateStatus("运动界面的系统文件打开失败");
                        MessageBox.Show("加载运动界面的系统文件失败!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    });
                    Logger.LogError($"加载运动界面的系统文件失败", EventIds.Load_Equip_Movement_Config_Failed);
                }
                
                EndStepTiming();
            }
            catch (Exception ex)
            {
                Logger?.LogError($"加载配置文件时发生错误: {ex.Message}", EventIds.Configuration_Error);
            }
        }

        // 使用新的TimerWrapper替换原来的System.Timers.Timer
        private TimerWrapper _UpdateTimer = null;
        private TimerWrapper _CalibrateTimer = null;
        private TimerWrapper _InputTimer = null;  // 替换tmrInput
        private TimerWrapper _CalTimer = null;    // 替换timer1

        // 定义timer1字段以保持代码兼容性
        private System.Windows.Forms.Timer timer1 = null;

        // 定义tmrInput字段以保持代码兼容性
        private System.Windows.Forms.Timer tmrInput = null;

        protected override async Task OnInitializeAsync()
        {
            // 将timer1和tmrInput设置为null，使用_CalTimer和_InputTimer替代
            timer1 = null;
            tmrInput = null;

            try
            {
                StartStepTiming("OnInitializeAsync");
                
                // 检查用户是否有权限访问运动参数配置页面
                if (!UserContext.CanAccessMotionPage())
                {
                    Logger?.LogWarning($"用户 {UserContext.CurrentUser?.Username ?? "未知"} ({UserContext.GetRoleDisplayName()}) 尝试访问运动参数页面但没有权限",
                        EventIds.Unhandled_Exception);

                    // 显示权限不足的消息
                    ShowWarning($"您没有权限访问运动参数配置页面。\n\n当前角色：{UserContext.GetRoleDisplayName()}\n只有管理员和工程师可以配置参数。");

                    // 禁用当前页面
                    this.Enabled = false;
                    return;
                }

                RecordTiming("权限检查");

                Logger?.LogInformation($"用户 {UserContext.CurrentUser?.Username ?? "未知"} 成功访问运动参数页面",
                    EventIds.Page_Initialize_Completed);
                
                // 添加标签页选择事件
                uiTabControlMenu1.SelectedIndexChanged += UiTabControlMenu1_SelectedIndexChanged;

                uiTabControlMenu1.SelectedIndex = 0;
                
                // 记录默认标签页已初始化
                _initializedTabs.Add("tPX");

                // 修改：Z轴归零偏移设置改为后台执行，不阻塞UI线程
                if (_axisEventService == null)
                {
                    Logger?.LogError("轴事件服务未初始化，无法设置Z轴归零偏移。");
                }
                else if (TxtZOffset == null)
                {
                    Logger?.LogError("TxtZOffset控件未初始化，无法设置Z轴归零偏移。");
                }
                else
                {
                    double offset;
                    if (!double.TryParse(TxtZOffset.Text, out offset))
                    {
                        Logger?.LogError($"TxtZOffset.Text不是有效数字: {TxtZOffset.Text}");
                    }
                    else
                    {
                        _ = _axisEventService.SetZAxisHomeOffsetAsync(offset)
                            .ContinueWith(t => {
                                if (t.Exception != null)
                                    Logger?.LogError($"Z轴归零偏移设置失败: {t.Exception.Message}");
                                else
                                    Logger?.LogDebug("Z轴归零偏移设置成功");
                            }, TaskScheduler.Default);
                    }
                }

                RecordTiming("Z轴归零偏移设置");

                // 修改：系统参数配置也在后台执行
                if (_mainWindowViewModel == null)
                {
                    Logger?.LogError("主窗口视图模型(_mainWindowViewModel)未初始化，无法设置系统参数。");
                }
                else if (TxtCameraOffset == null)
                {
                    Logger?.LogError("TxtCameraOffset控件未初始化，无法设置系统参数。");
                }
                else if (TxtBottomPhotoOffset == null)
                {
                    Logger?.LogError("TxtBottomPhotoOffset控件未初始化，无法设置系统参数。");
                }
                else if (TxtTopGap == null)
                {
                    Logger?.LogError("TxtTopGap控件未初始化，无法设置系统参数。");
                }
                else if (TxtBottomGap == null)
                {
                    Logger?.LogError("TxtBottomGap控件未初始化，无法设置系统参数。");
                }
                else
                {
                    double cameraOffset, bottomPhotoOffset, topGap, bottomGap;
                    if (!double.TryParse(TxtCameraOffset.Text, out cameraOffset))
                    {
                        Logger?.LogError($"TxtCameraOffset.Text不是有效数字: {TxtCameraOffset.Text}");
                    }
                    else if (!double.TryParse(TxtBottomPhotoOffset.Text, out bottomPhotoOffset))
                    {
                        Logger?.LogError($"TxtBottomPhotoOffset.Text不是有效数字: {TxtBottomPhotoOffset.Text}");
                    }
                    else if (!double.TryParse(TxtTopGap.Text, out topGap))
                    {
                        Logger?.LogError($"TxtTopGap.Text不是有效数字: {TxtTopGap.Text}");
                    }
                    else if (!double.TryParse(TxtBottomGap.Text, out bottomGap))
                    {
                        Logger?.LogError($"TxtBottomGap.Text不是有效数字: {TxtBottomGap.Text}");
                    }
                    else
                    {
                        _ = _mainWindowViewModel.SystemParameterExecuteAsync(
                            cameraOffset,
                            bottomPhotoOffset,
                            topGap,
                            bottomGap
                        ).ContinueWith(t => {
                            if (t.Exception != null)
                                Logger?.LogError($"系统参数配置失败: {t.Exception.Message}");
                            else
                                Logger?.LogDebug("系统参数配置成功");
                        }, TaskScheduler.Default);
                    }
                }

                RecordTiming("系统参数配置");

                // ? 检查PLC连接状态（使用兼容性服务）
                object plcInstance = null;
                if (_axisEventService == null)
                {
                    Logger?.LogError("_axisEventService未初始化，无法获取PLC实例。", EventIds.Plc_Connection_Failed);
                }
                else
                {
                    plcInstance = _axisEventService.GetMainPLCInstance();
                }
                if (plcInstance == null)
                {
                    Logger?.LogWarning("PLC实例为空，界面功能可能受限", EventIds.Plc_Connection_Failed);
                    UpdateStatus("PLC未初始化");
                }
                else
                {
                    Logger?.LogInformation("PLC连接正常，开始启动Timer", EventIds.Plc_Connection_Succeeded);
                }

                RecordTiming("PLC连接检查");

                // 使用CreateTimerWithAsyncHandler方法，支持异步方法作为处理器
                _InputTimer = CreateTimerWithAsyncHandler("InputTimer", 350, TmrInput_Tick);
                _CalTimer = CreateTimerWithAsyncHandler("CalTimer", 100, Timer1_Tick);
                _UpdateTimer = CreateTimerWithAsyncHandler("UpdateTimer", 200, _Update_Tick);
                _CalibrateTimer = CreateTimerWithAsyncHandler("CalibrateTimer", 2000, Calibrate_Tick);

                RecordTiming("Timer初始化");

                try
                {
                    if (!_isClosing)
                    {
                        // 修改：延迟启动定时器，给UI渲染预留时间
                        _ = Task.Delay(500).ContinueWith(_ =>
                        {
                            SafeInvoke(() => {
                                // 启动更新定时器
                                if (_UpdateTimer != null && !_UpdateTimer.Enabled)
                                    _UpdateTimer.Start();

                                // 启动输入定时器
                                if (_InputTimer != null && !_InputTimer.Enabled)
                                    _InputTimer.Start();
                            });
                        });
                    }
                }
                catch (Exception ex)
                {
                    Logger.LogError(ex, "启动定时器失败", EventIds.Update_Timer_Start_Failed);
                }

                RecordTiming("Timer启动");

                // 注册其他Timer到资源管理器
                if (_CalTimer != null)
                    RegisterResource("CalTimer", _CalTimer); // 使用_CalTimer替代timer1

                if (_InputTimer != null)
                    RegisterResource("InputTimer", _InputTimer); // 使用_InputTimer替代tmrInput

                // 已迁移到Task-based异步模式，不再使用BackgroundWorker
                // 注册取消令牌源清理操作
                ResourceManager?.RegisterCustomCleanup(() =>
                {
                    _calPosCts?.Cancel();
                    _calPosCts?.Dispose();
                    return Task.CompletedTask;
                });

                // 添加界面可见性变化事件处理
                this.VisibleChanged += FTitlePage3_VisibleChanged;

                RecordTiming("资源注册");

                await base.OnInitializeAsync();
                
                RecordTiming("基类初始化");
                // 修改：将SysPareExcute移到这里，在所有UI初始化后执行
                // 使用Task.Delay确保UI完全加载后再执行
                _ = Task.Delay(100).ContinueWith(_ =>
                {
                    try
                    {
                        var sw = System.Diagnostics.Stopwatch.StartNew();
                        SysPareExcute();
                        sw.Stop();
                        if (_perfEnabled)
                        {
                            _perfTimings["SysPareExcute"] = sw.ElapsedMilliseconds;
                        }
                    }
                    catch (Exception ex)
                    {
                        Logger?.LogError(ex, "延迟执行SysPareExcute失败", EventIds.Configuration_Error);
                    }
                }, TaskScheduler.Default);

                RecordTiming("SysPareExcute启动");

                // 服务注入：只初始化XYR轴控件，其他控件采用延迟加载模式
                Logger?.LogInformation("开始初始化XYR轴控件", EventIds.Control_Initialization_Started);
                
                try
                {
                    步进电机调试X.InitServices(Logger, _axisEventService, _plcVariableService, _plcConnectionManager, _axisFactory, _cylinderService);
                    步进电机调试Y.InitServices(Logger, _axisEventService, _plcVariableService, _plcConnectionManager, _axisFactory, _cylinderService);
                    步进电机调试R.InitServices(Logger, _axisEventService, _plcVariableService, _plcConnectionManager, _axisFactory, _cylinderService);
                    
                    Logger?.LogInformation("XYR轴控件初始化完成", EventIds.Control_Initialization_Completed);
                }
                catch (Exception ex)
                {
                    Logger?.LogError(ex, "XYR轴控件初始化失败", EventIds.Control_Initialization_Failed);
                }
                
                RecordTiming("轴控件服务注入");
                EndStepTiming();
                
                // 结束性能监控并显示结果
                EndPerformanceMonitoring();
            }
            catch (Exception ex)
            {
                Logger?.LogError(ex, "FTitlePage3初始化时发生错误", EventIds.Configuration_Error);
            }
        }

        // 性能监控方法
        private void StartPerformanceMonitoring(string totalName)
        {
            if (!_perfEnabled) return;
            _perfTimings.Clear();
            _perfTotalStopwatch.Restart();
            _perfTimings[totalName] = 0; // 占位，最后会更新
        }

        private void StartStepTiming(string stepName)
        {
            if (!_perfEnabled) return;
            _perfStepStopwatch.Restart();
            _perfTimings[stepName + "_开始"] = 0; // 记录步骤开始
        }

        private void RecordTiming(string name)
        {
            if (!_perfEnabled) return;
            _perfTimings[name] = _perfStepStopwatch.ElapsedMilliseconds;
        }

        private void EndStepTiming()
        {
            if (!_perfEnabled) return;
            _perfStepStopwatch.Stop();
        }

        private void EndPerformanceMonitoring()
        {
            if (!_perfEnabled) return;
            
            _perfTotalStopwatch.Stop();
            
            // 记录总时间
            foreach (var key in _perfTimings.Keys.ToList())
            {
                if (key.EndsWith("总初始化时间"))
                {
                    _perfTimings[key] = _perfTotalStopwatch.ElapsedMilliseconds;
                }
            }
            
            // 使用Task.Delay，确保UI完全加载后再显示性能数据
            Task.Delay(500).ContinueWith(_ => 
            {
                try
                {
                    SafeInvoke(() => 
                    {
                        ShowPerformanceResults();
                    });
                }
                catch (Exception ex)
                {
                    Logger?.LogError(ex, "显示性能监控结果时发生错误");
                }
            }, TaskScheduler.Default);
        }

        private void ShowPerformanceResults()
        {
            if (!_perfEnabled) return;
            
            var sb = new StringBuilder();
            sb.AppendLine("FTitlePage3页面加载性能分析:");
            sb.AppendLine("----------------------------");
            
            // 先显示总时间
            foreach (var key in _perfTimings.Keys)
            {
                if (key.Contains("总初始化时间"))
                {
                    sb.AppendLine($"{key}: {_perfTimings[key]}ms");
                    break;
                }
            }
            
            sb.AppendLine("----------------------------");
            sb.AppendLine("各步骤耗时:");
            
            // 按时间排序显示各步骤耗时
            var sortedTimings = _perfTimings
                .Where(kvp => !kvp.Key.Contains("总初始化时间") && !kvp.Key.EndsWith("_开始"))
                .OrderByDescending(kvp => kvp.Value)
                .ToList();

            foreach (var timing in sortedTimings)
            {
                sb.AppendLine($"{timing.Key}: {timing.Value}ms");
            }
            
            // 改为记录到日志而不是显示弹窗
            Logger.LogInformation(sb.ToString(), EventIds.Page_Performance_Monitoring);
        }

        private void FTitlePage3_VisibleChanged(object sender, EventArgs e)
        {
            try
            {
                // 检查是否正在关闭
                if (_isClosing)
                {
                    Logger?.LogDebug("窗体正在关闭，跳过可见性处理", EventIds.Form_Closing_Skip_Visibility);
                    return;
                }

                if (this.Visible)
                {
                    // 界面变为可见时，恢复Timer
                    if (_InputTimer != null && !_InputTimer.Enabled && !_isClosing)
                    {
                        _InputTimer.Start();
                        Logger?.LogDebug("界面变为可见，恢复Timer", EventIds.Timer_Resumed);
                    }
                    if (_UpdateTimer != null && !_UpdateTimer.Enabled && !_isClosing)
                    {
                        try
                        {
                            _UpdateTimer.Start();
                            Logger?.LogDebug("界面变为可见，恢复XYR更新Timer", EventIds.Xyr_Timer_Resumed);
                        }
                        catch (Exception ex)
                        {
                            // 记录异常但继续执行
                            Logger?.LogDebug($"恢复XYR更新Timer失败: {ex.Message}", EventIds.Xyr_Timer_Resume_Error);
                        }
                    }
                }
                else
                {
                    // 界面变为不可见时，暂停Timer以节省资源
                    if (_InputTimer != null && _InputTimer.Enabled)
                    {
                        _InputTimer.Stop();
                        Logger?.LogDebug("界面变为不可见，暂停Timer", EventIds.Timer_Paused);
                    }
                    if (_UpdateTimer != null && _UpdateTimer.Enabled)
                    {
                        _UpdateTimer.Stop();
                        Logger?.LogDebug("界面变为不可见，暂停XYR更新Timer", EventIds.Xyr_Timer_Paused);
                    }
                }
            }
            catch (Exception ex)
            {
                Logger?.LogError($"处理界面可见性变化时发生错误: {ex.Message}", EventIds.Visible_Changed_Error);
            }
        }
        // 添加日志频率控制
        private static int _debugLogCounter = 0;
        private static bool _isDevelopmentMode = WaferAligner.Common.DevelopmentModeHelper.IsDevelopmentMode();

        private async void _Update_Tick(object sender, System.Timers.ElapsedEventArgs e)
        {
            // 检查关闭标志
            if (_isClosing)
            {
                // ?? 开发模式下静默跳过
                if (!_isDevelopmentMode)
                    Logger?.LogDebug("窗体正在关闭，跳过Update Timer执行", EventIds.Form_Closing_In_Update_Timer);
                return;
            }

            try
            {
                // 开发模式下跳过XYR位置更新，避免触发PLC操作
                if (_isDevelopmentMode)
                {
                    return;
                }

                // 直接执行异步操作，不需要Task.Run包装
                // 检查窗体状态和取消令牌
                if (this.IsDisposed || !this.IsHandleCreated || _cts == null || _cts.Token.IsCancellationRequested)
                {
                    return;
                }

                try
                {
                    // 视觉软件一直发送XYR的当前位置
                    // ? 使用迁移助手批量获取XYR位置并设置
                    var (x, y, r) = await _axisEventService.GetXYRPositionsAsync();
                    await _axisEventService.SetXYRPositionAsync(
                        Convert.ToSingle(x / AxisConstants.AXIS_XY_MULTIPLE_CONVERTION),
                        Convert.ToSingle(y / AxisConstants.AXIS_XY_MULTIPLE_CONVERTION),
                        Convert.ToSingle(r / AxisConstants.AXIS_R_MULTIPLE_CONVERTION)
                    );
                }
                catch (Exception ex)
                {
                    // ?? 保持重要的错误日志
                    Logger.LogError($"更新XYR位置时发生错误: {ex.Message}", EventIds.Update_Xyr_Position_Error);
                }
            }
            catch (OperationCanceledException)
            {
                // 任务被取消，静默退出，不记录日志
                return;
            }
            catch (Exception ex)
            {
                // ?? 保持重要的错误日志
                Logger.LogError($"_Update_Tick执行发生错误: {ex.Message}", EventIds.Update_Tick_Error);
            }
        }

        public Dictionary<String, Action<object>> VariableChangeActions = new();
        private void Notify(object? sender, InvoanceVariableChangedEventArgs e)
        {

        }
        private async void TmrInput_Tick(object sender, System.Timers.ElapsedEventArgs e)
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            // 检查关闭标志
            if (_isClosing)
            {
                // ?? 开发模式下静默跳过
                if (!_isDevelopmentMode)
                    Logger?.LogDebug("窗体正在关闭，跳过Timer执行", EventIds.Form_Closing_In_Timer);
                return;
            }

            // 添加null检查，防止空引用异常
            if (_InputTimer == null)
            {
                // ?? 保持重要的警告日志
                Logger?.LogWarning("_InputTimer为null，跳过本次执行", EventIds.Input_Timer_Null);
                return;
            }

            // 检查窗体状态
            if (this.IsDisposed || this.Disposing || !this.IsHandleCreated)
            {
                // ?? 开发模式下静默跳过
                if (!_isDevelopmentMode)
                    Logger?.LogDebug("窗体已释放或正在释放，跳过Timer执行", EventIds.Form_Disposed_In_Timer);
                return;
            }

            try
            {
                _InputTimer.Stop();
            }
            catch (ObjectDisposedException)
            {
                // ?? 开发模式下静默处理
                if (!_isDevelopmentMode)
                    Logger?.LogDebug("Timer在Stop时已被释放", EventIds.Timer_Disposed_On_Stop);
                return;
            }
            
            // 创建取消令牌，设置超时时间为250ms
            using var timeoutCts = new CancellationTokenSource(250);
            
            try
            {
                // 开发模式下跳过PLC读取操作，避免触发连接检查和重连
                if (_isDevelopmentMode)
                {
                    // 开发模式：使用模拟数据更新UI，避免PLC操作
                    await UpdateUIWithSimulatedData();
                    return;
                }

                // 直接执行PLC读取操作，不需要Task.Run包装
                // 检查窗体状态，避免在窗体销毁后执行操作
                if (this.IsDisposed || !this.IsHandleCreated)
                {
                    return;
                }

                // 定义要读取的PLC变量名数组
                var variableNames = new[]
                {
                    $"{AxisConstants.AXIS_GVL}.UpperWaferAdSorptionSensor",
                    $"{AxisConstants.AXIS_GVL}.LowerChuckAdSorptionSensor",
                    $"{AxisConstants.AXIS_GVL}.LowerChuckAdSorptionSensor1",
                    $"{AxisConstants.AXIS_GVL}.LowerWaferAdSorptionSensor",
                    $"{AxisConstants.AXIS_GVL}.UpperChuckLeftLockSensor",
                    $"{AxisConstants.AXIS_GVL}.UpperChuckRightLockSensor",
                    $"{AxisConstants.AXIS_GVL}.UpperChuckLeftUnLockSensor",
                    $"{AxisConstants.AXIS_GVL}.UpperChuckRightUnLockSensor"
                };

                // 创建读取任务数组
                var readTasks = new Task<bool>[variableNames.Length];
                
                // 初始化结果数组
                var results = new object[variableNames.Length];
                
                // 启动所有读取任务
                for (int i = 0; i < variableNames.Length; i++)
                {
                    // 使用本地变量避免闭包问题
                    int index = i;
                    string varName = variableNames[i];
                    
                    // 创建读取任务，添加错误处理和超时保护
                    readTasks[i] = Task.Run(async () => {
                        try
                        {
                            // 使用自定义超时令牌
                            return await _plcVariableService.ReadVariableSafelyAsync<bool>(varName, false);
                        }
                        catch (Exception ex)
                        {
                            Logger?.LogDebug($"读取变量 {varName} 失败: {ex.Message}", EventIds.Plc_Variable_Read_Failed);
                            return false; // 读取失败返回默认值
                        }
                    });
                }

                // 等待所有任务完成或超时
                try
                {
                    // 使用Task.WhenAll和Task.WhenAny的组合实现超时控制
                    var allReadTask = Task.WhenAll(readTasks);
                    var completedTask = await Task.WhenAny(allReadTask, Task.Delay(200, timeoutCts.Token));
                    
                    if (completedTask == allReadTask)
                    {
                        // 所有任务成功完成
                        var boolResults = await allReadTask;
                        for (int i = 0; i < boolResults.Length; i++)
                        {
                            results[i] = boolResults[i];
                        }
                    }
                    else
                    {
                        // 发生超时，收集已完成的任务结果
                        Logger?.LogWarning("PLC读取操作部分超时，使用已完成的结果", EventIds.Plc_Read_Timeout);
                        
                        for (int i = 0; i < readTasks.Length; i++)
                        {
                            if (readTasks[i].IsCompleted && !readTasks[i].IsFaulted)
                            {
                                results[i] = readTasks[i].Result;
                            }
                            else
                            {
                                results[i] = false; // 未完成或出错的任务使用默认值
                            }
                        }
                    }
                }
                catch (OperationCanceledException)
                {
                    Logger?.LogWarning("PLC读取操作被取消", EventIds.Plc_Read_Cancelled);
                    // 继续使用已有的results数组，可能包含部分结果
                }

                // 在UI线程上更新界面
                SafeInvoke(() =>
                {
                    UpdateUIWithPLCResults(results);
                });
            }
            catch (Exception ex)
            {
                // ?? 保持重要的错误日志
                Logger.LogError($"PLC读取操作发生错误: {ex.Message}", EventIds.Plc_Variable_Read_Failed);
            }
            finally
            {
                try
                {
                    // 只有在性能问题时才记录警告
                    if (_InputTimer != null && !_InputTimer.IsDisposed)
                    {
                        if (stopwatch.ElapsedMilliseconds > _InputTimer.Interval * 0.8)
                        {
                            // ?? 保持性能警告日志
                            Logger?.LogWarning($"PLC读取Timer执行时间过长: {stopwatch.ElapsedMilliseconds}ms，间隔: {_InputTimer.Interval}ms", EventIds.Timer_Performance_Warning);
                            
                            // 如果执行时间超过间隔的80%，临时增加定时器间隔
                            if (_InputTimer.Interval < 500)
                            {
                                double newInterval = Math.Min(500, _InputTimer.Interval * 1.5);
                                _InputTimer.Interval = newInterval;
                                Logger?.LogInformation($"临时调整PLC读取Timer间隔为: {newInterval}ms", EventIds.Timer_Interval_Adjusted);
                            }
                        }
                        else if (stopwatch.ElapsedMilliseconds < _InputTimer.Interval * 0.3 && _InputTimer.Interval > 300)
                        {
                            // 如果执行时间很短，可以适当减小间隔，但不低于300ms
                            double newInterval = Math.Max(300, _InputTimer.Interval * 0.9);
                            _InputTimer.Interval = newInterval;
                        }
                    }

                    // 安全的Timer重启
                    SafeRestartTimer();
                }
                catch (ObjectDisposedException)
                {
                    // Timer已被释放，静默处理，不记录日志
                }
                catch (Exception ex)
                {
                    // ?? 保持重要的错误日志
                    Logger?.LogError(ex, $"PLC Timer重启时发生错误: {ex.Message}", EventIds.Plc_Timer_Restart_Error);
                }
            }
        }

        private void SafeRestartTimer()
        {
            try
            {
                // 增加Timer空检查和关闭检查
                if (_isClosing || this.IsDisposed || this.Disposing)
                    return;

                if (_UpdateTimer != null && !_UpdateTimer.Enabled)
                {
                    _UpdateTimer.Start();
                }

                if (_InputTimer != null && !_InputTimer.Enabled)
                {
                    _InputTimer.Start();
                }
            }
            catch (Exception ex)
            {
                Logger?.LogError(ex, $"重启Timer时发生错误: {ex.Message}", EventIds.Timer_Restart_Error);
            }
        }

        private async Task<object> ReadPLCVariableSafely(string variableName, Type expectedType)
        {
            try
            {
                // 使用迁移助手安全读取PLC变量，而非直接访问PLC实例
                var result = await _plcVariableService.ReadVariableSafelyAsync<object>(variableName, null);
                return result;
            }
            catch (Exception ex)
            {
                Logger?.LogWarning($"读取PLC变量 {variableName} 失败: {ex.Message}",
                                  EventIds.Plc_Variable_Read_Failed);
                return null;
            }
        }

        /// <summary>
        /// 开发模式下使用模拟数据更新UI，避免PLC操作
        /// </summary>
        private async Task UpdateUIWithSimulatedData()
        {
            try
            {
                // 开发模式：直接设置UI为未连接状态，不使用模拟PLC数据
                SafeInvoke(() =>
                {
                    // 设置所有状态为未连接状态（灰色）
                    LightTopWafer.State = UILightState.Off;
                    LalTopWaferState.Text = "设备未连接";

                    LightTrayWaferOuter.State = UILightState.Off;
                    LalTrayWaferOuterState.Text = "设备未连接";

                    LightTrayWaferInner.State = UILightState.Off;
                    LalTrayWaferInnerState.Text = "设备未连接";

                    LightTray.State = UILightState.Off;
                    LalTrayState.Text = "设备未连接";

                    LightChuckLock.State = UILightState.Off;
                    LalChuckLockState.Text = "设备未连接";

                    LightHorizontalAdjustLock.State = UILightState.Off;
                    LalHorizontalAdjustLockState.Text = "设备未连接";
                });

                // 模拟一些处理时间，避免过于频繁的更新
                await Task.Delay(10);
            }
            catch (Exception ex)
            {
                Logger?.LogError(ex, "开发模式模拟数据更新失败", EventIds.Plc_Variable_Read_Failed);
            }
        }

        private void UpdateUIWithPLCResults(object[] results)
        {
            try
            {
                // 安全的类型转换方法
                bool SafeBoolConvert(object value, bool defaultValue = false)
                {
                    if (value == null) return defaultValue;
                    if (value is bool boolValue) return boolValue;
                    if (value is int intValue) return intValue != 0;
                    if (value is string stringValue) return !string.IsNullOrEmpty(stringValue) && stringValue.ToLower() != "false" && stringValue != "0";
                    return defaultValue;
                }

                // 检查results数组是否有效
                if (results == null || results.Length < 8)
                {
                    Logger.LogWarning($"PLC结果数组无效: results={results?.Length ?? 0}", EventIds.Plc_Invalid_Results_Array);
                    return;
                }

                // 上晶圆吸附状态
                bool retInPutTopWafer = SafeBoolConvert(results[0]);
                if (_axisEventService.GetCylinderState("TOPWAFER") == (int)CylinderStatus.Open)
                {
                    LightTopWafer.State = retInPutTopWafer ? UILightState.On : UILightState.Off;
                    LalTopWaferState.Text = retInPutTopWafer ? "上晶圆吸附成功" : "上晶圆吸附失败";
                }
                else
                {
                    LightTopWafer.State = UILightState.Off;
                    LalTopWaferState.Text = "上晶圆释放";
                }

                // 托盘晶圆外吸附状态
                bool retTrayWaferOuter = SafeBoolConvert(results[1]);
                if (_axisEventService.GetCylinderState("TRAYWAFERUTER") == (int)CylinderStatus.Open)
                {
                    BtnTrayWaferOuter.Text = "托盘晶圆外吸附";
                    LightTrayWaferOuter.State = retTrayWaferOuter ? UILightState.On : UILightState.Off;
                    LalTrayWaferOuterState.Text = retTrayWaferOuter ? "托盘晶圆外吸附成功" : "托盘晶圆外吸附失败";
                }
                else
                {
                    BtnTrayWaferOuter.Text = "托盘晶圆外释放";
                    LightTrayWaferOuter.State = UILightState.Off;
                    LalTrayWaferOuterState.Text = "托盘晶圆外释放";
                }

                // 托盘晶圆内吸附状态
                bool retTrayWaferInner = SafeBoolConvert(results[2]);
                if (_axisEventService.GetCylinderState("TRAYWAFERINNER") == (int)CylinderStatus.Open)
                {
                    BtnTrayWaferInner.Text = "托盘晶圆内吸附";
                    LightTrayWaferInner.State = retTrayWaferInner ? UILightState.On : UILightState.Off;
                    LalTrayWaferInnerState.Text = retTrayWaferInner ? "托盘晶圆内吸附成功" : "托盘晶圆内吸附失败";
                }
                else
                {
                    BtnTrayWaferInner.Text = "托盘晶圆内释放";
                    LightTrayWaferInner.State = UILightState.Off;
                    LalTrayWaferInnerState.Text = "托盘晶圆内释放";
                }

                // 托盘吸附状态
                bool retTray = SafeBoolConvert(results[3]);
                if (_axisEventService.GetCylinderState("TRAY") == (int)CylinderStatus.Open)
                {
                    BtnTray.Text = "托盘吸附";
                    LightTray.State = retTray ? UILightState.On : UILightState.Off;
                    LalTrayState.Text = retTray ? "托盘吸附成功" : "托盘吸附失败";
                }
                else
                {
                    BtnTray.Text = "托盘释放";
                    LightTray.State = UILightState.Off;
                    LalTrayState.Text = "托盘释放";
                }

                // 卡盘锁状态
                bool retChuckLeft = SafeBoolConvert(results[4]);
                bool retInPutChuckRight = SafeBoolConvert(results[5]);
                bool retChuckLeftUnLock = SafeBoolConvert(results[6]);
                bool retInPutChuckRightUnLock = SafeBoolConvert(results[7]);

                if (_axisEventService.GetCylinderState("CHUCKLOCK") == (int)CylinderStatus.Open)
                {
                    BtnChuckLock.Text = "卡盘锁紧";
                    LightChuckLock.State = (retChuckLeft && retInPutChuckRight) ? UILightState.On : UILightState.Off;
                    LalChuckLockState.Text = (retChuckLeft && retInPutChuckRight) ? "卡盘锁紧成功" : "卡盘锁紧失败";
                }
                else
                {
                    BtnChuckLock.Text = "卡盘锁开";
                    bool leftUnLock = retChuckLeftUnLock;
                    bool rightUnLock = retInPutChuckRightUnLock;
                    LightChuckLock.State = (leftUnLock && rightUnLock) ? UILightState.On : UILightState.Off;
                    LalChuckLockState.Text = (leftUnLock && rightUnLock) ? "卡盘锁开成功" : "卡盘锁开失败";
                }

                // 调平锁状态
                if (_axisEventService.GetCylinderState("HORIZONTALADJUST") == (int)CylinderStatus.Close)
                {
                    LalHorizontalAdjustLockState.Text = "调平锁关";
                    BtnHorizontalAdjustLock.Text = "调平锁关";
                    LightHorizontalAdjustLock.State = UILightState.Off;
                }
                else
                {
                    LalHorizontalAdjustLockState.Text = "调平锁开";
                    BtnHorizontalAdjustLock.Text = "调平锁开";
                    LightHorizontalAdjustLock.State = UILightState.On;
                }
            }
            catch (Exception ex)
            {
                Logger.LogError($"更新UI时发生错误: {ex.Message}", EventIds.UI_Update_Failed);
            }
        }

        #endregion  Load       

        #region  IOButton
        private async void BtnTopWafer_Click(object sender, EventArgs e)
        {
            if (_isClosing) return;
            
            try
            {
                // 禁用按钮防止重复操作
                await SafeInvokeAsync(() => BtnTopWafer.Enabled = false);
                
                // 记录操作日志
                Logger.LogInformation("用户请求控制上晶圆气缸", EventIds.Top_Wafer_Control_Requested);
                
                // 重置和注册取消令牌
                _cts?.Cancel();
                _cts?.Dispose();
                _cts = CreateCancellationTokenSource("TopWaferControl");
                
                // 获取当前状态并确定目标状态
                var currentState = _axisEventService.GetCylinderState("TOPWAFER");
                int targetState = currentState == (int)CylinderStatus.Close ? (int)CylinderStatus.Open : (int)CylinderStatus.Close;
                
                // 使用迁移助手进行气缸控制
                await _axisEventService.CylinderControlAsync("TOPWAFER", targetState);
                
                // 更新UI显示
                await SafeInvokeAsync(() =>
                {
                    BtnTopWafer.Text = targetState == (int)CylinderStatus.Open ? "上晶圆吸附" : "上晶圆释放";
                    LalTopWaferState.Text = targetState == (int)CylinderStatus.Open ? "上晶圆吸附" : "上晶圆释放";
                });
                
                Logger.LogInformation($"上晶圆气缸控制成功，状态: {(targetState == (int)CylinderStatus.Open ? "吸附" : "释放")}", EventIds.Top_Wafer_Control_Success);
            }
            catch (OperationCanceledException)
            {
                // 操作被取消，记录日志
                Logger.LogWarning("上晶圆气缸控制操作被取消", EventIds.Operation_Cancelled);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "上晶圆气缸控制失败", EventIds.Top_Wafer_Control_Failed);
                await SafeInvokeAsync(() => ShowError("上晶圆气缸控制失败，请检查设备状态"));
            }
            finally
            {
                // 确保UI恢复正常状态
                if (!_isClosing && !this.IsDisposed)
                {
                    await SafeInvokeAsync(() => BtnTopWafer.Enabled = true);
                }
            }
        }
        private async void BtnTrayWaferOuter_Click(object sender, EventArgs e)
        {
            BtnTrayWaferOuter.Enabled = false;
            try
            {
                // ? 使用迁移助手进行气缸状态检查和控制
                var currentState = _axisEventService.GetCylinderState("TRAYWAFERUTER");
                int targetState = currentState == (int)CylinderStatus.Close ? (int)CylinderStatus.Open : (int)CylinderStatus.Close;

                await _axisEventService.CylinderControlAsync("TRAYWAFERUTER", targetState);

                // 更新UI显示
                _uiUpdateService.SafeUpdateUI(this, () =>
                {
                    BtnTrayWaferOuter.Text = targetState == (int)CylinderStatus.Open ? "托盘晶圆外吸附" : "托盘晶圆外释放";
                    LalTrayWaferOuterState.Text = targetState == (int)CylinderStatus.Open ? "托盘晶圆外吸附" : "托盘晶圆外释放";
                });
            }
            catch (Exception ex)
            {
                Logger.LogError($"托盘晶圆外气缸控制失败: {ex.Message}", EventIds.Tray_Wafer_Outer_Control_Failed);
                ShowError("托盘晶圆外气缸控制失败，请检查设备状态");
            }
            finally
            {
                BtnTrayWaferOuter.Enabled = true;
            }
        }
        private async void BtnTrayWaferInner_Click(object sender, EventArgs e)
        {
            BtnTrayWaferInner.Enabled = false;
            try
            {
                // ? 使用迁移助手进行气缸状态检查和控制
                var currentState = _axisEventService.GetCylinderState("TRAYWAFERINNER");
                int targetState = currentState == (int)CylinderStatus.Close ? (int)CylinderStatus.Open : (int)CylinderStatus.Close;

                await _axisEventService.CylinderControlAsync("TRAYWAFERINNER", targetState);

                // 更新UI显示
                _uiUpdateService.SafeUpdateUI(this, () =>
                {
                    BtnTrayWaferInner.Text = targetState == (int)CylinderStatus.Open ? "托盘晶圆内吸附" : "托盘晶圆内释放";
                    LalTrayWaferInnerState.Text = targetState == (int)CylinderStatus.Open ? "托盘晶圆内吸附" : "托盘晶圆内释放";
                });
            }
            catch (Exception ex)
            {
                Logger.LogError($"托盘晶圆内气缸控制失败: {ex.Message}", EventIds.Tray_Wafer_Inner_Control_Failed);
                ShowError("托盘晶圆内气缸控制失败，请检查设备状态");
            }
            finally
            {
                BtnTrayWaferInner.Enabled = true;
            }
        }
        private async void BtnTray_Click(object sender, EventArgs e)
        {
            BtnTray.Enabled = false;
            try
            {
                // ? 使用迁移助手进行气缸状态检查和控制
                var currentState = _axisEventService.GetCylinderState("TRAY");
                int targetState = currentState == (int)CylinderStatus.Close ? (int)CylinderStatus.Open : (int)CylinderStatus.Close;

                await _axisEventService.CylinderControlAsync("TRAY", targetState);

                // 更新UI显示
                _uiUpdateService.SafeUpdateUI(this, () =>
                {
                    BtnTray.Text = targetState == (int)CylinderStatus.Open ? "托盘吸附" : "托盘吸附释放";
                    LalTrayState.Text = targetState == (int)CylinderStatus.Open ? "托盘吸附" : "托盘吸附释放";
                });
            }
            catch (Exception ex)
            {
                Logger.LogError($"托盘气缸控制失败: {ex.Message}", EventIds.Tray_Control_Failed);
                ShowError("托盘气缸控制失败，请检查设备状态");
            }
            finally
            {
                BtnTray.Enabled = true;
            }
        }
        private async void BtnChuckLock_Click(object sender, EventArgs e)
        {
            BtnChuckLock.Enabled = false;
            try
            {
                // ? 使用迁移助手进行气缸状态检查和控制
                var currentState = _axisEventService.GetCylinderState("CHUCKLOCK");
                int targetState = currentState == (int)CylinderStatus.Close ? (int)CylinderStatus.Open : (int)CylinderStatus.Close;

                await _axisEventService.CylinderControlAsync("CHUCKLOCK", targetState);

                // 更新UI显示
                _uiUpdateService.SafeUpdateUI(this, () =>
                {
                    if (targetState == (int)CylinderStatus.Open)
                    {
                        LalChuckLockState.Text = "卡盘锁紧";
                        BtnChuckLock.Text = "卡盘锁紧";
                        LightChuckLock.State = UILightState.On;
                    }
                    else
                    {
                        LalChuckLockState.Text = "卡盘锁开";
                        BtnChuckLock.Text = "卡盘锁开";
                        LightChuckLock.State = UILightState.Off;
                    }
                });
            }
            catch (Exception ex)
            {
                Logger.LogError($"卡盘锁气缸控制失败: {ex.Message}", EventIds.Chuck_Lock_Control_Failed);
                ShowError("卡盘锁气缸控制失败，请检查设备状态");
            }
            finally
            {
                BtnChuckLock.Enabled = true;
            }
        }
        private async void BtnHorizontalAdjustLock_Click(object sender, EventArgs e)
        {
            BtnHorizontalAdjustLock.Enabled = false;
            try
            {
                // ? 使用迁移助手进行气缸状态检查和控制
                var currentState = _axisEventService.GetCylinderState("HORIZONTALADJUST");
                int targetState = currentState == (int)CylinderStatus.Close ? (int)CylinderStatus.Open : (int)CylinderStatus.Close;

                await _axisEventService.CylinderControlAsync("HORIZONTALADJUST", targetState);

                // 更新UI显示
                _uiUpdateService.SafeUpdateUI(this, () =>
                {
                    if (targetState == (int)CylinderStatus.Open)
                    {
                        LalHorizontalAdjustLockState.Text = "调平锁开";
                        BtnHorizontalAdjustLock.Text = "调平锁开";
                        LightHorizontalAdjustLock.State = UILightState.On;
                    }
                    else
                    {
                        LalHorizontalAdjustLockState.Text = "调平锁关";
                        BtnHorizontalAdjustLock.Text = "调平锁关";
                        LightHorizontalAdjustLock.State = UILightState.Off;
                    }
                });
            }
            catch (Exception ex)
            {
                Logger.LogError($"调平锁气缸控制失败: {ex.Message}", EventIds.Horizontal_Adjust_Control_Failed);
                ShowError("调平锁气缸控制失败，请检查设备状态");
            }
            finally
            {
                BtnHorizontalAdjustLock.Enabled = true;
            }
        }

        #endregion  IOButton

        #region  TEXT
        private async void Txt_KeyUp(object sender, KeyEventArgs e)
        {
            UITextBox temp = (UITextBox)sender;
            string TempText = temp.Text.Trim();
            double value;
            if (TempText == "+" || TempText == "-" || TempText == "")
            {
                value = 0;
            }
            else if (TempText.EndsWith("."))
            {
                return;
            }
            else
            {
                value = Convert.ToDouble(TempText);
            }
            switch (temp.Name)
            {
                case "TxtZOffset":
                    {
                        // ? 使用迁移助手进行Z轴归零偏移设置
                        await _axisEventService.SetZAxisHomeOffsetAsync(value);
                    }
                    break;
                case "TxtTopGap":
                    {
                        // ? 使用迁移助手设置上间隙参数
                        await _axisEventService.SetTopGapParameterAsync(value);
                    }
                    break;
                case "TxtBottomGap":
                    {
                        // ? 使用迁移助手设置下间隙参数
                        await _axisEventService.SetBottomGapParameterAsync(value);
                    }
                    break;
                case "TxtBottomPhotoOffset":
                    {
                        // ? 使用迁移助手设置下拍照参数
                        await _axisEventService.SetBottomPhotoParameterAsync(value);
                    }
                    break;
                case "TxtCameraOffset":
                    {
                        // ? 使用迁移助手设置相机偏移参数
                        await _axisEventService.SetCameraOffsetParameterAsync(value);
                    }
                    break;
            }


        }
        #endregion  TEXT

        #region 平台标定
        int Calibrate_state = -1;
        private async void BtnCal_Click(object sender, EventArgs e)
        {
            if (_isClosing) return;
            
            try
            {
                // 禁用按钮防止重复操作
                await SafeInvokeAsync(() => BtnManage(false));
                
                UpdateStatus("开始平台标定");
                // UpdateStatus("平台标定中...");
                
                // 重置和注册取消令牌
                _cts?.Cancel();
                _cts?.Dispose();
                _cts = CreateCancellationTokenSource("Calibration");
                
                // 使用迁移助手进行XYR标定
                await _axisEventService.CalibrateXYRAsync();
                
                // 使用_CalTimer替代timer1
                _CalTimer.Interval = 100;
                Calibrate_state = 0;
                _CalTimer.Start();
            }
            catch (OperationCanceledException)
            {
                // 操作被取消，记录日志
                Logger.LogWarning("平台标定操作被取消", EventIds.Operation_Cancelled);
                // UpdateStatus("平台标定已取消");
                
                // 重置状态
                Calibrate_state = -1;
                await SafeInvokeAsync(() => BtnManage(true));
            }
            catch (Exception ex)
            {
                // 处理异常
                Logger.LogError(ex, "平台标定失败", EventIds.Calibration_Failed);
                
                // 重置状态
                Calibrate_state = -1;
                
                await SafeInvokeAsync(() => 
                {
                    MessageBox.Show($"平台标定失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    // UpdateStatus($"平台标定失败: {ex.Message}");
                    BtnManage(true);
                });
            }
        }

        private async void Timer1_Tick(object sender, System.Timers.ElapsedEventArgs e)
        {
            // 停止计时器，防止重入
            _CalTimer.Stop();

            try
            {
                // 开发模式下跳过标定相关操作，避免触发PLC操作
                if (_isDevelopmentMode)
                {
                    return;
                }

                // 如果正在关闭或已取消，则退出
                if (_isClosing || _cts.IsCancellationRequested)
                {
                    return;
                }
                
                // 创建取消令牌，支持超时取消
                using var localCts = CancellationTokenSource.CreateLinkedTokenSource(_cts.Token);
                localCts.CancelAfter(TimeSpan.FromSeconds(30)); // 30秒超时
                
                switch (Calibrate_state)
                {
                    case 0://获取20 21 22的状态直到111
                        {
                            // 使用迁移助手接收消息
                            string State = await _axisEventService.ReceiveMsgAsync();
                            
                            // 检查是否有效
                            if (string.IsNullOrEmpty(State))
                            {
                                Logger.LogWarning("标定过程中接收到空消息", EventIds.Calibration_Warning);
                                if (!_isClosing && !this.IsDisposed && this.IsHandleCreated)
                                    _CalTimer?.Start();
                                return;
                            }
                            
                            string[] States = State.Split(',');
                            if (Convert.ToInt32(States[0]) == 1 && Convert.ToInt32(States[1]) == 9 && Convert.ToInt32(States[2]) == 1)
                            {
                                Calibrate_state = 1;
                                if (!_isClosing && !this.IsDisposed && this.IsHandleCreated)
                                    _CalTimer?.Start();
                            }
                            else if (Convert.ToInt32(States[0]) == 1 && Convert.ToInt32(States[1]) == 4 && Convert.ToInt32(States[2]) == 1)
                            {
                                //标定完成
                                UpdateStatus("平台标定完成");
                                // UpdateStatus("平台标定完成！");
                                
                                await SafeInvokeAsync(() => { BtnManage(true); });
                                return;
                            }
                            else if (Convert.ToInt32(States[0]) == 1 && Convert.ToInt32(States[1]) == 4 && Convert.ToInt32(States[2]) == 2)
                            {
                                // UpdateStatus("平台标定失败！");
                                Logger.LogError("平台标定失败", EventIds.Platform_Calibrate_Failed);
                                
                                await SafeInvokeAsync(() => { BtnManage(true); });
                                return;
                            }
                            else
                            {
                                if (!_isClosing && !this.IsDisposed && this.IsHandleCreated)
                                    _CalTimer?.Start();
                            }
                        }
                        break;
                    case 1:
                        {
                            Calibrate_state = -1;
                            //获取26、28、30的位置并发出指令
                            // 使用迁移助手获取XYR位置
                            string Pos = await _axisEventService.GetXYRPosAsync();
                            
                            // 检查是否有效
                            if (string.IsNullOrEmpty(Pos))
                            {
                                Logger.LogWarning("标定过程中获取XYR位置失败", EventIds.Calibration_Warning);
                                if (!_isClosing && !this.IsDisposed && this.IsHandleCreated)
                                    _CalTimer?.Start();
                                return;
                            }
                            
                            string[] Poses = Pos.Split(',');

                            // 使用迁移助手执行XYR轴批量定位
                            await _axisEventService.MoveXYRAxesToPositionAsync(
                                Convert.ToSingle(Poses[0]),
                                Convert.ToSingle(Poses[1]),
                                Convert.ToSingle(Poses[2])
                            );

                            Calibrate_state = 2;
                            if (!_isClosing && !this.IsDisposed && this.IsHandleCreated)
                                _CalTimer?.Start();
                        }
                        break;
                    case 2://等待各轴运动完成 
                        {
                            Calibrate_state = -1;
                            // 使用迁移助手获取XYR轴运动状态
                            var (retX, retY, retR) = _axisEventService.GetXYRRunStates();
                            if (retX != 1 && retY != 1 && retR != 1)
                            {
                                //运动完成
                                // 使用迁移助手发送消息
                                await _axisEventService.SendMsgAsync(1, 9, 1, 0, 0);
                                Calibrate_state = 0;
                                if (!_isClosing && !this.IsDisposed && this.IsHandleCreated)
                                    _CalTimer?.Start();
                            }
                            else
                            {
                                //运动中
                                Calibrate_state = 2;
                                if (!_isClosing && !this.IsDisposed && this.IsHandleCreated)
                                    _CalTimer?.Start();
                            }
                        }
                        break;
                }
            }
            catch (OperationCanceledException)
            {
                // 操作被取消，记录日志
                Logger.LogWarning("标定操作被取消", EventIds.Operation_Cancelled);
                Calibrate_state = -1;
                
                await SafeInvokeAsync(() => { BtnManage(true); });
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, $"标定Timer处理发生错误: {ex.Message}", EventIds.Calibrate_Timer_Error);
                Calibrate_state = -1;
                
                await SafeInvokeAsync(() => { 
                    BtnManage(true);
                    // UpdateStatus($"标定错误: {ex.Message}");
                });
            }
        }

        private async void Calibrate_Tick(object sender, System.Timers.ElapsedEventArgs e)
        {
            // 停止计时器，防止重入
            if (_CalibrateTimer != null)
                _CalibrateTimer.Stop();

            try
            {
                // 开发模式下跳过标定相关操作，避免触发PLC操作
                if (_isDevelopmentMode)
                {
                    return;
                }

                // 如果正在关闭或已取消，则退出
                if (_isClosing || _cts.IsCancellationRequested)
                {
                    return;
                }
                
                // 创建取消令牌，支持超时取消
                using var localCts = CancellationTokenSource.CreateLinkedTokenSource(_cts.Token);
                localCts.CancelAfter(TimeSpan.FromSeconds(30)); // 30秒超时
                
                switch (Calibrate_state)
                {
                    case 0://获取20 21 22的状态直到111
                        {
                            Calibrate_state = -1;
                            // 使用迁移助手接收消息
                            string State = await _axisEventService.ReceiveMsgAsync();
                            
                            // 检查是否有效
                            if (string.IsNullOrEmpty(State))
                            {
                                Logger.LogWarning("标定过程中接收到空消息", EventIds.Calibration_Warning);
                                if (!_isClosing && !this.IsDisposed && this.IsHandleCreated && _CalibrateTimer != null)
                                    _CalibrateTimer.Start();
                                return;
                            }
                            
                            string[] States = State.Split(',');
                            if (Convert.ToInt32(States[0]) == 1 && Convert.ToInt32(States[1]) == 9 && Convert.ToInt32(States[2]) == 1)
                            {
                                Calibrate_state = 1;
                                if (!_isClosing && !this.IsDisposed && this.IsHandleCreated && _CalibrateTimer != null)
                                    _CalibrateTimer.Start();
                            }
                            else if (Convert.ToInt32(States[0]) == 1 && Convert.ToInt32(States[1]) == 4 && Convert.ToInt32(States[2]) == 1)
                            {
                                //标定完成
                                UpdateStatus("平台标定完成");
                                // UpdateStatus("平台标定完成！");
                                
                                await SafeInvokeAsync(() => { BtnManage(true); });
                                return;
                            }
                            else if (Convert.ToInt32(States[0]) == 1 && Convert.ToInt32(States[1]) == 4 && Convert.ToInt32(States[2]) == 2)
                            {
                                // UpdateStatus("平台标定失败！");
                                Logger.LogError("平台标定失败", EventIds.Platform_Calibrate_Failed);
                                
                                await SafeInvokeAsync(() => { BtnManage(true); });
                                return;
                            }
                            else
                            {
                                if (!_isClosing && !this.IsDisposed && this.IsHandleCreated && _CalibrateTimer != null)
                                    _CalibrateTimer.Start();
                            }
                        }
                        break;
                    case 1:
                        {
                            Calibrate_state = -1;
                            //获取26、28、30的位置并发出指令
                            // 使用迁移助手获取XYR位置
                            string Pos = await _axisEventService.GetXYRPosAsync();
                            
                            // 检查是否有效
                            if (string.IsNullOrEmpty(Pos))
                            {
                                Logger.LogWarning("标定过程中获取XYR位置失败", EventIds.Calibration_Warning);
                                if (!_isClosing && !this.IsDisposed && this.IsHandleCreated && _CalibrateTimer != null)
                                    _CalibrateTimer.Start();
                                return;
                            }
                            
                            string[] Poses = Pos.Split(',');

                            // 使用迁移助手执行XYR轴批量定位
                            await _axisEventService.MoveXYRAxesToPositionAsync(
                                Convert.ToSingle(Poses[0]),
                                Convert.ToSingle(Poses[1]),
                                Convert.ToSingle(Poses[2])
                            );

                            Calibrate_state = 2;
                            if (!_isClosing && !this.IsDisposed && this.IsHandleCreated && _CalibrateTimer != null)
                                _CalibrateTimer.Start();
                        }
                        break;
                    case 2://等待各轴运动完成 
                        {
                            Calibrate_state = -1;
                            // 使用迁移助手获取XYR轴运动状态
                            var (retX, retY, retR) = _axisEventService.GetXYRRunStates();
                            if (retX != 1 && retY != 1 && retR != 1)
                            {
                                //运动完成
                                // 使用迁移助手发送消息
                                await _axisEventService.SendMsgAsync(1, 9, 1, 0, 0);
                                Calibrate_state = 0;
                                if (!_isClosing && !this.IsDisposed && this.IsHandleCreated && _CalibrateTimer != null)
                                    _CalibrateTimer.Start();
                            }
                            else
                            {
                                //运动中
                                Calibrate_state = 2;
                                if (!_isClosing && !this.IsDisposed && this.IsHandleCreated && _CalibrateTimer != null)
                                    _CalibrateTimer.Start();
                            }
                        }
                        break;
                }
            }
            catch (OperationCanceledException)
            {
                // 操作被取消，记录日志
                Logger.LogWarning("标定操作被取消", EventIds.Operation_Cancelled);
                Calibrate_state = -1;
                
                await SafeInvokeAsync(() => { BtnManage(true); });
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, $"标定Timer处理发生错误: {ex.Message}", EventIds.Calibrate_Timer_Error);
                Calibrate_state = -1;
                
                await SafeInvokeAsync(() => { 
                    BtnManage(true);
                    // UpdateStatus($"标定错误: {ex.Message}");
                });
            }
        }



        // 替换BackgroundWorker为CancellationTokenSource
        private CancellationTokenSource _calPosCts = null;

        private async void BtnCalPos_Click(object sender, EventArgs e)
        {
            try
            {
                BtnManage(false);
                BtnSave.Enabled = false;
                BtnOpen.Enabled = false;

                if (TxtLX.Text == "" || TxtLY.Text == "" || TxtLZ.Text == "" || TxtRX.Text == "" || TxtRY.Text == "" || TxtRZ.Text == "")
                {
                    MessageBox.Show("标定运动前,请输入全部轴位置！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    BtnManage(true);
                    return;
                }

                // 创建新的取消令牌源
                _calPosCts = CreateCancellationTokenSource("CalPosCTS");

                UpdateStatus("标定位运动中...");

                // 使用迁移助手执行相机轴批量定位
                await _axisEventService.MoveCameraAxesToPositionAsync(
                    Convert.ToDouble(TxtLX.Text),  // LX
                    Convert.ToDouble(TxtLY.Text),  // LY
                    Convert.ToDouble(TxtLZ.Text),  // LZ
                    Convert.ToDouble(TxtRX.Text),  // RX
                    Convert.ToDouble(TxtRY.Text),  // RY
                    Convert.ToDouble(TxtRZ.Text)   // RZ
                );

                // 异步等待轴到达目标位置
                try
                {
                    // 使用异步任务替代BackgroundWorker
                    await Task.Run(async () =>
                    {
                        // 检查是否取消，如果取消则抛出异常
                        while (!_axisEventService.CheckCameraAxesArrived())
                        {
                            _calPosCts.Token.ThrowIfCancellationRequested();
                            // 短暂休眠避免占用过多CPU
                            await Task.Delay(50, _calPosCts.Token);
                        }
                    }, _calPosCts.Token);

                    // 任务完成，显示成功消息
                    UpdateStatus("标定运动完成！");
                }
                catch (OperationCanceledException)
                {
                    // 任务被取消
                    MessageBox.Show("您取消了操作!");
                    UpdateStatus("标定位运动取消！");
                }
                catch (Exception ex)
                {
                    // 处理错误
                    UpdateStatus("标定位运动中产生错误:" + ex.Message);
                    Logger.LogError(ex, "标定位运动中产生错误", EventIds.Calibrated_Moving_Failed);
                    MessageBox.Show("标定位运动中产生错误:" + ex.Message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
                finally
                {
                    // 清理取消令牌源
                    _calPosCts?.Dispose();
                    _calPosCts = null;

                    // 恢复按钮状态
                    BtnManage(true);
                    BtnSave.Enabled = true;
                    BtnOpen.Enabled = true;
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "标定位运动执行失败", EventIds.Calibrated_Moving_Failed);
                MessageBox.Show("标定位运动执行失败: " + ex.Message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);

                // 确保UI恢复正常状态
                BtnManage(true);
                BtnSave.Enabled = true;
                BtnOpen.Enabled = true;
            }
        }

        private async void BtnFiveStop_Click(object sender, EventArgs e)
        {
            if (_isClosing) return;
            
            try
            {
                // 禁用按钮防止重复操作
                await SafeInvokeAsync(() => BtnManage(false));
                
                UpdateStatus("用户请求停止所有轴运动");
                // UpdateStatus("正在停止所有轴...");

                // 取消当前运行的标定任务
                _calPosCts?.Cancel();
                
                // 取消其他可能的异步操作
                _cts?.Cancel();
                _cts?.Dispose();
                _cts = CreateCancellationTokenSource("StopAllAxes");

                // 使用迁移助手停止所有轴运动
                await _axisEventService.StopAllAxesAsync();
                
                UpdateStatus("成功停止所有轴运动");
                // UpdateStatus("已停止所有轴运动");
            }
            catch (OperationCanceledException)
            {
                // 操作被取消，记录日志
                Logger.LogWarning("停止轴运动操作被取消", EventIds.Operation_Cancelled);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "停止轴运动失败", EventIds.Axis_Stop_Error);
                await SafeInvokeAsync(() => 
                {
                    MessageBox.Show("停止轴运动失败: " + ex.Message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    // UpdateStatus($"停止轴运动失败: {ex.Message}");
                });
            }
            finally
            {
                // 确保UI恢复正常状态
                if (!_isClosing && !this.IsDisposed)
                {
                    await SafeInvokeAsync(() => BtnManage(true));
                }
            }
        }

        private void BtnSave_Click(object sender, EventArgs e)
        {
            BtnSave.Enabled = false;
            string path;
            SaveFileDialog savefile = new()
            {
                Filter = "标定文件(*.json)|*.json", //格式写对才能显示对应的后缀名的文件
                InitialDirectory = Environment.CurrentDirectory + @"\CalPara\"
            };
            if (savefile.ShowDialog() == DialogResult.OK)//选择了打开文件 
            {
                Application.DoEvents();
                path = savefile.FileName;
                bool Res = WriteToCalJSON(path);
                if (Res)
                {
                    UpdateStatus("标定文件保存成功!");
                    Logger.LogInformation($"保存标定参数文件成功", EventIds.Save_Calibration_Config_Success);

                }
                else
                {
                    UpdateStatus("标定文件保存失败!");
                    Logger.LogError($"保存标定参数文件失败", EventIds.Save_Calibration_Config_Failed);

                    MessageBox.Show("标定文件保存失败!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                }
            }
            else
            {
                UpdateStatus("没有保存标定文件!");
                MessageBox.Show("没有保存标定文件", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
            }
            BtnSave.Enabled = true;
        }

        private void BtnOpen_Click(object sender, EventArgs e)
        {
            BtnOpen.Enabled = false;
            string path;
            OpenFileDialog openfile = new()
            {
                Filter = "标定文件(*.json)|*.json", //格式写对才能显示对应的后缀名的文件
                InitialDirectory = Environment.CurrentDirectory + @"\CalPara\"
            };
            if (openfile.ShowDialog() == DialogResult.OK)//选择了打开文件 
            {
                Application.DoEvents();

                path = openfile.FileName;
                bool Res = ReadFromCalJSON(path);
                if (Res)
                {
                    UpdateStatus("打开标定文件成功!");
                    Logger.LogInformation($"加载标定参数文件成功", EventIds.Load_Calibration_Config_Success);

                }
                else
                {
                    UpdateStatus("打开标定文件失败!");
                    Logger.LogError($"加载标定参数文件发生错误", EventIds.Load_Calibration_Config_Failed);

                    MessageBox.Show("打开标定文件失败!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                }
            }
            else
            { //选择取消  
                UpdateStatus("没有选择标定文件");
                MessageBox.Show("没有选择标定文件", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
            }
            BtnOpen.Enabled = true;
        }
        private bool ReadFromCalJSON(string FileName)
        {
            try
            {
                JsonOperator oper = new(FileName);
                this.SafeInvoke(() => TxtLX.Text = oper.Select("LX轴标定位置"));
                this.SafeInvoke(() => TxtLY.Text = oper.Select("LY轴标定位置"));
                this.SafeInvoke(() => TxtLZ.Text = oper.Select("LZ轴标定位置"));
                this.SafeInvoke(() => TxtRX.Text = oper.Select("RX轴标定位置"));
                this.SafeInvoke(() => TxtRY.Text = oper.Select("RY轴标定位置"));
                this.SafeInvoke(() => TxtRZ.Text = oper.Select("RZ轴标定位置"));
                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message.ToString());
                Application.Exit();
                return false;
            }
        }

        public bool WriteToCalJSON(string FileName)
        {
            try
            {
                if (File.Exists(FileName))
                {
                    File.Delete(FileName);
                }
                JsonOperator oper = new(FileName);
                oper.Create("LX轴标定位置", TxtLX.Text);
                oper.Create("LY轴标定位置", TxtLY.Text);
                oper.Create("LZ轴标定位置", TxtLZ.Text);
                oper.Create("RX轴标定位置", TxtRX.Text);
                oper.Create("RY轴标定位置", TxtRY.Text);
                oper.Create("RZ轴标定位置", TxtRZ.Text);
                oper.Save();
                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message.ToString());
                return false;
            }
        }

        private bool ReadFromSysJSON(string FileName)
        {
            try
            {
                JsonOperator oper = new(FileName);
                //运动参数
                步进电机调试X.PosMax = Convert.ToDouble(oper.Select("X轴限位max"));
                步进电机调试X.PosMin = Convert.ToDouble(oper.Select("X轴限位min"));
                步进电机调试X.SpeedMax = Convert.ToDouble(oper.Select("X轴速度max"));
                步进电机调试X.SpeedMin = Convert.ToDouble(oper.Select("X轴速度min"));
                步进电机调试X.JogSpeedMax = Convert.ToDouble(oper.Select("X轴Jog速度max"));
                步进电机调试X.JogSpeedMin = Convert.ToDouble(oper.Select("X轴Jog速度min"));
                //步进电机调试X.Pos = Convert.ToDouble(oper.Select(FileName, "X轴目标位置"));
                步进电机调试X.Speed = Convert.ToDouble(oper.Select("X轴目标速度"));
                步进电机调试X.JogSpeed = Convert.ToDouble(oper.Select("X轴Jog速度"));


                步进电机调试Y.PosMax = Convert.ToDouble(oper.Select("Y轴限位max"));
                步进电机调试Y.PosMin = Convert.ToDouble(oper.Select("Y轴限位min"));
                步进电机调试Y.SpeedMax = Convert.ToDouble(oper.Select("Y轴速度max"));
                步进电机调试Y.SpeedMin = Convert.ToDouble(oper.Select("Y轴速度min"));
                步进电机调试Y.JogSpeedMax = Convert.ToDouble(oper.Select("Y轴Jog速度max"));
                步进电机调试Y.JogSpeedMin = Convert.ToDouble(oper.Select("Y轴Jog速度min"));
                //步进电机调试Y.Pos = Convert.ToDouble(oper.Select(FileName, "Y轴目标位置"));
                步进电机调试Y.Speed = Convert.ToDouble(oper.Select("Y轴目标速度"));
                步进电机调试Y.JogSpeed = Convert.ToDouble(oper.Select("Y轴Jog速度"));


                步进电机调试R.PosMax = Convert.ToDouble(oper.Select("R轴限位max"));
                步进电机调试R.PosMin = Convert.ToDouble(oper.Select("R轴限位min"));
                步进电机调试R.SpeedMax = Convert.ToDouble(oper.Select("R轴速度max"));
                步进电机调试R.SpeedMin = Convert.ToDouble(oper.Select("R轴速度min"));
                步进电机调试R.JogSpeedMax = Convert.ToDouble(oper.Select("R轴Jog速度max"));
                步进电机调试R.JogSpeedMin = Convert.ToDouble(oper.Select("R轴Jog速度min"));
                //步进电机调试R.Pos = Convert.ToDouble(oper.Select(FileName, "R轴目标位置"));
                步进电机调试R.Speed = Convert.ToDouble(oper.Select("R轴目标速度"));
                步进电机调试R.JogSpeed = Convert.ToDouble(oper.Select("R轴Jog速度"));


                步进电机调试Z.PosMax = Convert.ToDouble(oper.Select("Z轴限位max"));
                步进电机调试Z.PosMin = Convert.ToDouble(oper.Select("Z轴限位min"));
                步进电机调试Z.SpeedMax = Convert.ToDouble(oper.Select("Z轴速度max"));
                步进电机调试Z.SpeedMin = Convert.ToDouble(oper.Select("Z轴速度min"));
                步进电机调试Z.JogSpeedMax = Convert.ToDouble(oper.Select("Z轴Jog速度max"));
                步进电机调试Z.JogSpeedMin = Convert.ToDouble(oper.Select("Z轴Jog速度min"));
                //步进电机调试Z.Pos = Convert.ToDouble(oper.Select(FileName, "Z轴目标位置"));
                步进电机调试Z.Speed = Convert.ToDouble(oper.Select("Z轴目标速度"));
                步进电机调试Z.JogSpeed = Convert.ToDouble(oper.Select("Z轴Jog速度"));


                步进电机调试LX.PosMax = Convert.ToDouble(oper.Select("LX轴限位max"));
                步进电机调试LX.PosMin = Convert.ToDouble(oper.Select("LX轴限位min"));
                步进电机调试LX.SpeedMax = Convert.ToDouble(oper.Select("LX轴速度max"));
                步进电机调试LX.SpeedMin = Convert.ToDouble(oper.Select("LX轴速度min"));
                步进电机调试LX.JogSpeedMax = Convert.ToDouble(oper.Select("LX轴Jog速度max"));
                步进电机调试LX.JogSpeedMin = Convert.ToDouble(oper.Select("LX轴Jog速度min"));
                //步进电机调试LX.Pos = Convert.ToDouble(oper.Select(FileName, "LX轴目标位置"));
                步进电机调试LX.Speed = Convert.ToDouble(oper.Select("LX轴目标速度"));
                步进电机调试LX.JogSpeed = Convert.ToDouble(oper.Select("LX轴Jog速度"));


                步进电机调试LY.PosMax = Convert.ToDouble(oper.Select("LY轴限位max"));
                步进电机调试LY.PosMin = Convert.ToDouble(oper.Select("LY轴限位min"));
                步进电机调试LY.SpeedMax = Convert.ToDouble(oper.Select("LY轴速度max"));
                步进电机调试LY.SpeedMin = Convert.ToDouble(oper.Select("LY轴速度min"));
                步进电机调试LY.JogSpeedMax = Convert.ToDouble(oper.Select("LY轴Jog速度max"));
                步进电机调试LY.JogSpeedMin = Convert.ToDouble(oper.Select("LY轴Jog速度min"));
                //步进电机调试LY.Pos = Convert.ToDouble(oper.Select(FileName, "LY轴目标位置"));
                步进电机调试LY.Speed = Convert.ToDouble(oper.Select("LY轴目标速度"));
                步进电机调试LY.JogSpeed = Convert.ToDouble(oper.Select("LY轴Jog速度"));


                步进电机调试LZ.PosMax = Convert.ToDouble(oper.Select("LZ轴限位max"));
                步进电机调试LZ.PosMin = Convert.ToDouble(oper.Select("LZ轴限位min"));
                步进电机调试LZ.SpeedMax = Convert.ToDouble(oper.Select("LZ轴速度max"));
                步进电机调试LZ.SpeedMin = Convert.ToDouble(oper.Select("LZ轴速度min"));
                步进电机调试LZ.JogSpeedMax = Convert.ToDouble(oper.Select("LZ轴Jog速度max"));
                步进电机调试LZ.JogSpeedMin = Convert.ToDouble(oper.Select("LZ轴Jog速度min"));
                //步进电机调试LZ.Pos = Convert.ToDouble(oper.Select(FileName, "LZ轴目标位置"));
                步进电机调试LZ.Speed = Convert.ToDouble(oper.Select("LZ轴目标速度"));
                步进电机调试LZ.JogSpeed = Convert.ToDouble(oper.Select("LZ轴Jog速度"));


                步进电机调试RX.PosMax = Convert.ToDouble(oper.Select("RX轴限位max"));
                步进电机调试RX.PosMin = Convert.ToDouble(oper.Select("RX轴限位min"));
                步进电机调试RX.SpeedMax = Convert.ToDouble(oper.Select("RX轴速度max"));
                步进电机调试RX.SpeedMin = Convert.ToDouble(oper.Select("RX轴速度min"));
                步进电机调试RX.JogSpeedMax = Convert.ToDouble(oper.Select("RX轴Jog速度max"));
                步进电机调试RX.JogSpeedMin = Convert.ToDouble(oper.Select("RX轴Jog速度min"));
                //步进电机调试RX.Pos = Convert.ToDouble(oper.Select(FileName, "RX轴目标位置"));
                步进电机调试RX.Speed = Convert.ToDouble(oper.Select("RX轴目标速度"));
                步进电机调试RX.JogSpeed = Convert.ToDouble(oper.Select("RX轴Jog速度"));


                步进电机调试RY.PosMax = Convert.ToDouble(oper.Select("RY轴限位max"));
                步进电机调试RY.PosMin = Convert.ToDouble(oper.Select("RY轴限位min"));
                步进电机调试RY.SpeedMax = Convert.ToDouble(oper.Select("RY轴速度max"));
                步进电机调试RY.SpeedMin = Convert.ToDouble(oper.Select("RY轴速度min"));
                步进电机调试RY.JogSpeedMax = Convert.ToDouble(oper.Select("RY轴Jog速度max"));
                步进电机调试RY.JogSpeedMin = Convert.ToDouble(oper.Select("RY轴Jog速度min"));
                //步进电机调试RY.Pos = Convert.ToDouble(oper.Select(FileName, "RY轴目标位置"));
                步进电机调试RY.Speed = Convert.ToDouble(oper.Select("RY轴目标速度"));
                步进电机调试RY.JogSpeed = Convert.ToDouble(oper.Select("RY轴Jog速度"));

                步进电机调试RZ.PosMax = Convert.ToDouble(oper.Select("RZ轴限位max"));
                步进电机调试RZ.PosMin = Convert.ToDouble(oper.Select("RZ轴限位min"));
                步进电机调试RZ.SpeedMax = Convert.ToDouble(oper.Select("RZ轴速度max"));
                步进电机调试RZ.SpeedMin = Convert.ToDouble(oper.Select("RZ轴速度min"));
                步进电机调试RZ.JogSpeedMax = Convert.ToDouble(oper.Select("RZ轴Jog速度max"));
                步进电机调试RZ.JogSpeedMin = Convert.ToDouble(oper.Select("RZ轴Jog速度min"));
                //步进电机调试RZ.Pos = Convert.ToDouble(oper.Select(FileName, "RZ轴目标位置"));
                步进电机调试RZ.Speed = Convert.ToDouble(oper.Select("RZ轴目标速度"));
                步进电机调试RZ.JogSpeed = Convert.ToDouble(oper.Select("RZ轴Jog速度"));

                //标定参数
                this.SafeInvoke(() => TxtLX.Text = oper.Select("LX轴标定位置"));
                this.SafeInvoke(() => TxtLY.Text = oper.Select("LY轴标定位置"));
                this.SafeInvoke(() => TxtLZ.Text = oper.Select("LZ轴标定位置"));
                this.SafeInvoke(() => TxtRX.Text = oper.Select("RX轴标定位置"));
                this.SafeInvoke(() => TxtRY.Text = oper.Select("RY轴标定位置"));
                this.SafeInvoke(() => TxtRZ.Text = oper.Select("RZ轴标定位置"));

                this.SafeInvoke(() => TxtZOffset.Text = oper.Select("Z轴原点偏移位置"));
                this.SafeInvoke(() => TxtCameraOffset.Text = oper.Select("相机偏移量"));
                this.SafeInvoke(() => TxtBottomPhotoOffset.Text = oper.Select("下透明片拍照偏移量"));
                this.SafeInvoke(() => TxtTopGap.Text = oper.Select("上晶圆吸附间隙"));
                this.SafeInvoke(() => TxtBottomGap.Text = oper.Select("下晶圆吸附间隙"));

                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message.ToString());
                Application.Exit();
                return false;
            }
        }

        private async Task SysPareExcute()
        {
            try
            {
                // 修改：使用后台任务执行批量设置所有轴速度，避免阻塞UI
                Logger?.LogInformation("开始设置轴速度参数", EventIds.Axis_Speed_Config_Started);

                // 准备所有要执行的任务
                var tasks = new List<Task>();

                // XYR轴速度配置 - 不使用Task.Run包装，直接使用异步操作
                var xyrTask = SetXYRAxisSpeedsAsync();
                tasks.Add(xyrTask);

                // 左侧相机轴速度配置 - 不使用Task.Run包装，直接使用异步操作
                var leftCameraTask = SetLeftCameraAxisSpeedsAsync();
                tasks.Add(leftCameraTask);

                // 右侧相机轴速度配置 - 不使用Task.Run包装，直接使用异步操作
                var rightCameraTask = SetRightCameraAxisSpeedsAsync();
                tasks.Add(rightCameraTask);

                // 等待所有任务组完成，但设置超时以防止长时间阻塞
                await Task.WhenAny(
                    Task.WhenAll(tasks),
                    Task.Delay(5000) // 5秒超时
                );

                // 检查是否所有任务都已完成
                if (tasks.All(t => t.IsCompleted))
                {
                    Logger?.LogInformation("所有轴速度参数设置已完成", EventIds.All_Axis_Speed_Config_Success);
                }
                else
                {
                    Logger?.LogWarning("部分轴速度参数设置超时，将在后台继续进行", EventIds.Some_Axis_Speed_Config_Timeout);
                }
            }
            catch (Exception ex)
            {
                Logger?.LogError(ex, "创建轴速度设置任务失败", EventIds.Task_Creation_Failed);
            }
        }

        // 拆分为单独的异步方法，便于维护
        private async Task SetXYRAxisSpeedsAsync()
        {
            try
            {
                // XYR轴速度配置
                await _axisEventService.SetAxisSpeedsAsync(
                    (uint)(步进电机调试X.Speed * AxisConstants.AXIS_XY_MULTIPLE_CONVERTION),
                    (uint)(步进电机调试X.JogSpeed * AxisConstants.AXIS_XY_MULTIPLE_CONVERTION),

                    (uint)(步进电机调试Y.Speed * AxisConstants.AXIS_XY_MULTIPLE_CONVERTION),
                    (uint)(步进电机调试Y.JogSpeed * AxisConstants.AXIS_XY_MULTIPLE_CONVERTION),

                    (uint)(步进电机调试R.Speed * AxisConstants.AXIS_R_MULTIPLE_CONVERTION),
                    (uint)(步进电机调试R.JogSpeed * AxisConstants.AXIS_R_MULTIPLE_CONVERTION),

                    (uint)(步进电机调试Z.Speed * AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION),
                    (uint)(步进电机调试Z.JogSpeed * AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION)
                );
                Logger?.LogDebug("XYR轴速度参数设置成功", EventIds.Xyr_Axis_Speed_Config_Success);
            }
            catch (Exception ex)
            {
                Logger?.LogError(ex, "XYR轴速度参数设置失败", EventIds.Xyr_Axis_Speed_Config_Failed);
            }
        }

        // 拆分为单独的异步方法，便于维护
        private async Task SetLeftCameraAxisSpeedsAsync()
        {
            try
            {
                // 左侧相机相关轴处理
                var lxAxis = await _axisEventService.GetCameraAxisViewModelAsync("Left", "X");
                var lyAxis = await _axisEventService.GetCameraAxisViewModelAsync("Left", "Y");
                var lzAxis = await _axisEventService.GetCameraAxisViewModelAsync("Left", "Z");

                await lxAxis.SetRunSpeedAsync(步进电机调试LX.Speed * AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION);
                await lxAxis.SetJogSpeedAsync(步进电机调试LX.JogSpeed * AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION);

                await lyAxis.SetRunSpeedAsync(步进电机调试LY.Speed * AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION);
                await lyAxis.SetJogSpeedAsync(步进电机调试LY.JogSpeed * AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION);

                await lzAxis.SetRunSpeedAsync(步进电机调试LZ.Speed);
                await lzAxis.SetJogSpeedAsync(步进电机调试LZ.JogSpeed);

                Logger?.LogDebug("左侧相机轴速度参数设置成功", EventIds.Left_Camera_Axis_Speed_Config_Success);
            }
            catch (Exception ex)
            {
                Logger?.LogError(ex, "左侧相机轴速度参数设置失败", EventIds.Left_Camera_Axis_Speed_Config_Failed);
            }
        }

        // 拆分为单独的异步方法，便于维护
        private async Task SetRightCameraAxisSpeedsAsync()
        {
            try
            {
                // 右侧相机相关轴处理
                var rxAxis = await _axisEventService.GetCameraAxisViewModelAsync("Right", "X");
                var ryAxis = await _axisEventService.GetCameraAxisViewModelAsync("Right", "Y");
                var rzAxis = await _axisEventService.GetCameraAxisViewModelAsync("Right", "Z");

                await rxAxis.SetRunSpeedAsync(步进电机调试RX.Speed * AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION);
                await rxAxis.SetJogSpeedAsync(步进电机调试RX.JogSpeed * AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION);

                await ryAxis.SetRunSpeedAsync(步进电机调试RY.Speed * AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION);
                await ryAxis.SetJogSpeedAsync(步进电机调试RY.JogSpeed * AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION);

                await rzAxis.SetRunSpeedAsync(步进电机调试RZ.Speed);
                await rzAxis.SetJogSpeedAsync(步进电机调试RZ.JogSpeed);

                Logger?.LogDebug("右侧相机轴速度参数设置成功", EventIds.Right_Camera_Axis_Speed_Config_Success);
            }
            catch (Exception ex)
            {
                Logger?.LogError(ex, "右侧相机轴速度参数设置失败", EventIds.Right_Camera_Axis_Speed_Config_Failed);
            }
        }

        public bool WriteToSysJSON(string FileName)
        {
            try
            {
                if (File.Exists(FileName))
                {
                    File.Delete(FileName);
                }
                JsonOperator oper = new(FileName);

                oper.Create("X轴限位max", Convert.ToString(步进电机调试X.PosMax));
                oper.Create("X轴限位min", Convert.ToString(步进电机调试X.PosMin));
                oper.Create("X轴速度max", Convert.ToString(步进电机调试X.SpeedMax));
                oper.Create("X轴速度min", Convert.ToString(步进电机调试X.SpeedMin));
                oper.Create("X轴Jog速度max", Convert.ToString(步进电机调试X.JogSpeedMax));
                oper.Create("X轴Jog速度min", Convert.ToString(步进电机调试X.JogSpeedMin));
                //oper.Create(FileName, "X轴目标位置", Convert.ToString(步进电机调试X.Pos));
                oper.Create("X轴目标速度", Convert.ToString(步进电机调试X.Speed));
                oper.Create("X轴Jog速度", Convert.ToString(步进电机调试X.JogSpeed));

                oper.Create("Y轴限位max", Convert.ToString(步进电机调试Y.PosMax));
                oper.Create("Y轴限位min", Convert.ToString(步进电机调试Y.PosMin));
                oper.Create("Y轴速度max", Convert.ToString(步进电机调试Y.SpeedMax));
                oper.Create("Y轴速度min", Convert.ToString(步进电机调试Y.SpeedMin));
                oper.Create("Y轴Jog速度max", Convert.ToString(步进电机调试Y.JogSpeedMax));
                oper.Create("Y轴Jog速度min", Convert.ToString(步进电机调试Y.JogSpeedMin));
                //oper.Create(FileName, "Y轴目标位置", Convert.ToString(步进电机调试Y.Pos));
                oper.Create("Y轴目标速度", Convert.ToString(步进电机调试Y.Speed));
                oper.Create("Y轴Jog速度", Convert.ToString(步进电机调试Y.JogSpeed));


                oper.Create("R轴限位max", Convert.ToString(步进电机调试R.PosMax));
                oper.Create("R轴限位min", Convert.ToString(步进电机调试R.PosMin));
                oper.Create("R轴速度max", Convert.ToString(步进电机调试R.SpeedMax));
                oper.Create("R轴速度min", Convert.ToString(步进电机调试R.SpeedMin));
                oper.Create("R轴Jog速度max", Convert.ToString(步进电机调试R.JogSpeedMax));
                oper.Create("R轴Jog速度min", Convert.ToString(步进电机调试R.JogSpeedMin));
                //oper.Create(FileName, "R轴目标位置", Convert.ToString(步进电机调试R.Pos));
                oper.Create("R轴目标速度", Convert.ToString(步进电机调试R.Speed));
                oper.Create("R轴Jog速度", Convert.ToString(步进电机调试R.JogSpeed));

                oper.Create("Z轴限位max", Convert.ToString(步进电机调试Z.PosMax));
                oper.Create("Z轴限位min", Convert.ToString(步进电机调试Z.PosMin));
                oper.Create("Z轴速度max", Convert.ToString(步进电机调试Z.SpeedMax));
                oper.Create("Z轴速度min", Convert.ToString(步进电机调试Z.SpeedMin));
                oper.Create("Z轴Jog速度max", Convert.ToString(步进电机调试Z.JogSpeedMax));
                oper.Create("Z轴Jog速度min", Convert.ToString(步进电机调试Z.JogSpeedMin));
                //oper.Create(FileName, "Z轴目标位置", Convert.ToString(步进电机调试Z.Pos));
                oper.Create("Z轴目标速度", Convert.ToString(步进电机调试Z.Speed));
                oper.Create("Z轴Jog速度", Convert.ToString(步进电机调试Z.JogSpeed));

                oper.Create("LX轴限位max", Convert.ToString(步进电机调试LX.PosMax));
                oper.Create("LX轴限位min", Convert.ToString(步进电机调试LX.PosMin));
                oper.Create("LX轴速度max", Convert.ToString(步进电机调试LX.SpeedMax));
                oper.Create("LX轴速度min", Convert.ToString(步进电机调试LX.SpeedMin));
                oper.Create("LX轴Jog速度max", Convert.ToString(步进电机调试LX.JogSpeedMax));
                oper.Create("LX轴Jog速度min", Convert.ToString(步进电机调试LX.JogSpeedMin));
                //oper.Create(FileName, "LX轴目标位置", Convert.ToString(步进电机调试LX.Pos));
                oper.Create("LX轴目标速度", Convert.ToString(步进电机调试LX.Speed));
                oper.Create("LX轴Jog速度", Convert.ToString(步进电机调试LX.JogSpeed));

                oper.Create("LY轴限位max", Convert.ToString(步进电机调试LY.PosMax));
                oper.Create("LY轴限位min", Convert.ToString(步进电机调试LY.PosMin));
                oper.Create("LY轴速度max", Convert.ToString(步进电机调试LY.SpeedMax));
                oper.Create("LY轴速度min", Convert.ToString(步进电机调试LY.SpeedMin));
                oper.Create("LY轴Jog速度max", Convert.ToString(步进电机调试LY.JogSpeedMax));
                oper.Create("LY轴Jog速度min", Convert.ToString(步进电机调试LY.JogSpeedMin));
                //oper.Create(FileName, "LY轴目标位置", Convert.ToString(步进电机调试LY.Pos));
                oper.Create("LY轴目标速度", Convert.ToString(步进电机调试LY.Speed));
                oper.Create("LY轴Jog速度", Convert.ToString(步进电机调试LY.JogSpeed));


                oper.Create("LZ轴限位max", Convert.ToString(步进电机调试LZ.PosMax));
                oper.Create("LZ轴限位min", Convert.ToString(步进电机调试LZ.PosMin));
                oper.Create("LZ轴速度max", Convert.ToString(步进电机调试LZ.SpeedMax));
                oper.Create("LZ轴速度min", Convert.ToString(步进电机调试LZ.SpeedMin));
                oper.Create("LZ轴Jog速度max", Convert.ToString(步进电机调试LZ.JogSpeedMax));
                oper.Create("LZ轴Jog速度min", Convert.ToString(步进电机调试LZ.JogSpeedMin));
                //oper.Create(FileName, "LZ轴目标位置", Convert.ToString(步进电机调试LZ.Pos));
                oper.Create("LZ轴目标速度", Convert.ToString(步进电机调试LZ.Speed));
                oper.Create("LZ轴Jog速度", Convert.ToString(步进电机调试LZ.JogSpeed));

                oper.Create("RX轴限位max", Convert.ToString(步进电机调试RX.PosMax));
                oper.Create("RX轴限位min", Convert.ToString(步进电机调试RX.PosMin));
                oper.Create("RX轴速度max", Convert.ToString(步进电机调试RX.SpeedMax));
                oper.Create("RX轴速度min", Convert.ToString(步进电机调试RX.SpeedMin));
                oper.Create("RX轴Jog速度max", Convert.ToString(步进电机调试RX.JogSpeedMax));
                oper.Create("RX轴Jog速度min", Convert.ToString(步进电机调试RX.JogSpeedMin));
                //oper.Create(FileName, "RX轴目标位置", Convert.ToString(步进电机调试RX.Pos));
                oper.Create("RX轴目标速度", Convert.ToString(步进电机调试RX.Speed));
                oper.Create("RX轴Jog速度", Convert.ToString(步进电机调试RX.JogSpeed));

                oper.Create("RY轴限位max", Convert.ToString(步进电机调试RY.PosMax));
                oper.Create("RY轴限位min", Convert.ToString(步进电机调试RY.PosMin));
                oper.Create("RY轴速度max", Convert.ToString(步进电机调试RY.SpeedMax));
                oper.Create("RY轴速度min", Convert.ToString(步进电机调试RY.SpeedMin));
                oper.Create("RY轴Jog速度max", Convert.ToString(步进电机调试RY.JogSpeedMax));
                oper.Create("RY轴Jog速度min", Convert.ToString(步进电机调试RY.JogSpeedMin));
                //oper.Create(FileName, "RY轴目标位置", Convert.ToString(步进电机调试RY.Pos));
                oper.Create("RY轴目标速度", Convert.ToString(步进电机调试RY.Speed));
                oper.Create("RY轴Jog速度", Convert.ToString(步进电机调试RY.JogSpeed));

                oper.Create("RZ轴限位max", Convert.ToString(步进电机调试RZ.PosMax));
                oper.Create("RZ轴限位min", Convert.ToString(步进电机调试RZ.PosMin));
                oper.Create("RZ轴速度max", Convert.ToString(步进电机调试RZ.SpeedMax));
                oper.Create("RZ轴速度min", Convert.ToString(步进电机调试RZ.SpeedMin));
                oper.Create("RZ轴Jog速度max", Convert.ToString(步进电机调试RZ.JogSpeedMax));
                oper.Create("RZ轴Jog速度min", Convert.ToString(步进电机调试RZ.JogSpeedMin));
                //oper.Create(FileName, "RZ轴目标位置", Convert.ToString(步进电机调试RZ.Pos));
                oper.Create("RZ轴目标速度", Convert.ToString(步进电机调试RZ.Speed));
                oper.Create("RZ轴Jog速度", Convert.ToString(步进电机调试RZ.JogSpeed));

                oper.Create("LX轴标定位置", TxtLX.Text);
                oper.Create("LY轴标定位置", TxtLY.Text);
                oper.Create("LZ轴标定位置", TxtLZ.Text);
                oper.Create("RX轴标定位置", TxtRX.Text);
                oper.Create("RY轴标定位置", TxtRY.Text);
                oper.Create("RZ轴标定位置", TxtRZ.Text);

                oper.Create("Z轴原点偏移位置", TxtZOffset.Text);
                oper.Create("相机偏移量", TxtCameraOffset.Text);
                oper.Create("下透明片拍照偏移量", TxtBottomPhotoOffset.Text);
                oper.Create("上晶圆吸附间隙", TxtTopGap.Text);
                oper.Create("下晶圆吸附间隙", TxtBottomGap.Text);




                oper.Save();
                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message.ToString());
                return false;
            }
        }

        private void SaveConfigurations()
        {
            try
            {
                // 保存标定文件
                string path = Application.StartupPath + "CalPara\\CalPara.json";
                bool Res = WriteToCalJSON(path);
                if (Res)
                {
                    UpdateStatus("标定文件保存成功");
                    Logger.LogInformation($"保存标定参数文件成功", EventIds.Save_Calibration_Config_Success);
                }
                else
                {
                    UpdateStatus("标定文件保存失败");
                    Logger.LogError($"保存标定参数文件失败", EventIds.Save_Calibration_Config_Failed);
                }

                // 保存系统文件
                path = Application.StartupPath + "SysPara\\ConfPara_Page3.json";
                Res = WriteToSysJSON(path);
                if (Res)
                {
                    UpdateStatus("运动界面的系统文件保存成功");
                    Logger.LogInformation($"运动界面的系统文件保存成功", EventIds.Save_Equip_Movement_Config_Success);
                }
                else
                {
                    UpdateStatus("运动界面的系统文件保存失败");
                    Logger.LogError($"运动界面的系统文件保存失败", EventIds.Save_Equip_Movement_Config_Failed);
                }
            }
            catch (Exception ex)
            {
                Logger.LogError($"保存配置文件时发生错误: {ex.Message}", EventIds.Save_Configuration_Error);
            }
        }

        private void AllAxisControlCleanUp()
        {
            try
            {
                步进电机调试X?.CleanUp();
                步进电机调试Y?.CleanUp();
                步进电机调试R?.CleanUp();
                步进电机调试Z?.CleanUp();  // 添加Z轴清理

                // 取消标定位运动操作（已迁移到Task-based异步模式）
                _calPosCts?.Cancel();

                // 注销所有已注册的轴事件
                if (_axisEventService != null)
                {
                    // 解绑所有可能的轴事件 - 使用Task.Run避免UI冻结
                    Task.Run(async () => {
                        try {
                            // 主轴事件解绑
                            await _axisEventService.UnregisterAxisEventAsync("X", $"{AxisConstants.AXIS_GVL}.XRealDistance");
                            await _axisEventService.UnregisterAxisEventAsync("Y", $"{AxisConstants.AXIS_GVL}.YRealDistance");
                            await _axisEventService.UnregisterAxisEventAsync("R", $"{AxisConstants.AXIS_GVL}.RRealDistance");
                            await _axisEventService.UnregisterAxisEventAsync("Z", $"{AxisConstants.AXIS_GVL}.ZRealDistance");  // 添加Z轴事件解绑
                            
                            // 相机轴事件解绑
                            await _axisEventService.UnregisterAxisEventAsync("LX", $"{AxisConstants.AXIS_GVL}.LXRealDistance");
                            await _axisEventService.UnregisterAxisEventAsync("LY", $"{AxisConstants.AXIS_GVL}.LYRealDistance");
                            await _axisEventService.UnregisterAxisEventAsync("LZ", $"{AxisConstants.AXIS_GVL}.LZRealDistance");
                            await _axisEventService.UnregisterAxisEventAsync("RX", $"{AxisConstants.AXIS_GVL}.RXRealDistance");
                            await _axisEventService.UnregisterAxisEventAsync("RY", $"{AxisConstants.AXIS_GVL}.RYRealDistance");
                            await _axisEventService.UnregisterAxisEventAsync("RZ", $"{AxisConstants.AXIS_GVL}.RZRealDistance");
                            
                            Logger?.LogDebug("所有轴事件已注销", WaferAligner.EventIds.EventIds.Axis_Event_Unregistered);
                        } 
                        catch (Exception innerEx) 
                        {
                            Logger?.LogError(innerEx, "解绑轴事件失败", WaferAligner.EventIds.EventIds.Axis_Event_Unregister_Failed);
                        }
                    });
                }

                // 清理变量更改动作字典
                VariableChangeActions?.Clear();
            }
            catch (Exception ex)
            {
                Logger.LogError($"清理轴控制资源时发生错误: {ex.Message}", EventIds.Axis_Control_Cleanup_Error);
            }
        }

        protected override void OnDispose()
        {
            try
            {
                _isClosing = true;
                
                Logger?.LogInformation("开始释放FTitlePage3资源", WaferAligner.EventIds.EventIds.Resource_Released);
                
                // 停止所有定时器
                _UpdateTimer?.Stop();
                _CalibrateTimer?.Stop();
                _InputTimer?.Stop();
                _CalTimer?.Stop();
                
                // 取消所有操作
                _cts?.Cancel();
                _calPosCts?.Cancel();
                
                // 安全取消标签页事件注册
                try
                {
                    if (uiTabControlMenu1 != null)
                    {
                        uiTabControlMenu1.SelectedIndexChanged -= UiTabControlMenu1_SelectedIndexChanged;
                    }
                }
                catch (Exception ex)
                {
                    Logger?.LogWarning($"注销标签页事件时发生错误: {ex.Message}", WaferAligner.EventIds.EventIds.Resource_Released);
                }
                
                // 关闭页面时立即隐藏，避免可能的额外交互
                try
                {
                    if (!this.IsDisposed)
                    {
                        this.Visible = false;
                    }
                }
                catch (Exception ex)
                {
                    Logger?.LogDebug($"设置页面可见性时发生错误: {ex.Message}");
                }
                
                // 记录资源释放完成
                Logger?.LogInformation("FTitlePage3资源释放完成", WaferAligner.EventIds.EventIds.Resource_Released);
                
                // 调用基类OnDispose
                base.OnDispose();
            }
            catch (Exception ex)
            {
                Logger?.LogError(ex, "FTitlePage3资源释放失败", WaferAligner.EventIds.EventIds.Resource_Released);
            }
        }

        public void CleanUp()
        {
            // 转发到新的资源管理机制
            this.Dispose();
        }

        #endregion

        #region  TabControl
        private void TabControl1_DrawItem(object sender, DrawItemEventArgs e)
        {
            TabControl temp = (TabControl)sender;

            Graphics g = e.Graphics;
            //修改tabcontrol的背景颜色
            //g.FillRectangle(new SolidBrush(SystemColors.Control), ClientRectangle);

            Font font = new("宋体", 12f, FontStyle.Regular);

            for (int i = 0; i < temp.TabCount; i++)
            {
                Rectangle rect = temp.GetTabRect(i);

                StringFormat sf = new()
                {
                    LineAlignment = StringAlignment.Center,
                    Alignment = StringAlignment.Near
                };//封装文本布局信息 
                  //写字                
                g.DrawString(temp.TabPages[i].Text, font, SystemBrushes.ControlText, rect, sf);
                //恢复原始变换
                //g.Transform = matxSave;                
            }
            g.Dispose();
        }

        #endregion  TabControl

        void BtnManage(bool EnableState)
        {
            BtnCalPos.Enabled = EnableState;
            //BtnFiveStop.Enabled = EnableState;
            BtnCal.Enabled = EnableState;
            uiTabControlMenu1.Enabled = EnableState;
        }

        private async void BtnTopWafer_Release_Click(object sender, EventArgs e)
        {
            BtnTopWafer_Release.Enabled = false;
            // 使用_axisEventService替代静态引用，控制上晶圆气缸
            await _axisEventService.CylinderControlAsync("TOPWAFER", (int)CylinderStatus.Open);
            BtnTopWafer_Release.Enabled = true;
        }

        private void FTitlePage3_FormClosing(object sender, FormClosingEventArgs e)
        {
            try
            {
                // ?? 关键改进：在FormClosing的第一时间就设置关闭标志
                _isClosing = true;

                // 立即将字段设为null，防止其他线程访问
                var updateTimer = _UpdateTimer;
                var calibrateTimer = _CalibrateTimer;
                var inputTimer = _InputTimer;
                var calTimer = _CalTimer;
                _UpdateTimer = null;
                _CalibrateTimer = null;
                _InputTimer = null;
                _CalTimer = null;

                // ?? 立即停止所有Timer，防止在保存配置期间有Timer回调执行
                try
                {
                    if (updateTimer != null)
                    {
                        updateTimer.Stop();
                        updateTimer.Dispose();
                    }
                    if (calibrateTimer != null)
                    {
                        calibrateTimer.Stop();
                        calibrateTimer.Dispose();
                    }
                    if (inputTimer != null)
                    {
                        inputTimer.Stop();
                        inputTimer.Dispose();
                    }
                    if (calTimer != null)
                    {
                        calTimer.Stop();
                        calTimer.Dispose();
                    }
                    if (_CalTimer != null)
                    {
                        _CalTimer.Stop();
                        // 不能设置Enabled属性，它是只读的
                    }

                    // 取消标定位运动的取消令牌源
                    if (_calPosCts != null)
                    {
                        _calPosCts.Cancel();
                    }
                }
                catch (Exception ex)
                {
                    Logger?.LogWarning($"停止Timer时发生错误: {ex.Message}", EventIds.Timer_Stop_Error_In_Form_Closing);
                }

                // 保存配置
                SaveConfigurations();
            }
            catch (Exception ex)
            {
                Logger?.LogError($"窗体关闭时发生错误: {ex.Message}", EventIds.Form_Closing_Error);
            }
        }

        // 处理标签页切换事件
        private async void UiTabControlMenu1_SelectedIndexChanged(object sender, EventArgs e)
        {
            try
            {
                // 如果正在关闭，则跳过处理
                if (_isClosing)
                {
                    return;
                }

                // 获取当前选中的标签页
                var selectedTab = uiTabControlMenu1.SelectedTab;
                if (selectedTab == null) return;

                Logger?.LogDebug($"切换到标签页: {selectedTab.Name} ({selectedTab.Text})", EventIds.Tab_Switched);

                // 暂时禁用TabControl，防止快速切换导致的问题
                uiTabControlMenu1.Enabled = false;
                
                // 检查是否是首次切换到Z轴或相机轴
                if ((selectedTab.Name == "tPZ" || selectedTab.Name.Contains("tPL") || selectedTab.Name.Contains("tPR")) &&
                    !_initializedTabs.Contains(selectedTab.Name))
                {
                    try
                    {
                        // 禁用标签控件，防止用户在初始化过程中再次切换
                        uiTabControlMenu1.Enabled = false;
                        
                        // 记录已初始化的标签页
                        _initializedTabs.Add(selectedTab.Name);
                        
                        // 显示加载提示
                        await SafeInvokeAsync(() => {
                            // UpdateStatus($"正在加载{selectedTab.Text}控件...");
                        });
                        
                        // 初始化对应控件
                        await InitializeControlByTabName(selectedTab.Name);
                        
                        // 恢复状态提示
                        await SafeInvokeAsync(() => {
                            // UpdateStatus($"{selectedTab.Text}控件加载完成");
                        });
                    }
                    catch (Exception ex)
                    {
                        // 处理初始化过程中的异常
                        Logger?.LogError(ex, $"初始化标签页 {selectedTab.Name} 失败", EventIds.Tab_Initialization_Failed);
                        
                        // 从已初始化列表中移除，允许用户重试
                        _initializedTabs.Remove(selectedTab.Name);
                        
                        await SafeInvokeAsync(() => {
                            // UpdateStatus($"{selectedTab.Text}控件加载失败");
                            MessageBox.Show($"加载{selectedTab.Text}控件失败: {ex.Message}\n请尝试重新切换标签页或重启应用程序。", 
                                           "控件初始化错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        });
                    }
                    finally
                    {
                        // 确保标签控件被重新启用
                        await SafeInvokeAsync(() => {
                            uiTabControlMenu1.Enabled = true;
                        });
                    }
                }
                else
                {
                    // 如果不需要初始化，直接重新启用TabControl
                    await SafeInvokeAsync(() => {
                        uiTabControlMenu1.Enabled = true;
                    });
                }
            }
            catch (Exception ex)
            {
                Logger?.LogError(ex, $"标签页切换事件处理失败: {ex.Message}", EventIds.Unhandled_Exception);
                await SafeInvokeAsync(() => {
                    // UpdateStatus("标签页切换失败");
                    uiTabControlMenu1.Enabled = true; // 确保在异常情况下也能重新启用
                });
            }
        }
        
        // 根据标签名初始化对应控件
        private async Task InitializeControlByTabName(string tabName)
        {
            try
            {
                // 记录开始初始化的日志
                Logger?.LogInformation($"开始初始化{tabName}控件", EventIds.Control_Initialization_Started);
                
                // 延迟一小段时间让UI能够更新状态显示
                await Task.Delay(50);
                
                // 创建一个超时保护的CancellationTokenSource
                using var timeoutCts = new CancellationTokenSource(10000); // 10秒超时
                
                // 根据标签名初始化对应控件
                switch (tabName)
                {
                    case "tPZ":
                        Logger?.LogDebug($"正在初始化Z轴控件", EventIds.Control_Initialization_In_Progress);

                        // 不等待InitServices完成，让它在后台异步执行，避免阻塞选项卡切换
                        _ = Task.Run(async () => {
                            try {
                                步进电机调试Z.InitServices(Logger, _axisEventService, _plcVariableService, _plcConnectionManager, _axisFactory, _cylinderService);
                                // 给一点时间让Load事件完成
                                await Task.Delay(100);
                                Logger?.LogDebug("Z轴控件后台初始化完成", EventIds.Control_Initialization_Completed);
                            }
                            catch (Exception ex) {
                                Logger?.LogError(ex, "Z轴控件初始化失败", EventIds.Control_Initialization_Failed);
                            }
                        });
                        break;
                    case "tPLX":
                        Logger?.LogDebug($"正在初始化LX轴控件", EventIds.Control_Initialization_In_Progress);

                        // 不等待InitServices完成，让它在后台异步执行，避免阻塞选项卡切换
                        _ = Task.Run(async () => {
                            try {
                                步进电机调试LX.InitServices(Logger, _axisEventService, _plcVariableService, _plcConnectionManager, _axisFactory, _cylinderService);
                                await Task.Delay(100);
                                Logger?.LogDebug("LX轴控件后台初始化完成", EventIds.Control_Initialization_Completed);
                            }
                            catch (Exception ex) {
                                Logger?.LogError(ex, "LX轴控件初始化失败", EventIds.Control_Initialization_Failed);
                            }
                        });
                        break;
                    case "tPLY":
                        Logger?.LogDebug($"正在初始化LY轴控件", EventIds.Control_Initialization_In_Progress);

                        // 不等待InitServices完成，让它在后台异步执行，避免阻塞选项卡切换
                        _ = Task.Run(async () => {
                            try {
                                步进电机调试LY.InitServices(Logger, _axisEventService, _plcVariableService, _plcConnectionManager, _axisFactory, _cylinderService);
                                await Task.Delay(100);
                                Logger?.LogDebug("LY轴控件后台初始化完成", EventIds.Control_Initialization_Completed);
                            }
                            catch (Exception ex) {
                                Logger?.LogError(ex, "LY轴控件初始化失败", EventIds.Control_Initialization_Failed);
                            }
                        });
                        break;
                    case "tPLZ":
                        Logger?.LogDebug($"正在初始化LZ轴控件", EventIds.Control_Initialization_In_Progress);

                        // 不等待InitServices完成，让它在后台异步执行，避免阻塞选项卡切换
                        _ = Task.Run(async () => {
                            try {
                                步进电机调试LZ.InitServices(Logger, _axisEventService, _plcVariableService, _plcConnectionManager, _axisFactory, _cylinderService);
                                await Task.Delay(100);
                                Logger?.LogDebug("LZ轴控件后台初始化完成", EventIds.Control_Initialization_Completed);
                            }
                            catch (Exception ex) {
                                Logger?.LogError(ex, "LZ轴控件初始化失败", EventIds.Control_Initialization_Failed);
                            }
                        });
                        break;
                    case "tPRX":
                        Logger?.LogDebug($"正在初始化RX轴控件", EventIds.Control_Initialization_In_Progress);

                        // 不等待InitServices完成，让它在后台异步执行，避免阻塞选项卡切换
                        _ = Task.Run(async () => {
                            try {
                                步进电机调试RX.InitServices(Logger, _axisEventService, _plcVariableService, _plcConnectionManager, _axisFactory, _cylinderService);
                                await Task.Delay(100);
                                Logger?.LogDebug("RX轴控件后台初始化完成", EventIds.Control_Initialization_Completed);
                            }
                            catch (Exception ex) {
                                Logger?.LogError(ex, "RX轴控件初始化失败", EventIds.Control_Initialization_Failed);
                            }
                        });
                        break;
                    case "tPRY":
                        Logger?.LogDebug($"正在初始化RY轴控件", EventIds.Control_Initialization_In_Progress);

                        // 不等待InitServices完成，让它在后台异步执行，避免阻塞选项卡切换
                        _ = Task.Run(async () => {
                            try {
                                步进电机调试RY.InitServices(Logger, _axisEventService, _plcVariableService, _plcConnectionManager, _axisFactory, _cylinderService);
                                await Task.Delay(100);
                                Logger?.LogDebug("RY轴控件后台初始化完成", EventIds.Control_Initialization_Completed);
                            }
                            catch (Exception ex) {
                                Logger?.LogError(ex, "RY轴控件初始化失败", EventIds.Control_Initialization_Failed);
                            }
                        });
                        break;
                    case "tPRZ":
                        Logger?.LogDebug($"正在初始化RZ轴控件", EventIds.Control_Initialization_In_Progress);

                        // 不等待InitServices完成，让它在后台异步执行，避免阻塞选项卡切换
                        _ = Task.Run(async () => {
                            try {
                                步进电机调试RZ.InitServices(Logger, _axisEventService, _plcVariableService, _plcConnectionManager, _axisFactory, _cylinderService);
                                await Task.Delay(100);
                                Logger?.LogDebug("RZ轴控件后台初始化完成", EventIds.Control_Initialization_Completed);
                            }
                            catch (Exception ex) {
                                Logger?.LogError(ex, "RZ轴控件初始化失败", EventIds.Control_Initialization_Failed);
                            }
                        });
                        break;
                }

                // 选项卡切换立即完成，初始化在后台进行
                Logger?.LogInformation($"{tabName}控件切换完成，初始化在后台进行", EventIds.Control_Initialization_Completed);
            }
            catch (TaskCanceledException)
            {
                // 处理超时情况
                Logger?.LogError($"{tabName}控件初始化超时", EventIds.Control_Initialization_Timeout);
                await SafeInvokeAsync(() => {
                    // UpdateStatus($"{tabName}控件初始化超时，请重试");
                    MessageBox.Show($"{tabName}控件初始化超时，请重试或重启应用程序", "初始化超时", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                });
            }
            catch (Exception ex)
            {
                Logger?.LogError(ex, $"初始化{tabName}控件失败: {ex.Message}", EventIds.Control_Initialization_Failed);
                await SafeInvokeAsync(() => {
                    // UpdateStatus($"{tabName}控件初始化失败");
                    MessageBox.Show($"{tabName}控件初始化失败: {ex.Message}", "初始化错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                });
            }
        }

        private void UpdateStatus(string status)
        {
            _statusUpdateService?.UpdateStatus(status);
        }

    }
}

