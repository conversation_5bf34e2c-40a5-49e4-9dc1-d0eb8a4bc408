using System;
using System.Drawing;
using System.Windows.Forms;
using Sunny.UI;
using WaferAligner.Services.Extensions;

namespace WaferAligner.Common
{
    /// <summary>
    /// 加载指示器控件
    /// 用于显示加载进度和状态
    /// </summary>
    public class LoadingIndicator : UIUserControl
    {
        private UILabel _lblMessage;
        private UIProcessBar _progressBar;
        private UISymbolButton _btnCancel;
        private UILabel _lblTitle;
        
        // 进度值（0-100）
        public int Progress
        {
            get => _progressBar.Value;
            set => SafeSetProgress(value);
        }
        
        // 消息文本
        public string Message
        {
            get => _lblMessage.Text;
            set => SafeSetText(_lblMessage, value);
        }
        
        // 标题文本
        public string Title
        {
            get => _lblTitle.Text;
            set => SafeSetText(_lblTitle, value);
        }
        
        // 是否显示取消按钮
        public bool ShowCancelButton
        {
            get => _btnCancel.Visible;
            set => SafeSetVisible(_btnCancel, value);
        }
        
        // 取消事件
        public event EventHandler CancelRequested;
        
        public LoadingIndicator()
        {
            InitializeComponent();
            
            // 设置默认属性
            this.BackColor = UIColor.White;
            this.Radius = 10;
            this.Size = new Size(400, 120);
            this.ShowCancelButton = false;
        }
        
        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // 标题标签
            _lblTitle = new UILabel();
            _lblTitle.Text = "正在加载";
            _lblTitle.Font = new Font("微软雅黑", 12F, FontStyle.Bold);
            _lblTitle.Location = new Point(16, 10);
            _lblTitle.Size = new Size(370, 25);
            _lblTitle.TextAlign = ContentAlignment.MiddleLeft;
            
            // 消息标签
            _lblMessage = new UILabel();
            _lblMessage.Text = "请稍候...";
            _lblMessage.Font = new Font("微软雅黑", 10.5F);
            _lblMessage.Location = new Point(16, 40);
            _lblMessage.Size = new Size(370, 22);
            _lblMessage.TextAlign = ContentAlignment.MiddleLeft;
            
            // 进度条
            _progressBar = new UIProcessBar();
            _progressBar.Size = new Size(368, 29);
            _progressBar.Location = new Point(16, 70);
            _progressBar.Value = 0;
            _progressBar.Maximum = 100;
            _progressBar.ShowPercent = true;
            _progressBar.StyleCustomMode = true;
            
            // 取消按钮
            _btnCancel = new UISymbolButton();
            _btnCancel.Symbol = 61453; // FontAwesome X符号
            _btnCancel.Size = new Size(70, 24);
            _btnCancel.Location = new Point(314, 40);
            _btnCancel.Text = "取消";
            _btnCancel.Font = new Font("微软雅黑", 9F);
            _btnCancel.Visible = false;
            _btnCancel.Click += _btnCancel_Click;
            
            // 添加控件
            this.Controls.Add(_lblTitle);
            this.Controls.Add(_lblMessage);
            this.Controls.Add(_progressBar);
            this.Controls.Add(_btnCancel);
            
            this.ResumeLayout(false);
        }
        
        private void _btnCancel_Click(object sender, EventArgs e)
        {
            CancelRequested?.Invoke(this, EventArgs.Empty);
        }
        
        // 安全设置进度值
        private void SafeSetProgress(int value)
        {
            if (this.InvokeRequired)
            {
                try
                {
                    this.Invoke(new Action(() => _progressBar.Value = Math.Max(0, Math.Min(100, value))));
                }
                catch { }
            }
            else
            {
                _progressBar.Value = Math.Max(0, Math.Min(100, value));
            }
        }
        
        // 安全设置文本
        private void SafeSetText(Control control, string text)
        {
            if (control == null) return;
            
            if (this.InvokeRequired)
            {
                try
                {
                    this.Invoke(new Action(() => control.Text = text ?? string.Empty));
                }
                catch { }
            }
            else
            {
                control.Text = text ?? string.Empty;
            }
        }
        
        // 安全设置可见性
        private void SafeSetVisible(Control control, bool visible)
        {
            if (control == null) return;
            
            if (this.InvokeRequired)
            {
                try
                {
                    this.Invoke(new Action(() => control.Visible = visible));
                }
                catch { }
            }
            else
            {
                control.Visible = visible;
            }
        }
    }
} 