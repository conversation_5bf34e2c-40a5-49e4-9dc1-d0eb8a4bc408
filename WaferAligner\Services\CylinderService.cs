using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using WaferAligner.Services.Abstractions;
using WaferAligner.Communication.Inovance.Abstractions;
using WaferAligner.Services.Extensions;
using WaferAligner.Interfaces;
using WaferAligner.Services;
using static WaferAligner.Services.CylinderConstants;

namespace WaferAligner.Services
{
    /// <summary>
    /// 气缸控制服务实现类 - 重构版本
    /// 直接使用IPlcVariableService进行PLC通讯，消除与MainWindowViewModel的循环依赖
    /// </summary>
    public class CylinderService : ICylinderService
    {
        private readonly IPlcVariableService _plcVariableService;
        private readonly ILoggingService _loggingService;
        private readonly string[] _supportedCylinders;

        // 气缸PLC变量映射 - 基于实际PLC程序中的变量名称
        private readonly Dictionary<string, (string StateVar, string ExecuteVar)> _cylinderVarMap;

        /// <summary>
        /// 构造函数 - 重构版本
        /// </summary>
        /// <param name="plcVariableService">PLC变量服务</param>
        /// <param name="loggingService">日志服务</param>
        public CylinderService(
            IPlcVariableService plcVariableService,
            ILoggingService loggingService)
        {
            _plcVariableService = plcVariableService ?? throw new ArgumentNullException(nameof(plcVariableService));
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));

            // 初始化支持的气缸列表
            _supportedCylinders = new[]
            {
                CylinderTypes.TOP_WAFER,
                CylinderTypes.TRAY_WAFER_INNER,
                CylinderTypes.TRAY_WAFER_OUTER,
                CylinderTypes.TRAY,
                CylinderTypes.CHUCK_LOCK,
                CylinderTypes.HORIZONTAL_ADJUST
            };

            // 初始化气缸PLC变量映射 - 基于Control.cs中的实际PLC变量名称
            _cylinderVarMap = new Dictionary<string, (string StateVar, string ExecuteVar)>
            {
                { CylinderTypes.TOP_WAFER, ($"{AxisConstants.AXIS_GVL}.UpperWaferState", $"{AxisConstants.AXIS_GVL}.UpperWaferExecute") },
                { CylinderTypes.TRAY_WAFER_OUTER, ($"{AxisConstants.AXIS_GVL}.LowerWaferChuckState", $"{AxisConstants.AXIS_GVL}.LowerWaferChuckExecute") },
                { CylinderTypes.TRAY_WAFER_INNER, ($"{AxisConstants.AXIS_GVL}.LowerChuckState", $"{AxisConstants.AXIS_GVL}.LowerChuckExecute") },
                { CylinderTypes.TRAY, ($"{AxisConstants.AXIS_GVL}.LowerWaferState", $"{AxisConstants.AXIS_GVL}.LowerWaferExecute") },
                { CylinderTypes.CHUCK_LOCK, ($"{AxisConstants.AXIS_GVL}.UpperChuckCylinderState", $"{AxisConstants.AXIS_GVL}.UpperChuckCylinderExecute") },
                { CylinderTypes.HORIZONTAL_ADJUST, ($"{AxisConstants.AXIS_GVL}.CylinderState", $"{AxisConstants.AXIS_GVL}.CylinderExecute") }
            };
        }

        /// <summary>
        /// 控制气缸状态 - 重构版本
        /// 直接使用IPlcVariableService进行PLC通讯，消除循环依赖
        /// </summary>
        /// <param name="cylinderType">气缸类型</param>
        /// <param name="targetState">目标状态</param>
        /// <returns>操作是否成功</returns>
        public async Task<bool> ControlCylinderAsync(string cylinderType, int targetState)
        {
            try
            {
                // 验证参数
                ValidateCylinderType(cylinderType);
                ValidateCylinderState(targetState);

                // 记录操作
                _loggingService.LogInformation($"控制气缸 {cylinderType} 到状态 {targetState}", WaferAligner.EventIds.EventIds.Cylinder_Operation_Started);

                // 检查开发模式
                bool isDevelopmentMode = WaferAligner.Common.DevelopmentModeHelper.IsDevelopmentMode();
                if (isDevelopmentMode)
                {
                    _loggingService.LogInformation($"开发模式：模拟气缸 {cylinderType} 控制到状态 {targetState}", WaferAligner.EventIds.EventIds.Cylinder_Operation_Started);
                    await Task.Delay(100); // 模拟执行时间
                    _loggingService.LogInformation($"气缸 {cylinderType} 控制成功，目标状态 {targetState}", WaferAligner.EventIds.EventIds.Cylinder_Operation_Completed);
                    return true;
                }

                // 获取PLC变量映射
                if (!_cylinderVarMap.TryGetValue(cylinderType.ToUpper(), out var vars))
                {
                    _loggingService.LogWarning($"未找到气缸 {cylinderType} 的PLC变量映射", WaferAligner.EventIds.EventIds.Cylinder_Operation_Failed);
                    return false;
                }

                // 执行PLC控制序列
                // 1. 设置目标状态
                bool stateWriteSuccess = await _plcVariableService.WriteVariableSafelyAsync(vars.StateVar, (UInt16)targetState);
                if (!stateWriteSuccess)
                {
                    _loggingService.LogWarning($"写入气缸 {cylinderType} 状态失败: {vars.StateVar}={targetState}", WaferAligner.EventIds.EventIds.Cylinder_Operation_Failed);
                    return false;
                }

                // 2. 等待状态写入稳定
                await Task.Delay(30);

                // 3. 触发执行命令
                bool executeWriteSuccess = await _plcVariableService.WriteVariableSafelyAsync(vars.ExecuteVar, true);
                if (!executeWriteSuccess)
                {
                    _loggingService.LogWarning($"写入气缸 {cylinderType} 执行命令失败: {vars.ExecuteVar}=true", WaferAligner.EventIds.EventIds.Cylinder_Operation_Failed);
                    return false;
                }

                // 记录成功
                _loggingService.LogInformation($"气缸 {cylinderType} 控制成功，目标状态 {targetState}", WaferAligner.EventIds.EventIds.Cylinder_Operation_Completed);
                return true;
            }
            catch (ArgumentException ex)
            {
                _loggingService.LogWarning(ex, $"气缸控制参数无效: {cylinderType}, {targetState}", WaferAligner.EventIds.EventIds.Cylinder_Operation_Failed);
                return false;
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"控制气缸 {cylinderType} 到状态 {targetState} 失败", WaferAligner.EventIds.EventIds.Cylinder_Operation_Failed);
                return false;
            }
        }

        /// <summary>
        /// 获取气缸当前状态 - 重构版本
        /// 直接从PLC读取状态，而不是从ViewModel获取
        /// </summary>
        /// <param name="cylinderType">气缸类型</param>
        /// <returns>气缸状态</returns>
        public int GetCylinderState(string cylinderType)
        {
            try
            {
                // 验证参数
                ValidateCylinderType(cylinderType);

                // 检查开发模式
                bool isDevelopmentMode = WaferAligner.Common.DevelopmentModeHelper.IsDevelopmentMode();
                if (isDevelopmentMode)
                {
                    // 开发模式返回默认状态
                    return CylinderStates.CLOSED;
                }

                // 获取PLC变量映射
                if (!_cylinderVarMap.TryGetValue(cylinderType.ToUpper(), out var vars))
                {
                    _loggingService.LogWarning($"未找到气缸 {cylinderType} 的PLC变量映射", WaferAligner.EventIds.EventIds.Cylinder_Operation_Failed);
                    return CylinderStates.UNKNOWN;
                }

                // 从PLC读取当前状态
                var currentState = _plcVariableService.ReadVariableSafelyAsync<UInt16>(vars.StateVar, 0).Result;
                return (int)currentState;
            }
            catch (ArgumentException)
            {
                _loggingService.LogWarning($"获取未知气缸状态: {cylinderType}", WaferAligner.EventIds.EventIds.Cylinder_Operation_Failed);
                return CylinderStates.UNKNOWN;
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"获取气缸 {cylinderType} 状态时发生异常", WaferAligner.EventIds.EventIds.Cylinder_Operation_Failed);
                return CylinderStates.UNKNOWN;
            }
        }

        /// <summary>
        /// 等待气缸达到指定状态 - 重构版本
        /// 保持原有逻辑，但使用重构后的GetCylinderState方法
        /// </summary>
        /// <param name="cylinderType">气缸类型</param>
        /// <param name="expectedState">期望状态</param>
        /// <param name="timeout">超时时间</param>
        /// <returns>是否在超时前达到期望状态</returns>
        public async Task<bool> WaitForCylinderStateAsync(string cylinderType, int expectedState, TimeSpan timeout)
        {
            try
            {
                // 验证参数
                ValidateCylinderType(cylinderType);
                ValidateCylinderState(expectedState);

                // 记录开始等待
                _loggingService.LogInformation($"等待气缸 {cylinderType} 达到状态 {expectedState}, 超时 {timeout.TotalMilliseconds}ms",
                    WaferAligner.EventIds.EventIds.Cylinder_Operation_Started);

                // 检查开发模式
                bool isDevelopmentMode = WaferAligner.Common.DevelopmentModeHelper.IsDevelopmentMode();
                if (isDevelopmentMode)
                {
                    _loggingService.LogInformation($"开发模式：模拟等待气缸 {cylinderType} 达到状态 {expectedState}", WaferAligner.EventIds.EventIds.Cylinder_Operation_Started);
                    await Task.Delay(50); // 模拟等待时间
                    _loggingService.LogInformation($"气缸 {cylinderType} 已达到期望状态 {expectedState}", WaferAligner.EventIds.EventIds.Cylinder_Operation_Completed);
                    return true;
                }

                // 开始等待
                DateTime startTime = DateTime.Now;
                while (DateTime.Now - startTime < timeout)
                {
                    if (GetCylinderState(cylinderType) == expectedState)
                    {
                        _loggingService.LogInformation($"气缸 {cylinderType} 已达到期望状态 {expectedState}",
                            WaferAligner.EventIds.EventIds.Cylinder_Operation_Completed);
                        return true;
                    }

                    await Task.Delay(CylinderOperations.STATE_CHECK_INTERVAL_MS);
                }

                // 超时
                _loggingService.LogWarning($"等待气缸 {cylinderType} 达到状态 {expectedState} 超时",
                    WaferAligner.EventIds.EventIds.Cylinder_Operation_Failed);
                return false;
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"等待气缸 {cylinderType} 状态时发生异常",
                    WaferAligner.EventIds.EventIds.Cylinder_Operation_Failed);
                return false;
            }
        }

        /// <summary>
        /// 检查所有气缸是否就绪 - 重构版本
        /// 保持原有逻辑，但使用重构后的GetCylinderState方法
        /// </summary>
        /// <returns>所有气缸是否就绪</returns>
        public async Task<bool> CheckAllCylindersReadyAsync()
        {
            try
            {
                _loggingService.LogInformation("检查所有气缸状态", WaferAligner.EventIds.EventIds.Cylinder_Operation_Started);

                // 检查开发模式
                bool isDevelopmentMode = WaferAligner.Common.DevelopmentModeHelper.IsDevelopmentMode();
                if (isDevelopmentMode)
                {
                    _loggingService.LogInformation("开发模式：所有气缸模拟就绪", WaferAligner.EventIds.EventIds.Cylinder_Operation_Completed);
                    return true;
                }

                // 这里可以根据实际业务需求定义各个气缸的"就绪"状态
                // 当前简单实现：检查每个气缸是否处于预期状态
                bool topWaferReady = GetCylinderState(CylinderTypes.TOP_WAFER) != CylinderStates.UNKNOWN;
                bool trayWaferInnerReady = GetCylinderState(CylinderTypes.TRAY_WAFER_INNER) != CylinderStates.UNKNOWN;
                bool trayWaferOuterReady = GetCylinderState(CylinderTypes.TRAY_WAFER_OUTER) != CylinderStates.UNKNOWN;
                bool trayReady = GetCylinderState(CylinderTypes.TRAY) != CylinderStates.UNKNOWN;
                bool chuckLockReady = GetCylinderState(CylinderTypes.CHUCK_LOCK) != CylinderStates.UNKNOWN;
                bool horizontalAdjustReady = GetCylinderState(CylinderTypes.HORIZONTAL_ADJUST) != CylinderStates.UNKNOWN;

                bool allReady = topWaferReady && trayWaferInnerReady && trayWaferOuterReady
                              && trayReady && chuckLockReady && horizontalAdjustReady;

                _loggingService.LogInformation($"气缸就绪检查结果: {(allReady ? "全部就绪" : "部分未就绪")}",
                    WaferAligner.EventIds.EventIds.Cylinder_Operation_Completed);

                return allReady;
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, "检查气缸就绪状态时发生异常", WaferAligner.EventIds.EventIds.Cylinder_Operation_Failed);
                return false;
            }
        }

        /// <summary>
        /// 获取气缸名称列表
        /// </summary>
        /// <returns>所有支持的气缸名称</returns>
        public string[] GetSupportedCylinders()
        {
            return _supportedCylinders;
        }
        
        #region 辅助方法
        
        /// <summary>
        /// 验证气缸类型是否有效
        /// </summary>
        private void ValidateCylinderType(string cylinderType)
        {
            if (string.IsNullOrEmpty(cylinderType))
            {
                throw new ArgumentException("气缸类型不能为空", nameof(cylinderType));
            }
            
            if (!_supportedCylinders.Contains(cylinderType.ToUpper()))
            {
                throw new ArgumentException($"不支持的气缸类型: {cylinderType}", nameof(cylinderType));
            }
        }
        
        /// <summary>
        /// 验证气缸状态是否有效
        /// </summary>
        private void ValidateCylinderState(int state)
        {
            if (state != CylinderStates.CLOSED && state != CylinderStates.OPENED)
            {
                throw new ArgumentException($"无效的气缸状态: {state}", nameof(state));
            }
        }
        
        #endregion
    }
} 