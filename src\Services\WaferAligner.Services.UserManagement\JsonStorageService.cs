using Newtonsoft.Json;
using System.Security.Cryptography;
using System.Text;

namespace WaferAligner.Services.UserManagement
{
    public class JsonStorageService
    {
        private readonly string _usersFilePath;
        private readonly string _rolesFilePath;
        private readonly string _permissionsFilePath;
        private readonly string _dataDirectory;

        public JsonStorageService()
        {
            _dataDirectory = Environment.CurrentDirectory + @"\Users\";

            _usersFilePath = Path.Combine(_dataDirectory, "users.json");
            _rolesFilePath = Path.Combine(_dataDirectory, "roles.json");
            _permissionsFilePath = Path.Combine(_dataDirectory, "permissions.json");
            
            EnsureDirectoryExists();
        }

        private void EnsureDirectoryExists()
        {
            if (!Directory.Exists(_dataDirectory))
            {
                Directory.CreateDirectory(_dataDirectory);
            }
        }

        public List<UserInfo> LoadUsers()
        {
            if (!File.Exists(_usersFilePath))
            {
                return GetDefaultUsers();
            }

            try
            {
                var json = File.ReadAllText(_usersFilePath);
                var users = JsonConvert.DeserializeObject<List<UserInfo>>(json);
                if (users == null || users.Count == 0)
                {
                    return GetDefaultUsers();
                }
                return users;
            }
            catch
            {
                return GetDefaultUsers();
            }
        }

        public void SaveUsers(List<UserInfo> users)
        {
            var json = JsonConvert.SerializeObject(users, Formatting.Indented);
            File.WriteAllText(_usersFilePath, json);
        }

        public List<Role> LoadRoles()
        {
            if (!File.Exists(_rolesFilePath))
            {
                return GetDefaultRoles();
            }

            try
            {
                var json = File.ReadAllText(_rolesFilePath);
                return JsonConvert.DeserializeObject<List<Role>>(json) ?? GetDefaultRoles();
            }
            catch
            {
                return GetDefaultRoles();
            }
        }

        public void SaveRoles(List<Role> roles)
        {
            var json = JsonConvert.SerializeObject(roles, Formatting.Indented);
            File.WriteAllText(_rolesFilePath, json);
        }

        public List<Permission> LoadPermissions()
        {
            if (!File.Exists(_permissionsFilePath))
            {
                return GetDefaultPermissions();
            }

            try
            {
                var json = File.ReadAllText(_permissionsFilePath);
                return JsonConvert.DeserializeObject<List<Permission>>(json) ?? GetDefaultPermissions();
            }
            catch
            {
                return GetDefaultPermissions();
            }
        }

        public void SavePermissions(List<Permission> permissions)
        {
            var json = JsonConvert.SerializeObject(permissions, Formatting.Indented);
            File.WriteAllText(_permissionsFilePath, json);
        }

        private List<Role> GetDefaultRoles()
        {
            return new List<Role>
            {
                new Role
                {
                    Name = "Admin",
                    Description = "系统管理员",
                    Permissions = new List<string> { "*" } // 所有权限
                },
                new Role
                {
                    Name = "Engineer",
                    Description = "工程师",
                    Permissions = new List<string> 
                    { 
                        "system.view", "system.config", "recipe.view", "recipe.edit",
                        "process.view", "process.control", "data.view", "data.export",
                        "user.management", "parameter.config", "motion.config" // 工程师可以管理用户和配置参数
                    }
                },
                new Role
                {
                    Name = "Operator",
                    Description = "操作员",
                    Permissions = new List<string> 
                    { 
                        "system.view", "process.view", "process.control"
                        // 注意：操作员没有 user.management、parameter.config、motion.config 权限
                    }
                }
            };
        }

        private List<UserInfo> GetDefaultUsers()
        {
            return new List<UserInfo>
            {
                new UserInfo
                {
                    Username = "admin",
                    PasswordHash = "admin123", // 默认密码，建议首次登录后修改
                    Roles = new[] { "Admin" },
                    Description = "系统管理员",
                    IsActive = true,
                    CreatedDate = DateTime.Now,
                    LastLoginDate = null
                },
                new UserInfo
                {
                    Username = "engineer",
                    PasswordHash = "engineer123",
                    Roles = new[] { "Engineer" },
                    Description = "工程师",
                    IsActive = true,
                    CreatedDate = DateTime.Now,
                    LastLoginDate = null
                },
                new UserInfo
                {
                    Username = "operator",
                    PasswordHash = "operator123",
                    Roles = new[] { "Operator" },
                    Description = "操作员",
                    IsActive = true,
                    CreatedDate = DateTime.Now,
                    LastLoginDate = null
                }
            };
        }

        private List<Permission> GetDefaultPermissions()
        {
            return new List<Permission>
            {
                new Permission { Name = "system.view", Description = "查看系统信息", Category = "System" },
                new Permission { Name = "system.config", Description = "系统配置", Category = "System" },
                new Permission { Name = "system.admin", Description = "系统管理", Category = "System" },
                new Permission { Name = "recipe.view", Description = "查看配方", Category = "Recipe" },
                new Permission { Name = "recipe.edit", Description = "编辑配方", Category = "Recipe" },
                new Permission { Name = "recipe.delete", Description = "删除配方", Category = "Recipe" },
                new Permission { Name = "process.view", Description = "查看工艺", Category = "Process" },
                new Permission { Name = "process.control", Description = "工艺控制", Category = "Process" },
                new Permission { Name = "data.view", Description = "查看数据", Category = "Data" },
                new Permission { Name = "data.export", Description = "导出数据", Category = "Data" },
                new Permission { Name = "user.view", Description = "查看用户", Category = "User" },
                new Permission { Name = "user.edit", Description = "编辑用户", Category = "User" },
                new Permission { Name = "user.delete", Description = "删除用户", Category = "User" },
                new Permission { Name = "user.management", Description = "用户管理页面访问", Category = "User" },
                new Permission { Name = "parameter.config", Description = "对准参数配置页面访问", Category = "Config" },
                new Permission { Name = "motion.config", Description = "运动参数配置页面访问", Category = "Config" }
            };
        }
    }
}
