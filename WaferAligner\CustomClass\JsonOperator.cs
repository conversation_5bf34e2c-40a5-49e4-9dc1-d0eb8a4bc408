﻿using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using WaferAligner.Services.Abstractions;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;
using System.Xml;
using WaferAligner.Common;
using WaferAligner.EventIds;
using Microsoft.Extensions.DependencyInjection;
using WaferAligner.Services.Extensions;

namespace WaferAligner.CustomClass
{
    // 添加静态缓存，避免重复读取相同的文件
    class JsonOperator
    {
        // 静态缓存，按文件路径缓存配置数据
        private static readonly ConcurrentDictionary<string, CachedConfig> _fileCache = new ConcurrentDictionary<string, CachedConfig>();
        
        // 缓存过期时间（毫秒）
        private const int CACHE_EXPIRY_MS = 30000; // 30秒
        
        private ConcurrentDictionary<string, object> _tmpConfigurationMap = new ConcurrentDictionary<string, object>();
        
        private string tmpConfigFilePath = "";
        private readonly ILoggingService _loggingService;

        private readonly JObject _jObject = new JObject();
        private bool _isLoaded = false;

        // 缓存配置类
        private class CachedConfig
        {
            public ConcurrentDictionary<string, object> Data { get; } = new ConcurrentDictionary<string, object>();
            public DateTime LastAccess { get; set; } = DateTime.Now;
        }

        public JsonOperator(string path, ILoggingService loggingService = null)
        {
            this.tmpConfigFilePath = path;
            _loggingService = loggingService;
            
            // 检查缓存中是否已有此文件的数据
            if (_fileCache.TryGetValue(path, out var cachedConfig))
            {
                // 更新访问时间
                cachedConfig.LastAccess = DateTime.Now;
                
                // 使用缓存的数据
                foreach (var item in cachedConfig.Data)
                {
                    _tmpConfigurationMap[item.Key] = item.Value;
                }
                _isLoaded = true;
                
                // 定期清理过期缓存
                CleanupExpiredCache();
            }
            else
            {
                // 缓存中没有，加载文件
                LoadConfiguration(path, _tmpConfigurationMap);
            }
        }

        // 清理过期缓存
        private static void CleanupExpiredCache()
        {
            // 在后台线程执行清理
            Task.Run(() => {
                var now = DateTime.Now;
                var expiredKeys = _fileCache.Where(kv => (now - kv.Value.LastAccess).TotalMilliseconds > CACHE_EXPIRY_MS)
                                          .Select(kv => kv.Key)
                                          .ToList();
                
                foreach (var key in expiredKeys)
                {
                    _fileCache.TryRemove(key, out _);
                }
            });
        }

        public void Create(string key, object value)
        {
            // 保持添加顺序
            _jObject.Add(key, JToken.FromObject(value));
        }

        public string Select(string name)
        {
            return _tmpConfigurationMap.TryGetValue(name, out object res) switch
            {
                true => res.ToString(),
                false => ""
            };
        }

        public void Save()
        {
            try
            {
                string json = _jObject.ToString(Newtonsoft.Json.Formatting.Indented);
                File.WriteAllText(tmpConfigFilePath, json);
                
                // 更新缓存
                UpdateCache();
            }
            catch (Exception ex)
            {
                _loggingService.LogError("保存配置文件时发生错误", WaferAligner.EventIds.EventIds.Configuration_Error);
            }
        }

        // 更新缓存
        private void UpdateCache()
        {
            var cachedConfig = new CachedConfig();
            foreach (var item in _tmpConfigurationMap)
            {
                cachedConfig.Data[item.Key] = item.Value;
            }
            _fileCache[tmpConfigFilePath] = cachedConfig;
        }

        public async Task<bool> LoadConfigurationAsync(string loadPath, IDictionary<string, object> configmap, Action executor = null)
        {
            try
            {
                if (!File.Exists(loadPath))
                {
                    return false;
                }
                
                using (var fs = File.OpenText(loadPath.ToString()))
                {
                    var json = await JObject.LoadAsync(new JsonTextReader(fs));
                    var e = json.GetEnumerator();
                    while (e.MoveNext())
                    {
                        var kv = e.Current;
                        if (!configmap.TryAdd(kv.Key, kv.Value)) 
                            configmap[kv.Key] = kv.Value;
                    }
                    
                    try
                    {
                        executor?.Invoke();
                    }
                    catch (Exception)
                    {
                        throw;
                    }
                    
                    // 更新缓存
                    if (_fileCache.TryGetValue(loadPath, out var cachedConfig))
                    {
                        cachedConfig.LastAccess = DateTime.Now;
                        foreach (var item in configmap)
                        {
                            cachedConfig.Data[item.Key] = item.Value;
                        }
                    }
                    else
                    {
                        var newCache = new CachedConfig();
                        foreach (var item in configmap)
                        {
                            newCache.Data[item.Key] = item.Value;
                        }
                        _fileCache[loadPath] = newCache;
                    }
                    
                    _isLoaded = true;
                    _loggingService.LogInformation($"异步加载配置文件成功", WaferAligner.EventIds.EventIds.Configuration_Loaded);
                    return true;
                }
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"异步加载设备配置文件发生错误", WaferAligner.EventIds.EventIds.Configuration_Error);
                return false;
            }
        }

        public bool LoadConfiguration(string loadPath, IDictionary<string, object> map)
        {
            try
            {
                if (File.Exists(loadPath))
                {
                    // 检查缓存中是否已有此文件的数据
                    if (_fileCache.TryGetValue(loadPath, out var cachedConfig))
                    {
                        // 更新访问时间
                        cachedConfig.LastAccess = DateTime.Now;
                        
                        // 使用缓存的数据
                        foreach (var item in cachedConfig.Data)
                        {
                            map[item.Key] = item.Value;
                        }
                        
                        _isLoaded = true;
                        return true;
                    }
                    
                    // 缓存中没有，从文件加载
                    using (var fs = File.OpenText(loadPath.ToString()))
                    {
                        var json = JObject.Load(new JsonTextReader(fs));
                        var e = json.GetEnumerator();
                        while (e.MoveNext())
                        {
                            var kv = e.Current;
                            if (!map.TryAdd(kv.Key, kv.Value.ToObject<object>())) 
                                map[kv.Key] = kv.Value.ToObject<object>();
                        }
                        
                        // 添加到缓存
                        var newCache = new CachedConfig();
                        foreach (var item in map)
                        {
                            newCache.Data[item.Key] = item.Value;
                        }
                        _fileCache[loadPath] = newCache;
                        
                        _isLoaded = true;
                        _loggingService.LogInformation($"加载配置文件成功", WaferAligner.EventIds.EventIds.Configuration_Loaded);
                        return true;
                    }
                }
                else
                {
                    return false;
                }
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"加载设备配置文件发生错误", WaferAligner.EventIds.EventIds.Configuration_Error);
                return false;
            }
        }
    }
}
