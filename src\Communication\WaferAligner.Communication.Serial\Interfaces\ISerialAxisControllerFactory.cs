﻿using System.Threading;
using System.Threading.Tasks;

namespace WaferAligner.Communication.Serial.Interfaces
{
    /// <summary>
    /// 串口轴控制器工厂接口
    /// </summary>
    public interface ISerialAxisControllerFactory
    {
        /// <summary>
        /// 异步创建轴控制器
        /// </summary>
        /// <param name="axisName">轴名称</param>
        /// <param name="cancellationToken">取消令牌</param>
        Task<ISerialAxisController> CreateAxisControllerAsync(string axisName, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 获取轴控制器（同步版本）
        /// </summary>
        /// <param name="axisName">轴名称</param>
        ISerialAxisController GetAxisController(string axisName);
    }
}
