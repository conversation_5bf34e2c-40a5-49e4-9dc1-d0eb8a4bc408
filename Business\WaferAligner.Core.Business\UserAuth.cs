using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace WaferAligner.Core.Business
{
    public enum UserAuth
    {
        Admin = 0,
        Engineer = 1,
        Operator = 2,
    }
    public interface IRequestResult
    {
        public bool Result { set; get; }
        public object DataInfo { set; get; }
    }

    public class SuccessRequest : IRequestResult
    {
        public bool Result { get ; set; }
        public object DataInfo { get; set; }
    }

    public class FailedRequest : IRequestResult
    {
        public bool Result { get; set; }
        public object DataInfo { get; set; }
    }
}
