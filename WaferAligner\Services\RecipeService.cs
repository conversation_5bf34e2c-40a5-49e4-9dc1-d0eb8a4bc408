using System;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using AlignerUI;
using WaferAligner.Services.Abstractions;
using WaferAligner.Services.Extensions;
using WaferAligner.Communication.Inovance.Abstractions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using WaferAligner.Services;

namespace WaferAligner.Services
{
    /// <summary>
    /// 配方服务实现类
    /// </summary>
    public class RecipeService : IRecipeService
    {
        private readonly IPlcVariableService _plcVariableService;
        private readonly ILoggingService _loggingService;
        private readonly IConfiguration _configuration;
        private readonly IAlignerParaService _alignerParaService;
        
        private readonly string _recipeFolder;
        private readonly JsonSerializerOptions _jsonOptions;

        #region 属性实现
        public string ProductName { get; set; } = string.Empty;
        public string ProductSize { get; set; } = "6寸";
        public double SpacerThick { get; set; }
        public double MarkDistance { get; set; }
        public WaferSeries Material { get; set; }
        public int VisualNumber { get; set; }

        public double TopWaferPhotoLX { get; set; }
        public double TopWaferPhotoLY { get; set; }
        public double TopWaferPhotoLZ { get; set; }
        public double TopWaferPhotoRX { get; set; }
        public double TopWaferPhotoRY { get; set; }
        public double TopWaferPhotoRZ { get; set; }
        public double TopWaferPhotoZ { get; set; }
        public double TopWaferPhotoX { get; set; }
        public double TopWaferPhotoY { get; set; }
        public double TopWaferPhotoR { get; set; }
        public bool IsTopHorizontalAdjust { get; set; }
        public bool IsTopHorizontalPhoto { get; set; }
        public double TopWaferThick { get; set; }

        public double BottomWaferPhotoZ { get; set; }
        public double BottomWaferPhotoLX { get; set; }
        public double BottomWaferPhotoLY { get; set; }
        public double BottomWaferPhotoLZ { get; set; }
        public double BottomWaferPhotoRX { get; set; }
        public double BottomWaferPhotoRY { get; set; }
        public double BottomWaferPhotoRZ { get; set; }
        public double BottomWaferPhotoX { get; set; }
        public double BottomWaferPhotoY { get; set; }
        public double BottomWaferPhotoR { get; set; }
        public double BottomWaferThick { get; set; }
        public double BottomWaferPhotoFitZ { get; set; }
        #endregion
        
        /// <summary>
        /// 构造函数
        /// </summary>
        public RecipeService(
            IPlcVariableService plcVariableService,
            ILoggingService loggingService,
            IConfiguration configuration,
            IAlignerParaService alignerParaService)
        {
            _plcVariableService = plcVariableService ?? throw new ArgumentNullException(nameof(plcVariableService));
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
            _alignerParaService = alignerParaService ?? throw new ArgumentNullException(nameof(alignerParaService));
            
            // 获取配方文件夹路径，默认为应用程序目录下的Recipes文件夹
            _recipeFolder = _configuration["RecipeSettings:FolderPath"] ?? Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Recipes");
            
            // 确保配方文件夹存在
            if (!Directory.Exists(_recipeFolder))
            {
                Directory.CreateDirectory(_recipeFolder);
            }
            
            // 配置JSON序列化选项
            _jsonOptions = new JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            };
            
            // 注意：不在构造函数中调用ImportFromAlignerParaService，而是延迟到实际需要时
            // 避免循环依赖问题
            _loggingService.LogDebug("RecipeService已创建，将在首次需要时导入配方数据");
        }

        /// <summary>
        /// 从AlignerParaService导入数据
        /// </summary>
        private async Task ImportFromAlignerParaServiceAsync()
        {
            try
            {
                // 使用AlignerParaService同步参数
                await _alignerParaService.SyncToRecipeServiceAsync(this);
                _loggingService.LogInformation("从AlignerParaService导入配方数据成功", WaferAligner.EventIds.EventIds.Configuration_Loaded);
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, "从AlignerParaService导入配方数据失败", WaferAligner.EventIds.EventIds.Configuration_Error);
            }
        }
        
        /// <summary>
        /// 导出数据到AlignerParaService
        /// </summary>
        private async Task ExportToAlignerParaServiceAsync()
        {
            try
            {
                // 使用AlignerParaService同步参数
                await _alignerParaService.SyncFromRecipeServiceAsync(this);
                _loggingService.LogInformation("导出配方数据到AlignerParaService成功", WaferAligner.EventIds.EventIds.Configuration_Saved);
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, "导出配方数据到AlignerParaService失败", WaferAligner.EventIds.EventIds.Configuration_Error);
            }
        }

        /// <summary>
        /// 保存配方到文件
        /// </summary>
        public async Task<bool> SaveRecipeAsync(string fileName = null)
        {
            try
            {
                // 确保有配方名称
                if (string.IsNullOrEmpty(fileName) && string.IsNullOrEmpty(ProductName))
                {
                    _loggingService.LogWarning("保存配方失败：未指定配方名称", WaferAligner.EventIds.EventIds.Configuration_Error);
                    return false;
                }
                
                // 使用指定的文件名或产品名称
                string recipeName = fileName ?? ProductName;
                string filePath = Path.Combine(_recipeFolder, $"{recipeName}.json");
                
                // 序列化配方数据
                var recipeData = new
                {
                    // 基本信息
                    ProductName,
                    ProductSize,
                    SpacerThick,
                    MarkDistance,
                    Material,
                    VisualNumber,
                    
                    // 上晶圆参数
                    TopWaferPhoto = new
                    {
                        TopWaferPhotoLX,
                        TopWaferPhotoLY,
                        TopWaferPhotoLZ,
                        TopWaferPhotoRX,
                        TopWaferPhotoRY,
                        TopWaferPhotoRZ,
                        TopWaferPhotoZ,
                        TopWaferPhotoX,
                        TopWaferPhotoY,
                        TopWaferPhotoR,
                        IsTopHorizontalAdjust,
                        IsTopHorizontalPhoto,
                        TopWaferThick
                    },
                    
                    // 下晶圆参数
                    BottomWaferPhoto = new
                    {
                        BottomWaferPhotoZ,
                        BottomWaferPhotoLX,
                        BottomWaferPhotoLY,
                        BottomWaferPhotoLZ,
                        BottomWaferPhotoRX,
                        BottomWaferPhotoRY,
                        BottomWaferPhotoRZ,
                        BottomWaferPhotoX,
                        BottomWaferPhotoY,
                        BottomWaferPhotoR,
                        BottomWaferThick,
                        BottomWaferPhotoFitZ
                    }
                };
                
                // 保存到文件
                string json = JsonSerializer.Serialize(recipeData, _jsonOptions);
                await File.WriteAllTextAsync(filePath, json);
                
                // 导出到AlignerParaService
                await ExportToAlignerParaServiceAsync();
                
                _loggingService.LogInformation($"成功保存配方：{recipeName}", WaferAligner.EventIds.EventIds.Configuration_Saved);
                return true;
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"保存配方失败：{fileName ?? ProductName}", WaferAligner.EventIds.EventIds.Configuration_Error);
                return false;
            }
        }
        
        /// <summary>
        /// 加载配方
        /// </summary>
        public async Task<bool> LoadRecipeAsync(string fileName = null)
        {
            try
            {
                // 如果未指定文件名，使用当前ProductName
                if (string.IsNullOrEmpty(fileName) && string.IsNullOrEmpty(ProductName))
                {
                    _loggingService.LogWarning("加载配方失败：未指定配方名称", WaferAligner.EventIds.EventIds.Configuration_Error);
                    return false;
                }
                
                string recipeName = fileName ?? ProductName;
                string filePath = Path.Combine(_recipeFolder, $"{recipeName}.json");
                
                if (!File.Exists(filePath))
                {
                    _loggingService.LogWarning($"配方文件不存在：{filePath}", WaferAligner.EventIds.EventIds.Configuration_Error);
                    return false;
                }
                
                // 从文件读取
                string json = await File.ReadAllTextAsync(filePath);
                var recipeData = JsonSerializer.Deserialize<JsonElement>(json, _jsonOptions);
                
                // 读取基本信息
                ProductName = GetPropertyValue(recipeData, "productName", ProductName);
                ProductSize = GetPropertyValue(recipeData, "productSize", ProductSize);
                SpacerThick = GetPropertyValue(recipeData, "spacerThick", SpacerThick);
                MarkDistance = GetPropertyValue(recipeData, "markDistance", MarkDistance);
                Material = (WaferSeries)GetPropertyValue(recipeData, "material", (int)Material);
                VisualNumber = GetPropertyValue(recipeData, "visualNumber", VisualNumber);
                
                // 读取上晶圆参数
                if (recipeData.TryGetProperty("topWaferPhoto", out JsonElement topWaferPhotoElement))
                {
                    TopWaferPhotoLX = GetPropertyValue(topWaferPhotoElement, "topWaferPhotoLX", TopWaferPhotoLX);
                    TopWaferPhotoLY = GetPropertyValue(topWaferPhotoElement, "topWaferPhotoLY", TopWaferPhotoLY);
                    TopWaferPhotoLZ = GetPropertyValue(topWaferPhotoElement, "topWaferPhotoLZ", TopWaferPhotoLZ);
                    TopWaferPhotoRX = GetPropertyValue(topWaferPhotoElement, "topWaferPhotoRX", TopWaferPhotoRX);
                    TopWaferPhotoRY = GetPropertyValue(topWaferPhotoElement, "topWaferPhotoRY", TopWaferPhotoRY);
                    TopWaferPhotoRZ = GetPropertyValue(topWaferPhotoElement, "topWaferPhotoRZ", TopWaferPhotoRZ);
                    TopWaferPhotoZ = GetPropertyValue(topWaferPhotoElement, "topWaferPhotoZ", TopWaferPhotoZ);
                    TopWaferPhotoX = GetPropertyValue(topWaferPhotoElement, "topWaferPhotoX", TopWaferPhotoX);
                    TopWaferPhotoY = GetPropertyValue(topWaferPhotoElement, "topWaferPhotoY", TopWaferPhotoY);
                    TopWaferPhotoR = GetPropertyValue(topWaferPhotoElement, "topWaferPhotoR", TopWaferPhotoR);
                    IsTopHorizontalAdjust = GetPropertyValue(topWaferPhotoElement, "isTopHorizontalAdjust", IsTopHorizontalAdjust);
                    IsTopHorizontalPhoto = GetPropertyValue(topWaferPhotoElement, "isTopHorizontalPhoto", IsTopHorizontalPhoto);
                    TopWaferThick = GetPropertyValue(topWaferPhotoElement, "topWaferThick", TopWaferThick);
                }
                
                // 读取下晶圆参数
                if (recipeData.TryGetProperty("bottomWaferPhoto", out JsonElement bottomWaferPhotoElement))
                {
                    BottomWaferPhotoZ = GetPropertyValue(bottomWaferPhotoElement, "bottomWaferPhotoZ", BottomWaferPhotoZ);
                    BottomWaferPhotoLX = GetPropertyValue(bottomWaferPhotoElement, "bottomWaferPhotoLX", BottomWaferPhotoLX);
                    BottomWaferPhotoLY = GetPropertyValue(bottomWaferPhotoElement, "bottomWaferPhotoLY", BottomWaferPhotoLY);
                    BottomWaferPhotoLZ = GetPropertyValue(bottomWaferPhotoElement, "bottomWaferPhotoLZ", BottomWaferPhotoLZ);
                    BottomWaferPhotoRX = GetPropertyValue(bottomWaferPhotoElement, "bottomWaferPhotoRX", BottomWaferPhotoRX);
                    BottomWaferPhotoRY = GetPropertyValue(bottomWaferPhotoElement, "bottomWaferPhotoRY", BottomWaferPhotoRY);
                    BottomWaferPhotoRZ = GetPropertyValue(bottomWaferPhotoElement, "bottomWaferPhotoRZ", BottomWaferPhotoRZ);
                    BottomWaferPhotoX = GetPropertyValue(bottomWaferPhotoElement, "bottomWaferPhotoX", BottomWaferPhotoX);
                    BottomWaferPhotoY = GetPropertyValue(bottomWaferPhotoElement, "bottomWaferPhotoY", BottomWaferPhotoY);
                    BottomWaferPhotoR = GetPropertyValue(bottomWaferPhotoElement, "bottomWaferPhotoR", BottomWaferPhotoR);
                    BottomWaferThick = GetPropertyValue(bottomWaferPhotoElement, "bottomWaferThick", BottomWaferThick);
                    BottomWaferPhotoFitZ = GetPropertyValue(bottomWaferPhotoElement, "bottomWaferPhotoFitZ", BottomWaferPhotoFitZ);
                }
                
                // 导出到AlignerParaService
                await ExportToAlignerParaServiceAsync();
                
                // 更新PLC参数
                await UpdatePlcParametersAsync();
                
                _loggingService.LogInformation($"成功加载配方：{recipeName}", WaferAligner.EventIds.EventIds.Configuration_Loaded);
                return true;
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"加载配方失败：{fileName ?? ProductName}", WaferAligner.EventIds.EventIds.Configuration_Error);
                return false;
            }
        }
        
        /// <summary>
        /// 获取所有配方名称
        /// </summary>
        public async Task<string[]> GetAllRecipeNamesAsync()
        {
            try
            {
                return await Task.Run(() => 
                {
                    DirectoryInfo dirInfo = new DirectoryInfo(_recipeFolder);
                    return dirInfo.GetFiles("*.json")
                        .Select(f => Path.GetFileNameWithoutExtension(f.Name))
                        .ToArray();
                });
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, "获取配方列表失败", WaferAligner.EventIds.EventIds.Configuration_Error);
                return Array.Empty<string>();
            }
        }
        
        /// <summary>
        /// 删除配方
        /// </summary>
        public async Task<bool> DeleteRecipeAsync(string recipeName)
        {
            try
            {
                if (string.IsNullOrEmpty(recipeName))
                {
                    return false;
                }
                
                string filePath = Path.Combine(_recipeFolder, $"{recipeName}.json");
                if (!File.Exists(filePath))
                {
                    return false;
                }
                
                await Task.Run(() => File.Delete(filePath));
                _loggingService.LogInformation($"成功删除配方：{recipeName}", WaferAligner.EventIds.EventIds.Configuration_Saved);
                return true;
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"删除配方失败：{recipeName}", WaferAligner.EventIds.EventIds.Configuration_Error);
                return false;
            }
        }

        /// <summary>
        /// 更新PLC参数
        /// </summary>
        public async Task UpdatePlcParametersAsync()
        {
            try
            {
                // 确保数据已初始化
                await EnsureDataInitializedAsync();
                
                // 向PLC写入厚度参数
                await _plcVariableService.WriteVariableSafelyAsync(
                    $"{AxisConstants.AXIS_GVL}.SpacerThickness", 
                    SpacerThick * AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION);
                
                await _plcVariableService.WriteVariableSafelyAsync(
                    $"{AxisConstants.AXIS_GVL}.ZWaferThickness", 
                    TopWaferThick * AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION);
                
                await _plcVariableService.WriteVariableSafelyAsync(
                    $"{AxisConstants.AXIS_GVL}.ZWaferDownThickness", 
                    BottomWaferThick * AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION);
                
                // 更新视觉任务号
                await _plcVariableService.WriteVariableSafelyAsync(
                    $"{AxisConstants.AXIS_GVL}.iVisualNum", 
                    VisualNumber);

                _loggingService.LogDebug("更新PLC参数成功", WaferAligner.EventIds.EventIds.Configuration_Saved);
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, "更新PLC参数失败", WaferAligner.EventIds.EventIds.Configuration_Error);
                throw;
            }
        }
        
        /// <summary>
        /// 确保数据已初始化
        /// </summary>
        private async Task EnsureDataInitializedAsync()
        {
            // 使用静态标志跟踪是否已初始化，避免重复初始化
            if (!_isInitialized)
            {
                bool shouldInitialize = false;
                
                // 使用锁检查是否需要初始化，但不在锁内执行异步操作
                lock (_initLock)
                {
                    if (!_isInitialized)
                    {
                        shouldInitialize = true;
                    }
                }
                
                // 锁外执行异步初始化
                if (shouldInitialize)
                {
                    try
                    {
                        // 尝试从AlignerParaService导入数据
                        await ImportFromAlignerParaServiceAsync();
                        
                        // 初始化完成后，再次获取锁来设置标志
                        lock (_initLock)
                        {
                            _isInitialized = true;
                        }
                    }
                    catch (Exception ex)
                    {
                        _loggingService.LogError(ex, "从AlignerParaService导入数据失败", WaferAligner.EventIds.EventIds.Configuration_Error);
                        
                        // 即使导入失败，也标记为已初始化，避免重复尝试
                        lock (_initLock)
                        {
                            _isInitialized = true;
                        }
                    }
                }
            }
        }
        
        // 添加静态标志和锁对象
        private static bool _isInitialized = false;
        private static readonly object _initLock = new object();
        
        #region 辅助方法
        /// <summary>
        /// 从JsonElement中安全获取属性值
        /// </summary>
        private T GetPropertyValue<T>(JsonElement element, string propertyName, T defaultValue)
        {
            if (!element.TryGetProperty(propertyName, out JsonElement property))
            {
                return defaultValue;
            }

            try
            {
                if (typeof(T) == typeof(string))
                {
                    return (T)(object)property.GetString();
                }
                else if (typeof(T) == typeof(int))
                {
                    return (T)(object)property.GetInt32();
                }
                else if (typeof(T) == typeof(double))
                {
                    return (T)(object)property.GetDouble();
                }
                else if (typeof(T) == typeof(bool))
                {
                    return (T)(object)property.GetBoolean();
                }
                else
                {
                    return JsonSerializer.Deserialize<T>(property.GetRawText(), _jsonOptions);
                }
            }
            catch
            {
                return defaultValue;
            }
        }
        #endregion
    }
} 