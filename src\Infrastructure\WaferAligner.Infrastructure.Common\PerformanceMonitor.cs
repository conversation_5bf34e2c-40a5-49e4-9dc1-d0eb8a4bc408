using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using WaferAligner.Services.Logging.Abstractions;
using Microsoft.Extensions.Logging;
using WaferAligner.EventIds;
using WaferAligner.Services.Logging.Extensions;
namespace WaferAligner.Infrastructure.Common
{
    /// <summary>
    /// 性能监控服务
    /// 用于跟踪和记录应用程序各部分的性能数据
    /// </summary>
    public class PerformanceMonitor
    {
        private readonly Dictionary<string, long> _timings = new Dictionary<string, long>();
        private readonly Dictionary<string, Stopwatch> _activeStopwatches = new Dictionary<string, Stopwatch>();
        private readonly ILoggingService _logger;
        private readonly Stopwatch _totalStopwatch = new Stopwatch();
        private readonly Stopwatch _stepStopwatch = new Stopwatch();
        private string _currentOperationName;
        private string _totalOperationName;
        private bool _isEnabled = true;
        private string _currentStepName;
        private bool _outputToDebugConsole = true; // 控制是否输出到调试控制台

        public PerformanceMonitor(ILoggingService logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// 是否启用性能监控
        /// </summary>
        public bool IsEnabled
        {
            get => _isEnabled;
            set => _isEnabled = value;
        }
        
        /// <summary>
        /// 是否输出到调试控制台
        /// </summary>
        public bool OutputToDebugConsole
        {
            get => _outputToDebugConsole;
            set => _outputToDebugConsole = value;
        }

        /// <summary>
        /// 开始监控总体操作
        /// </summary>
        /// <param name="operationName">操作名称</param>
        public void StartOperation(string operationName)
        {
            if (!_isEnabled) return;
            
            _totalOperationName = operationName;
            _timings.Clear();
            _activeStopwatches.Clear();
            _totalStopwatch.Restart();
            
            if (_outputToDebugConsole)
            {
                Debug.WriteLine($"[性能监控] 开始: {operationName}");
            }
        }

        /// <summary>
        /// 开始监控步骤
        /// </summary>
        /// <param name="stepName">步骤名称</param>
        public void StartStep(string stepName)
        {
            if (!_isEnabled) return;
            
            _currentStepName = stepName;
            _stepStopwatch.Restart();
            
            if (_outputToDebugConsole)
            {
                Debug.WriteLine($"[性能监控] 开始步骤: {stepName}");
            }
        }

        /// <summary>
        /// 记录特定计时点
        /// </summary>
        /// <param name="name">计时点名称</param>
        public void RecordTiming(string name)
        {
            if (!_isEnabled) return;
            
            _timings[name] = _stepStopwatch.ElapsedMilliseconds;
            
            if (_outputToDebugConsole)
            {
                Debug.WriteLine($"[性能监控] 记录点: {name} = {_stepStopwatch.ElapsedMilliseconds}ms");
            }
        }

        /// <summary>
        /// 结束当前步骤计时
        /// </summary>
        public void EndStep()
        {
            if (!_isEnabled || string.IsNullOrEmpty(_currentStepName)) return;
            
            _stepStopwatch.Stop();
            _timings[_currentStepName] = _stepStopwatch.ElapsedMilliseconds;
            
            if (_outputToDebugConsole)
            {
                Debug.WriteLine($"[性能监控] 步骤结束: {_currentStepName} 耗时 {_stepStopwatch.ElapsedMilliseconds}ms");
            }
            
            _logger?.LogDebug($"性能: {_currentStepName} 耗时 {_stepStopwatch.ElapsedMilliseconds}ms");
            _currentStepName = null;
        }

        /// <summary>
        /// 结束总体操作计时并显示结果
        /// </summary>
        public void EndOperation()
        {
            if (!_isEnabled || string.IsNullOrEmpty(_totalOperationName)) return;
            
            _totalStopwatch.Stop();
            
            var totalMs = _totalStopwatch.ElapsedMilliseconds;
            if (totalMs == 0) totalMs = 1; // 避免除零错误
            
            var sb = new StringBuilder();
            sb.AppendLine($"==== {_totalOperationName}性能统计 ====");
            sb.AppendLine($"[性能] {_totalOperationName}总耗时: {totalMs}ms");
            
            // 按耗时降序排序
            var sortedTimings = _timings.OrderByDescending(t => t.Value);
            
            foreach (var timing in sortedTimings)
            {
                var percentage = Math.Round((double)timing.Value / totalMs * 100, 1);
                sb.AppendLine($"  - {timing.Key}: {timing.Value}ms ({percentage}%)");
            }
            
            sb.AppendLine("==== 性能监控结束 ====");
            
            var resultText = sb.ToString();
            
            // 输出到调试控制台
            if (_outputToDebugConsole)
            {
                Debug.WriteLine(resultText);
            }
            
            // 还是保留原来的日志输出
            _logger?.LogInformation(resultText, WaferAligner.EventIds.EventIds.Performance_Monitoring_Summary);
        }

        /// <summary>
        /// 开始监控命名操作
        /// </summary>
        /// <param name="operationName">操作名称</param>
        public void StartNamedOperation(string operationName)
        {
            if (!_isEnabled) return;
            
            _currentOperationName = operationName;
            if (!_activeStopwatches.ContainsKey(operationName))
            {
                _activeStopwatches[operationName] = new Stopwatch();
            }
            _activeStopwatches[operationName].Restart();
            
            if (_outputToDebugConsole)
            {
                Debug.WriteLine($"[性能监控] 开始命名操作: {operationName}");
            }
        }

        /// <summary>
        /// 结束命名操作计时
        /// </summary>
        /// <param name="operationName">操作名称</param>
        public void EndNamedOperation(string operationName)
        {
            if (!_isEnabled) return;
            
            if (_activeStopwatches.TryGetValue(operationName, out var sw))
            {
                sw.Stop();
                _timings[operationName] = sw.ElapsedMilliseconds;
                
                if (_outputToDebugConsole)
                {
                    Debug.WriteLine($"[性能监控] 结束命名操作: {operationName} 耗时 {sw.ElapsedMilliseconds}ms");
                }
                
                _logger?.LogDebug($"性能: {operationName} 耗时 {sw.ElapsedMilliseconds}ms");
            }
        }

        /// <summary>
        /// 测量操作执行时间
        /// </summary>
        /// <param name="operationName">操作名称</param>
        /// <param name="action">要执行的操作</param>
        public void MeasureOperation(string operationName, Action action)
        {
            if (!_isEnabled)
            {
                action();
                return;
            }
            
            StartNamedOperation(operationName);
            try
            {
                action();
            }
            finally
            {
                EndNamedOperation(operationName);
            }
        }

        /// <summary>
        /// 异步测量操作执行时间
        /// </summary>
        /// <param name="operationName">操作名称</param>
        /// <param name="action">要执行的异步操作</param>
        public async Task MeasureOperationAsync(string operationName, Func<Task> action)
        {
            if (!_isEnabled)
            {
                await action();
                return;
            }
            
            StartNamedOperation(operationName);
            try
            {
                await action();
            }
            finally
            {
                EndNamedOperation(operationName);
            }
        }

        /// <summary>
        /// 重置所有计时
        /// </summary>
        public void Reset()
        {
            _timings.Clear();
            _activeStopwatches.Clear();
            _currentOperationName = null;
            _totalOperationName = null;
            _currentStepName = null;
        }
    }
} 