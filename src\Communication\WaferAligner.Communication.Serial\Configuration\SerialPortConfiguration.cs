using System;
using System.IO.Ports;

namespace WaferAligner.Communication.Serial.Configuration
{
    /// <summary>
    /// 串口配置类
    /// 从App.config读取串口通信参数，避免硬编码
    /// </summary>
    public class SerialPortConfiguration
    {
        /// <summary>
        /// 串口号（COM端口号）
        /// </summary>
        public int ComPort { get; private set; }

        /// <summary>
        /// 波特率
        /// </summary>
        public int BaudRate { get; private set; }

        /// <summary>
        /// 数据位
        /// </summary>
        public int DataBits { get; private set; }

        /// <summary>
        /// 停止位
        /// </summary>
        public StopBits StopBits { get; private set; }

        /// <summary>
        /// 校验位
        /// </summary>
        public Parity Parity { get; private set; }

        /// <summary>
        /// 流控制
        /// </summary>
        public Handshake Handshake { get; private set; }

        /// <summary>
        /// 串口超时时间（毫秒）
        /// </summary>
        public int Timeout { get; private set; }

        /// <summary>
        /// 重试次数
        /// </summary>
        public int RetryCount { get; private set; }

        /// <summary>
        /// 从App.config加载配置
        /// </summary>
        public static SerialPortConfiguration LoadFromConfig()
        {
            var config = new SerialPortConfiguration();

            try
            {
                // 读取基本配置
                config.ComPort = int.Parse(System.Configuration.ConfigurationManager.AppSettings["DefaultComPort"] ?? "1");
                config.BaudRate = int.Parse(System.Configuration.ConfigurationManager.AppSettings["DefaultBaudRate"] ?? "115200");
                config.DataBits = int.Parse(System.Configuration.ConfigurationManager.AppSettings["DefaultDataBits"] ?? "8");
                config.Timeout = int.Parse(System.Configuration.ConfigurationManager.AppSettings["SerialTimeout"] ?? "5000");
                config.RetryCount = int.Parse(System.Configuration.ConfigurationManager.AppSettings["SerialRetryCount"] ?? "3");

                // 解析停止位
                string stopBitsStr = System.Configuration.ConfigurationManager.AppSettings["DefaultStopBits"] ?? "1";
                config.StopBits = stopBitsStr switch
                {
                    "1" => StopBits.One,
                    "1.5" => StopBits.OnePointFive,
                    "2" => StopBits.Two,
                    _ => StopBits.One
                };

                // 解析校验位
                string parityStr = System.Configuration.ConfigurationManager.AppSettings["DefaultParity"] ?? "None";
                config.Parity = parityStr.ToLower() switch
                {
                    "none" => Parity.None,
                    "odd" => Parity.Odd,
                    "even" => Parity.Even,
                    "mark" => Parity.Mark,
                    "space" => Parity.Space,
                    _ => Parity.None
                };

                // 解析流控制
                string handshakeStr = System.Configuration.ConfigurationManager.AppSettings["DefaultHandshake"] ?? "None";
                config.Handshake = handshakeStr.ToLower() switch
                {
                    "none" => Handshake.None,
                    "xonxoff" => Handshake.XOnXOff,
                    "rts" => Handshake.RequestToSend,
                    "rtsxonxoff" => Handshake.RequestToSendXOnXOff,
                    _ => Handshake.None
                };

                return config;
            }
            catch (Exception ex)
            {
                // 如果配置读取失败，使用默认值
                System.Diagnostics.Debug.WriteLine($"串口配置读取失败，使用默认值: {ex.Message}");
                return GetDefaultConfiguration();
            }
        }

        /// <summary>
        /// 获取默认配置
        /// 优先从App.config读取，如果失败则使用硬编码的安全默认值
        /// </summary>
        public static SerialPortConfiguration GetDefaultConfiguration()
        {
            try
            {
                // 尝试从App.config读取默认值
                var config = new SerialPortConfiguration();

                config.ComPort = int.Parse(System.Configuration.ConfigurationManager.AppSettings["DefaultComPort"] ?? "1");
                config.BaudRate = int.Parse(System.Configuration.ConfigurationManager.AppSettings["DefaultBaudRate"] ?? "115200");
                config.DataBits = int.Parse(System.Configuration.ConfigurationManager.AppSettings["DefaultDataBits"] ?? "8");
                config.Timeout = int.Parse(System.Configuration.ConfigurationManager.AppSettings["SerialTimeout"] ?? "5000");
                config.RetryCount = int.Parse(System.Configuration.ConfigurationManager.AppSettings["SerialRetryCount"] ?? "3");

                // 解析停止位
                string stopBitsStr = System.Configuration.ConfigurationManager.AppSettings["DefaultStopBits"] ?? "1";
                config.StopBits = stopBitsStr switch
                {
                    "1" => StopBits.One,
                    "1.5" => StopBits.OnePointFive,
                    "2" => StopBits.Two,
                    _ => StopBits.One
                };

                // 解析校验位
                string parityStr = System.Configuration.ConfigurationManager.AppSettings["DefaultParity"] ?? "None";
                config.Parity = parityStr.ToLower() switch
                {
                    "none" => Parity.None,
                    "odd" => Parity.Odd,
                    "even" => Parity.Even,
                    "mark" => Parity.Mark,
                    "space" => Parity.Space,
                    _ => Parity.None
                };

                // 解析流控制
                string handshakeStr = System.Configuration.ConfigurationManager.AppSettings["DefaultHandshake"] ?? "None";
                config.Handshake = handshakeStr.ToLower() switch
                {
                    "none" => Handshake.None,
                    "xonxoff" => Handshake.XOnXOff,
                    "rts" => Handshake.RequestToSend,
                    "rtsxonxoff" => Handshake.RequestToSendXOnXOff,
                    _ => Handshake.None
                };

                return config;
            }
            catch (Exception ex)
            {
                // 如果App.config读取失败，使用硬编码的安全默认值
                System.Diagnostics.Debug.WriteLine($"无法从App.config读取串口配置，使用硬编码默认值: {ex.Message}");

                return new SerialPortConfiguration
                {
                    ComPort = 1,
                    BaudRate = 115200,
                    DataBits = 8,
                    StopBits = StopBits.One,
                    Parity = Parity.None,
                    Handshake = Handshake.None,
                    Timeout = 5000,
                    RetryCount = 3
                };
            }
        }

        /// <summary>
        /// 验证配置的有效性
        /// </summary>
        public bool IsValid()
        {
            return ComPort > 0 && ComPort <= 256 &&
                   BaudRate > 0 &&
                   DataBits >= 5 && DataBits <= 8 &&
                   Timeout > 0 &&
                   RetryCount >= 0;
        }

        /// <summary>
        /// 获取配置摘要信息
        /// </summary>
        public string GetSummary()
        {
            return $"COM{ComPort}, {BaudRate}bps, {DataBits}位数据, {StopBits}停止位, {Parity}校验, {Handshake}流控, {Timeout}ms超时, {RetryCount}次重试";
        }

        /// <summary>
        /// 获取COM端口名称
        /// </summary>
        public string GetPortName()
        {
            return $"COM{ComPort}";
        }
    }
}
