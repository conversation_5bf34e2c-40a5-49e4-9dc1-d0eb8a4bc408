using System;

namespace WaferAligner.Infrastructure.Common
{
    /// <summary>
    /// 开发模式检查帮助类
    /// 提供统一的开发模式检查逻辑，避免代码重复和不一致
    /// </summary>
    public static class DevelopmentModeHelper
    {
        private static bool? _cachedResult = null;
        private static readonly object _lock = new object();

        /// <summary>
        /// 检查是否为开发模式
        /// 使用缓存机制，避免重复检查配置
        /// </summary>
        /// <returns>如果是开发模式返回true，否则返回false</returns>
        public static bool IsDevelopmentMode()
        {
            if (_cachedResult.HasValue)
                return _cachedResult.Value;

            lock (_lock)
            {
                if (_cachedResult.HasValue)
                    return _cachedResult.Value;

                try
                {
                    // 优先检查配置文件设置
                    bool configDev = System.Configuration.ConfigurationManager.AppSettings["DevelopmentMode"] == "true";

                    // 检查环境变量设置
                    bool envDev = Environment.GetEnvironmentVariable("WAFER_ALIGNER_DEV_MODE") == "true";

                    // 统一的开发模式判断逻辑
                    // 只有在明确配置为开发模式时才启用，避免生产环境误判
                    // 不使用Debugger.IsAttached检查，防止生产环境调试时误判
                    bool isDev = configDev || envDev;

                    _cachedResult = isDev;

                    // 记录开发模式状态，便于调试
                    if (isDev)
                    {
                        System.Diagnostics.Debug.WriteLine($"[DevelopmentModeHelper] 开发模式已启用: Config={configDev}, Env={envDev}");
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"[DevelopmentModeHelper] 生产模式: Config={configDev}, Env={envDev}");
                    }

                    return isDev;
                }
                catch (Exception ex)
                {
                    // 如果配置读取失败，默认为生产模式
                    System.Diagnostics.Debug.WriteLine($"[DevelopmentModeHelper] 配置读取失败，默认为生产模式: {ex.Message}");
                    _cachedResult = false;
                    return false;
                }
            }
        }

        /// <summary>
        /// 强制重新检查开发模式状态
        /// 用于配置更改后刷新缓存
        /// </summary>
        public static void RefreshCache()
        {
            lock (_lock)
            {
                _cachedResult = null;
            }
        }

        /// <summary>
        /// 获取开发模式配置来源的详细信息
        /// 用于调试和日志记录
        /// </summary>
        /// <returns>配置来源信息</returns>
        public static string GetConfigurationSource()
        {
            try
            {
                bool configDev = System.Configuration.ConfigurationManager.AppSettings["DevelopmentMode"] == "true";
                bool envDev = Environment.GetEnvironmentVariable("WAFER_ALIGNER_DEV_MODE") == "true";

                if (configDev && envDev)
                    return "App.config + 环境变量";
                else if (configDev)
                    return "App.config";
                else if (envDev)
                    return "环境变量";
                else
                    return "默认(生产模式)";
            }
            catch (Exception ex)
            {
                return $"配置读取失败: {ex.Message}";
            }
        }
    }
}
