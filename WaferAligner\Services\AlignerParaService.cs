using AlignerUI;
using WaferAligner.Services.Logging.Abstractions;
using WaferAligner.Services.Logging.Extensions;
using Microsoft.Extensions.Logging;
using System;
using System.Reflection;
using System.Threading.Tasks;
using WaferAligner.EventIds;
using Microsoft.Extensions.Configuration;

namespace WaferAligner.Services
{
    /// <summary>
    /// 对准机参数服务实现类
    /// 提供对AlignerPara的访问和操作，替代对ConstValue.ALIGNERPARA的直接访问
    /// </summary>
    public class AlignerParaService : IAlignerParaService
    {
        private readonly ILoggingService _loggingService;
        private readonly IConfiguration _configuration;
        private AlignerPara _current;
        private static readonly object _lock = new object();
        
        // 添加配置项，用于控制是否启用与ConstValue的兼容交互
        private readonly bool _enableLegacyCompatibility;

        /// <summary>
        /// 获取当前AlignerPara实例
        /// </summary>
        public AlignerPara Current 
        { 
            get 
            {
                if (_current == null)
                {
                    lock (_lock)
                    {
                        if (_current == null)
                        {
                            _current = new AlignerPara();
                            //// 只有在启用兼容模式时才尝试从旧系统导入
                            //if (_enableLegacyCompatibility)
                            //{
                            //    InitializeFromLegacy();
                            //}
                        }
                    }
                }
                return _current;
            }
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="loggingService">日志服务</param>
        /// <param name="configuration">配置服务</param>
        public AlignerParaService(ILoggingService loggingService, IConfiguration configuration = null)
        {
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
            _configuration = configuration;
            
            // 默认禁用兼容模式，可通过配置启用（但通常不建议启用）
            _enableLegacyCompatibility = _configuration?.GetValue<bool>("EnableLegacyCompatibility", false) ?? false;
            
            // 初始化AlignerPara实例，但不立即同步数据
            _current = new AlignerPara();
            
            // 注意：不在构造函数中调用InitializeFromLegacy，而是延迟到实际需要时
            // 避免循环依赖问题
            _loggingService.LogDebug($"AlignerParaService已创建，兼容模式: {_enableLegacyCompatibility}");
        }

        /// <summary>
        /// 更新AlignerPara实例
        /// </summary>
        /// <param name="alignerPara">新的AlignerPara实例</param>
        public async Task UpdateAsync(AlignerPara alignerPara)
        {
            if (alignerPara == null)
            {
                throw new ArgumentNullException(nameof(alignerPara));
            }

            await Task.Run(() =>
            {
                lock (_lock)
                {
                    // 复制属性值
                    CopyProperties(alignerPara, _current);
                    
                    //// 只有在启用兼容模式时才同步到旧系统
                    //if (_enableLegacyCompatibility)
                    //{
                    //    // 同步到旧的ConstValue.ALIGNERPARA（向后兼容）
                    //    SyncToLegacy();
                    //}
                }
            });
            
            _loggingService.LogInformation("AlignerPara参数已更新", WaferAligner.EventIds.EventIds.Configuration_Saved);
        }

        /// <summary>
        /// 从配方服务同步参数
        /// </summary>
        /// <param name="recipeService">配方服务</param>
        public async Task SyncFromRecipeServiceAsync(IRecipeService recipeService)
        {
            if (recipeService == null)
            {
                throw new ArgumentNullException(nameof(recipeService));
            }

            await Task.Run(() =>
            {
                lock (_lock)
                {
                    // 基本信息
                    _current.ProductName = recipeService.ProductName;
                    _current.ProductSize = recipeService.ProductSize;
                    _current.SpacerThick = recipeService.SpacerThick;
                    _current.MarkDistance = recipeService.MarkDistance;
                    _current.Material = recipeService.Material;
                    _current.VisualNumber = recipeService.VisualNumber;
                    
                    // 上晶圆参数
                    _current.TopWaferPhotoLX = recipeService.TopWaferPhotoLX;
                    _current.TopWaferPhotoLY = recipeService.TopWaferPhotoLY;
                    _current.TopWaferPhotoLZ = recipeService.TopWaferPhotoLZ;
                    _current.TopWaferPhotoRX = recipeService.TopWaferPhotoRX;
                    _current.TopWaferPhotoRY = recipeService.TopWaferPhotoRY;
                    _current.TopWaferPhotoRZ = recipeService.TopWaferPhotoRZ;
                    _current.TopWaferPhotoZ = recipeService.TopWaferPhotoZ;
                    _current.TopWaferPhotoX = recipeService.TopWaferPhotoX;
                    _current.TopWaferPhotoY = recipeService.TopWaferPhotoY;
                    _current.TopWaferPhotoR = recipeService.TopWaferPhotoR;
                    _current.IsTopHorizontalAdjust = recipeService.IsTopHorizontalAdjust;
                    _current.IsTopHorizontalPhoto = recipeService.IsTopHorizontalPhoto;
                    _current.TopWaferThick = recipeService.TopWaferThick;
                    
                    // 下晶圆参数
                    _current.BottomWaferPhotoZ = recipeService.BottomWaferPhotoZ;
                    _current.BottomWaferPhotoLX = recipeService.BottomWaferPhotoLX;
                    _current.BottomWaferPhotoLY = recipeService.BottomWaferPhotoLY;
                    _current.BottomWaferPhotoLZ = recipeService.BottomWaferPhotoLZ;
                    _current.BottomWaferPhotoRX = recipeService.BottomWaferPhotoRX;
                    _current.BottomWaferPhotoRY = recipeService.BottomWaferPhotoRY;
                    _current.BottomWaferPhotoRZ = recipeService.BottomWaferPhotoRZ;
                    _current.BottomWaferPhotoX = recipeService.BottomWaferPhotoX;
                    _current.BottomWaferPhotoY = recipeService.BottomWaferPhotoY;
                    _current.BottomWaferPhotoR = recipeService.BottomWaferPhotoR;
                    _current.BottomWaferThick = recipeService.BottomWaferThick;
                    _current.BottomWaferPhotoFitZ = recipeService.BottomWaferPhotoFitZ;
                    
                    //// 只有在启用兼容模式时才同步到旧系统
                    //if (_enableLegacyCompatibility)
                    //{
                    //    // 同步到旧的ConstValue.ALIGNERPARA（向后兼容）
                    //    SyncToLegacy();
                    //}
                }
            });
            
            _loggingService.LogInformation("从配方服务同步参数到AlignerPara完成", WaferAligner.EventIds.EventIds.Configuration_Loaded);
        }

        /// <summary>
        /// 同步参数到配方服务
        /// </summary>
        /// <param name="recipeService">配方服务</param>
        public async Task SyncToRecipeServiceAsync(IRecipeService recipeService)
        {
            if (recipeService == null)
            {
                throw new ArgumentNullException(nameof(recipeService));
            }

            await Task.Run(() =>
            {
                lock (_lock)
                {
                    // 基本信息
                    recipeService.ProductName = _current.ProductName;
                    recipeService.ProductSize = _current.ProductSize;
                    recipeService.SpacerThick = _current.SpacerThick;
                    recipeService.MarkDistance = _current.MarkDistance;
                    recipeService.Material = _current.Material;
                    recipeService.VisualNumber = _current.VisualNumber;
                    
                    // 上晶圆参数
                    recipeService.TopWaferPhotoLX = _current.TopWaferPhotoLX;
                    recipeService.TopWaferPhotoLY = _current.TopWaferPhotoLY;
                    recipeService.TopWaferPhotoLZ = _current.TopWaferPhotoLZ;
                    recipeService.TopWaferPhotoRX = _current.TopWaferPhotoRX;
                    recipeService.TopWaferPhotoRY = _current.TopWaferPhotoRY;
                    recipeService.TopWaferPhotoRZ = _current.TopWaferPhotoRZ;
                    recipeService.TopWaferPhotoZ = _current.TopWaferPhotoZ;
                    recipeService.TopWaferPhotoX = _current.TopWaferPhotoX;
                    recipeService.TopWaferPhotoY = _current.TopWaferPhotoY;
                    recipeService.TopWaferPhotoR = _current.TopWaferPhotoR;
                    recipeService.IsTopHorizontalAdjust = _current.IsTopHorizontalAdjust;
                    recipeService.IsTopHorizontalPhoto = _current.IsTopHorizontalPhoto;
                    recipeService.TopWaferThick = _current.TopWaferThick;
                    
                    // 下晶圆参数
                    recipeService.BottomWaferPhotoZ = _current.BottomWaferPhotoZ;
                    recipeService.BottomWaferPhotoLX = _current.BottomWaferPhotoLX;
                    recipeService.BottomWaferPhotoLY = _current.BottomWaferPhotoLY;
                    recipeService.BottomWaferPhotoLZ = _current.BottomWaferPhotoLZ;
                    recipeService.BottomWaferPhotoRX = _current.BottomWaferPhotoRX;
                    recipeService.BottomWaferPhotoRY = _current.BottomWaferPhotoRY;
                    recipeService.BottomWaferPhotoRZ = _current.BottomWaferPhotoRZ;
                    recipeService.BottomWaferPhotoX = _current.BottomWaferPhotoX;
                    recipeService.BottomWaferPhotoY = _current.BottomWaferPhotoY;
                    recipeService.BottomWaferPhotoR = _current.BottomWaferPhotoR;
                    recipeService.BottomWaferThick = _current.BottomWaferThick;
                    recipeService.BottomWaferPhotoFitZ = _current.BottomWaferPhotoFitZ;
                }
            });
            
            _loggingService.LogInformation("从AlignerPara同步参数到配方服务完成", WaferAligner.EventIds.EventIds.Configuration_Saved);
        }

        #region 向后兼容方法
        /// <summary>
        /// 从旧的ConstValue.ALIGNERPARA初始化（向后兼容）
        /// 此方法已弃用，仅保留用于代码参考
        /// 
        /// 原始实现:
        /// 通过反射获取ConstValue.ALIGNERPARA静态属性，并将其属性值复制到当前实例
        /// 1. 使用Type.GetType("WaferAligner.ConstValue, WaferAligner")获取ConstValue类型
        /// 2. 通过反射获取ALIGNERPARA静态属性：legacyType.GetProperty("ALIGNERPARA")
        /// 3. 获取属性值：alignerParaProperty.GetValue(null)
        /// 4. 复制属性值到当前实例：CopyProperties(legacy, _current)
        /// 
        /// 由于兼容性已保证，此功能已于2025年5月禁用
        /// </summary>
        [Obsolete("此方法已被禁用，仅保留用于代码参考，实际运行不会有任何效果", true)]
        private void InitializeFromLegacy()
        {
            // 创建新实例，不再从旧系统导入数据
            _current = new AlignerPara();
            _loggingService?.LogInformation("已创建新的AlignerPara实例", WaferAligner.EventIds.EventIds.Configuration_Loaded);
            
            // 注释：
            // 此方法原本通过反射从ConstValue.ALIGNERPARA获取配置数据
            // 但由于移除静态兼容层，此功能已禁用
            // 详细说明参见方法的XML文档注释
        }

        /// <summary>
        /// 同步到旧的ConstValue.ALIGNERPARA（向后兼容）
        /// 此方法已弃用，仅保留用于代码参考
        /// 
        /// 原始实现:
        /// 通过反射获取ConstValue.ALIGNERPARA静态属性，并将当前实例的属性值复制到静态实例
        /// 1. 使用Type.GetType("WaferAligner.ConstValue, WaferAligner")获取ConstValue类型
        /// 2. 通过反射获取ALIGNERPARA静态属性：legacyType.GetProperty("ALIGNERPARA")
        /// 3. 获取属性值：alignerParaProperty.GetValue(null)
        /// 4. 复制当前实例属性值到旧系统：CopyProperties(_current, legacy)
        /// 
        /// 由于兼容性已保证，此功能已于2025年5月禁用
        /// </summary>
        [Obsolete("此方法已被禁用，仅保留用于代码参考，实际运行不会有任何效果", true)]
        private void SyncToLegacy()
        {
            // 此方法不再执行任何操作
            _loggingService?.LogDebug("旧配置同步功能已禁用", WaferAligner.EventIds.EventIds.Configuration_Saved);
            
            // 注释：
            // 此方法原本通过反射将当前数据同步到ConstValue.ALIGNERPARA
            // 但由于移除静态兼容层，此功能已禁用
            // 详细说明参见方法的XML文档注释
        }
        #endregion

        #region 辅助方法
        /// <summary>
        /// 复制对象属性值
        /// </summary>
        /// <param name="source">源对象</param>
        /// <param name="target">目标对象</param>
        private void CopyProperties(object source, object target)
        {
            if (source == null || target == null)
            {
                return;
            }

            try
            {
                var sourceType = source.GetType();
                var targetType = target.GetType();
                
                // 获取源对象的所有公共属性
                var properties = sourceType.GetProperties(BindingFlags.Public | BindingFlags.Instance);
                
                foreach (var property in properties)
                {
                    if (!property.CanRead)
                    {
                        continue;
                    }
                    
                    // 查找目标对象中的同名属性
                    var targetProperty = targetType.GetProperty(property.Name);
                    if (targetProperty == null || !targetProperty.CanWrite)
                    {
                        continue;
                    }
                    
                    // 检查类型兼容性
                    if (!targetProperty.PropertyType.IsAssignableFrom(property.PropertyType))
                    {
                        continue;
                    }
                    
                    try
                    {
                        // 获取源属性值并设置到目标属性
                        var value = property.GetValue(source);
                        targetProperty.SetValue(target, value);
                    }
                    catch (Exception ex)
                    {
                        _loggingService.LogDebug($"复制属性{property.Name}失败: {ex.Message}", WaferAligner.EventIds.EventIds.Configuration_Error);
                    }
                }
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, "复制对象属性失败", WaferAligner.EventIds.EventIds.Configuration_Error);
            }
        }
        #endregion
    }
} 