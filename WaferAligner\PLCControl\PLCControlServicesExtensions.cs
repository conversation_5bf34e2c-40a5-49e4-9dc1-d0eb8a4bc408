using WaferAligner.Communication.Inovance.Abstractions;
using WaferAligner.Communication.Inovance.Management;
using WaferAligner.Services.Abstractions;
using Microsoft.Extensions.DependencyInjection;
using WaferAligner.Communication.Inovance;
using System;

namespace WaferAligner.PLCControl
{
    /// <summary>
    /// PLC控制服务扩展
    /// </summary>
    public static class PLCControlServicesExtensions
    {
        /// <summary>
        /// 注册PLC控制相关服务
        /// </summary>
        /// <param name="services">服务集合</param>
        /// <returns>服务集合</returns>
        public static IServiceCollection AddPLCControlServices(this IServiceCollection services)
        {
            if (services == null)
                throw new ArgumentNullException(nameof(services));
                
            // PLC连接管理器
            services.AddSingleton<IPlcConnectionManager, PlcConnectionManager>();
            
            // 注册PLC实例
            services.AddSingleton<IPlcInstance, InvoancePlcInstance>();
            
            // PLC变量服务
            services.AddSingleton<IPlcVariableService, PlcVariableService>();
            
            // PLC通信服务
            services.AddSingleton<IPlcCommunication, PlcCommunication>();
            
            return services;
        }
        
        /// <summary>
        /// 注册PLC监控相关服务
        /// </summary>
        /// <param name="services">服务集合</param>
        /// <returns>服务集合</returns>
        public static IServiceCollection AddPLCMonitoring(this IServiceCollection services)
        {
            if (services == null)
                throw new ArgumentNullException(nameof(services));
            
            // 这里可以添加PLC监控相关服务
            // 例如数据采集、告警监控等
            
            return services;
        }
    }
} 