using WaferAligner.Services.UserManagement;
using WaferAligner.Core.Business;
using Sunny.UI;
using System;
using System.Linq;
using System.Windows.Forms;

namespace WaferAligner.Forms.Pages
{
    public partial class FAddUserDialog : UIForm
    {
        public UserInfo NewUser { get; private set; }
        private IUserManagement _userManagement;

        public FAddUserDialog(IUserManagement userManagement)
        {
            InitializeComponent();
            _userManagement = userManagement;
            InitializeForm();
        }

        private void InitializeForm()
        {
            this.Text = "新增用户";
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.StartPosition = FormStartPosition.CenterParent;
            this.Size = new System.Drawing.Size(420, 360);

            // 加载可用角色
            LoadAvailableRoles();
            
            // 设置默认值
            if (cmbRoles.Items.Count > 2)
                cmbRoles.SelectedIndex = 2; // 默认选择操作员
        }

        private void LoadAvailableRoles()
        {
            try
            {
                var roles = _userManagement.GetAllRoles();
                cmbRoles.Items.Clear();
                
                foreach (var role in roles)
                {
                    cmbRoles.Items.Add(role.Name);
                }
                
                if (cmbRoles.Items.Count > 0)
                {
                    cmbRoles.SelectedIndex = 0;
                }
            }
            catch (Exception ex)
            {
                UIMessageBox.ShowError($"加载角色列表失败: {ex.Message}");
            }
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if (ValidateInput())
            {
                CreateUser();
            }
        }

        private bool ValidateInput()
        {
            // 验证用户名
            if (string.IsNullOrWhiteSpace(txtUsername.Text))
            {
                UIMessageBox.ShowWarning("请输入用户名");
                txtUsername.Focus();
                return false;
            }

            if (txtUsername.Text.Length < 3 || txtUsername.Text.Length > 20)
            {
                UIMessageBox.ShowWarning("用户名长度必须在3-20个字符之间");
                txtUsername.Focus();
                return false;
            }

            // 验证用户名格式（只允许字母、数字和下划线）
            if (!System.Text.RegularExpressions.Regex.IsMatch(txtUsername.Text, @"^[a-zA-Z0-9_]+$"))
            {
                UIMessageBox.ShowWarning("用户名只能包含字母、数字和下划线");
                txtUsername.Focus();
                return false;
            }

            // 验证用户名是否已存在
            if (_userManagement.GetUserInfo(txtUsername.Text) != null)
            {
                UIMessageBox.ShowWarning("用户名已存在，请使用其他用户名");
                txtUsername.Focus();
                return false;
            }

            // 验证密码
            if (string.IsNullOrWhiteSpace(txtPassword.Text))
            {
                UIMessageBox.ShowWarning("请输入密码");
                txtPassword.Focus();
                return false;
            }

            if (txtPassword.Text.Length < 6 || txtPassword.Text.Length > 50)
            {
                UIMessageBox.ShowWarning("密码长度必须在6-50个字符之间");
                txtPassword.Focus();
                return false;
            }

            // 验证密码强度（至少包含一个字母和一个数字）
            if (!System.Text.RegularExpressions.Regex.IsMatch(txtPassword.Text, @"^(?=.*[A-Za-z])(?=.*\d).+$"))
            {
                UIMessageBox.ShowWarning("密码必须包含至少一个字母和一个数字");
                txtPassword.Focus();
                return false;
            }

            // 验证确认密码
            if (txtPassword.Text != txtConfirmPassword.Text)
            {
                UIMessageBox.ShowWarning("两次输入的密码不一致");
                txtConfirmPassword.Focus();
                return false;
            }

            // 验证角色选择
            if (cmbRoles.SelectedIndex == -1)
            {
                UIMessageBox.ShowWarning("请选择用户角色");
                cmbRoles.Focus();
                return false;
            }

            return true;
        }

        private void CreateUser()
        {
            try
            {
                NewUser = new UserInfo
                {
                    Username = txtUsername.Text.Trim(),
                    Roles = new[] { cmbRoles.SelectedItem.ToString() },
                    Description = txtDescription.Text.Trim(),
                    IsActive = true, // 新用户默认激活
                    CreatedDate = DateTime.Now
                };

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                UIMessageBox.ShowError($"创建用户信息失败: {ex.Message}");
            }
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private void txtUsername_TextChanged(object sender, EventArgs e)
        {
            // 实时检查用户名是否存在
            if (!string.IsNullOrWhiteSpace(txtUsername.Text) && txtUsername.Text.Length >= 3)
            {
                if (_userManagement.GetUserInfo(txtUsername.Text) != null)
                {
                    lblUsernameHint.Text = "用户名已存在";
                    lblUsernameHint.ForeColor = System.Drawing.Color.Red;
                }
                else
                {
                    lblUsernameHint.Text = "用户名可用";
                    lblUsernameHint.ForeColor = System.Drawing.Color.Green;
                }
            }
            else
            {
                lblUsernameHint.Text = "";
            }
        }

        private void cmbRoles_SelectedIndexChanged(object sender, EventArgs e)
        {
            // 显示选中角色的权限说明
            if (cmbRoles.SelectedItem != null)
            {
                string roleName = cmbRoles.SelectedItem.ToString();
                string roleDescription = GetRoleDescription(roleName);
                lblRoleDescription.Text = roleDescription;
            }
        }

        private string GetRoleDescription(string roleName)
        {
            switch (roleName)
            {
                case "Admin":
                    return "管理员：拥有所有系统权限，可以管理用户和配置所有参数";
                case "Engineer":
                    return "工程师：可以管理用户、配置对准参数和运动参数";
                case "Operator":
                    return "操作员：只能进行基本的键合对准操作";
                default:
                    return "未知角色";
            }
        }

        public string GetPassword()
        {
            return txtPassword.Text;
        }
    }
} 