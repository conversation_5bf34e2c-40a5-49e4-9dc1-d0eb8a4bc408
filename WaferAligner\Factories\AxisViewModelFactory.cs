using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using WaferAligner.Communication.Inovance.Abstractions;
using WaferAligner.Communication.Inovance.Management;
using WaferAligner.Communication.Inovance.Constants;
// using AlignerUI; // 已移除，不再需要旧的CameralHoldAxisViewModel
using WaferAligner.Interfaces;
using WaferAligner.Models;
using WaferAligner.Common;
using WaferAligner.Services.Logging.Abstractions;
using WaferAligner.Services.Logging.Extensions;
using WaferAligner.Services;
using Microsoft.Extensions.DependencyInjection;
using WaferAligner.Communication.Serial.Interfaces;
using System.Configuration;
using AlignerUI;

namespace WaferAligner.Factories
{
    /// <summary>
    /// 轴ViewModel工厂实现
    /// </summary>
    public class AxisViewModelFactory : IAxisViewModelFactory
    {
        private readonly IPlcConnectionManager _plcManager;
        private readonly ILoggingService _loggingService;
        private readonly IServiceProvider _serviceProvider;
        private AxisViewModelCollection _axisCollection;

        // 单例模式缓存字段
        private IXyrAxisViewModel _xAxisInstance;
        private IXyrAxisViewModel _yAxisInstance;
        private IXyrAxisViewModel _rAxisInstance;
        private IZAxisViewModel _zAxisInstance;
        private ICameraAxisViewModel _lxAxisInstance;
        private ICameraAxisViewModel _lyAxisInstance;
        private ICameraAxisViewModel _lzAxisInstance;
        private ICameraAxisViewModel _rxAxisInstance;
        private ICameraAxisViewModel _ryAxisInstance;
        private ICameraAxisViewModel _rzAxisInstance;
        private readonly object _lock = new object();

        // 添加静态变量用于控制各轴连接日志记录频率
        private static bool _xAxisConnectLogged = false;
        private static bool _yAxisConnectLogged = false;
        private static bool _rAxisConnectLogged = false;
        private static DateTime _lastXAxisConnectLogTime = DateTime.MinValue;
        private static DateTime _lastYAxisConnectLogTime = DateTime.MinValue;
        private static DateTime _lastRAxisConnectLogTime = DateTime.MinValue;
        
        // 添加串口连接日志抑制相关字段
        private static DateTime _lastSerialConnectLogTime = DateTime.MinValue;
        private static int _serialConnectFailCount = 0;
        private static readonly object _serialLogLock = new object();
        private const int MaxSerialConnectFailLogCount = 3; // 最大连续失败日志次数
        private const int SerialConnectLogIntervalSeconds = 30; // 日志间隔（秒）

        // 添加配置选项，允许切换使用SerialControl
        private readonly bool _useSerialControl;

        public AxisViewModelFactory(
            IPlcConnectionManager plcManager, 
            ILoggingService loggingService,
            IServiceProvider serviceProvider)
        {
            _plcManager = plcManager ?? throw new ArgumentNullException(nameof(plcManager));
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
            _serviceProvider = serviceProvider;
            
            // 从配置读取是否使用串口控制
            // 注意：此配置已废弃，现在始终使用SerialAxisViewModel
            _useSerialControl = true; // 强制使用SerialControl
            
            _loggingService.LogInformation("轴控制工厂使用串口控制模式", WaferAligner.EventIds.EventIds.Axis_Instance_Created_General);
        }

        #region XYR轴创建
        public async Task<IXyrAxisViewModel> CreateXAxisAsync()
        {
            try
            {
                _loggingService.LogDebug("创建X轴ViewModel", WaferAligner.EventIds.EventIds.Axis_Move_Started);
                
                // 现在始终使用SerialAxisViewModel
                return await CreateSerialAxisViewModelAsync("X");
            }
            catch (OutOfMemoryException ex)
            {
                _loggingService.LogError(ex, "创建X轴ViewModel时内存不足", WaferAligner.EventIds.EventIds.Axis_Move_Error);
                // 内存不足是严重错误，需要通知应用程序可能需要重启
                throw new InvalidOperationException("创建轴实例时内存不足，请重启应用程序", ex);
            }
            catch (ObjectDisposedException ex)
            {
                _loggingService.LogError(ex, "创建X轴ViewModel时对象已释放", WaferAligner.EventIds.EventIds.Axis_Move_Error);
                throw new InvalidOperationException("创建轴实例时发现对象已释放，请重试操作", ex);
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, "创建X轴ViewModel失败", WaferAligner.EventIds.EventIds.Axis_Move_Error);
                throw;
            }
        }

        public async Task<IXyrAxisViewModel> CreateYAxisAsync()
        {
            try
            {
                _loggingService.LogDebug("创建Y轴ViewModel", WaferAligner.EventIds.EventIds.Axis_Move_Started);
                
                // 现在始终使用SerialAxisViewModel
                return await CreateSerialAxisViewModelAsync("Y");
            }
            catch (OutOfMemoryException ex)
            {
                _loggingService.LogError(ex, "创建Y轴ViewModel时内存不足", WaferAligner.EventIds.EventIds.Axis_Move_Error);
                throw new InvalidOperationException("创建轴实例时内存不足，请重启应用程序", ex);
            }
            catch (ObjectDisposedException ex)
            {
                _loggingService.LogError(ex, "创建Y轴ViewModel时对象已释放", WaferAligner.EventIds.EventIds.Axis_Move_Error);
                throw new InvalidOperationException("创建轴实例时发现对象已释放，请重试操作", ex);
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, "创建Y轴ViewModel失败", WaferAligner.EventIds.EventIds.Axis_Move_Error);
                throw;
            }
        }

        public async Task<IXyrAxisViewModel> CreateRAxisAsync()
        {
            try
            {
                _loggingService.LogDebug("创建R轴ViewModel", WaferAligner.EventIds.EventIds.Axis_Move_Started);
                
                // 现在始终使用SerialAxisViewModel
                return await CreateSerialAxisViewModelAsync("R");
            }
            catch (OutOfMemoryException ex)
            {
                _loggingService.LogError(ex, "创建R轴ViewModel时内存不足", WaferAligner.EventIds.EventIds.Axis_Move_Error);
                throw new InvalidOperationException("创建轴实例时内存不足，请重启应用程序", ex);
            }
            catch (ObjectDisposedException ex)
            {
                _loggingService.LogError(ex, "创建R轴ViewModel时对象已释放", WaferAligner.EventIds.EventIds.Axis_Move_Error);
                throw new InvalidOperationException("创建轴实例时发现对象已释放，请重试操作", ex);
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, "创建R轴ViewModel失败", WaferAligner.EventIds.EventIds.Axis_Move_Error);
                throw;
            }
        }
        
        /// <summary>
        /// 创建使用SerialAxisController的SerialAxisViewModel
        /// </summary>
        private async Task<IXyrAxisViewModel> CreateSerialAxisViewModelAsync(string axisName)
        {
            try
            {
                _loggingService.LogDebug($"使用串口控制创建{axisName}轴ViewModel", WaferAligner.EventIds.EventIds.Axis_Move_Started);
                
                // 获取SerialAxisControllerFactory
                var factory = _serviceProvider?.GetService<ISerialAxisControllerFactory>();
                if (factory == null)
                {
                    _loggingService.LogError($"无法获取串口轴控制器工厂，创建空实现", WaferAligner.EventIds.EventIds.Service_Unavailable);
                    // 创建空实现
                    var emptyController = _serviceProvider?.GetService<ISerialAxisController>() ?? 
                        new EmptySerialAxisController(axisName, _loggingService);
                    return new SerialAxisViewModel(emptyController, _loggingService);
                }
                
                // 通过工厂创建轴控制器
                var controller = await factory.CreateAxisControllerAsync(axisName);
                if (controller == null)
                {
                    _loggingService.LogError($"创建{axisName}轴控制器失败，创建空实现", WaferAligner.EventIds.EventIds.Service_Unavailable);
                    // 创建空实现
                    var emptyController = new EmptySerialAxisController(axisName, _loggingService);
                    return new SerialAxisViewModel(emptyController, _loggingService);
                }
                
                // 使用控制器创建SerialAxisViewModel
                var viewModel = new SerialAxisViewModel(controller, _loggingService);
                
                // 连接和初始化
                bool connected = await viewModel.ConnectAsync("", 0);
                if (connected)
                {
                    // 重置连接失败计数
                    lock (_serialLogLock)
                    {
                        _serialConnectFailCount = 0;
                    }
                    _loggingService.LogInformation($"使用串口控制连接{axisName}轴成功", WaferAligner.EventIds.EventIds.Plc_Connection_Succeeded);
                }
                else if (ShouldLogSerialConnectFailure(axisName))
                {
                    _loggingService.LogWarning($"使用串口控制连接{axisName}轴失败", WaferAligner.EventIds.EventIds.Plc_Connection_Failed);
                }
                
                return viewModel;
            }
            catch (Exception ex)
            {
                if (ShouldLogSerialConnectFailure(axisName))
                {
                    _loggingService.LogError(ex, $"使用串口控制创建{axisName}轴ViewModel失败", WaferAligner.EventIds.EventIds.Axis_Move_Error);
                    _loggingService.LogWarning($"创建空实现", WaferAligner.EventIds.EventIds.Plc_Connection_Failed);
                }
                
                // 创建空实现
                var emptyController = new EmptySerialAxisController(axisName, _loggingService);
                return new SerialAxisViewModel(emptyController, _loggingService);
            }
        }
        #endregion

        #region Z轴创建
        public async Task<IZAxisViewModel> CreateZAxisAsync()
        {
            try
            {
                _loggingService.LogDebug("创建Z轴ViewModel", WaferAligner.EventIds.EventIds.Axis_Move_Started);

                // 优先使用新的ZAxisViewModelNew实现
                if (_serviceProvider != null)
                {
                    try
                    {
                        // 使用依赖注入获取ZAxisViewModelNew实例
                        var zAxisViewModel = _serviceProvider.GetRequiredService<ZAxisViewModelNew>();

                        // 初始化
                        await zAxisViewModel.InitializeAsync();

                        return zAxisViewModel;
                    }
                    catch (Exception serviceEx)
                    {
                        _loggingService.LogWarning($"使用依赖注入创建Z轴失败: {serviceEx.Message}，尝试直接创建", WaferAligner.EventIds.EventIds.Axis_Instance_Create_Failed);
                    }
                }

                // 如果依赖注入失败，直接创建实例
                var plcCommunication = _serviceProvider?.GetService<IPlcCommunication>();
                var axisEventService = _serviceProvider?.GetService<IAxisEventService>();

                if (plcCommunication != null && axisEventService != null)
                {
                    var directViewModel = new ZAxisViewModelNew(plcCommunication, _loggingService, axisEventService);
                    await directViewModel.InitializeAsync();
                    return directViewModel;
                }

                // 所有创建方式都失败，抛出异常
                var errorMessage = "无法创建Z轴ViewModel：依赖注入失败且直接创建也失败";
                _loggingService.LogError(errorMessage, WaferAligner.EventIds.EventIds.Axis_Instance_Create_Failed);
                throw new InvalidOperationException(errorMessage);
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, "创建Z轴ViewModel失败", WaferAligner.EventIds.EventIds.Axis_Move_Error);
                throw;
            }
        }
        #endregion

        #region 相机支架轴创建
        public async Task<ICameraAxisViewModel> CreateLeftXAxisAsync()
        {
            try
            {
                _loggingService.LogDebug("创建左X轴ViewModel", WaferAligner.EventIds.EventIds.Axis_Move_Started);

                // 优先使用新的CameraAxisViewModelNew实现
                if (_serviceProvider != null)
                {
                    try
                    {
                        // 创建相机轴视图模型并设置参数
                        var cameraAxisViewModel = _serviceProvider.GetRequiredService<CameraAxisViewModelNew>();

                        // 设置相机轴参数
                        cameraAxisViewModel.SetAxis("LX");
                        cameraAxisViewModel.CameraPosition = "Left";

                        // 初始化
                        await cameraAxisViewModel.InitializeAsync();

                        return cameraAxisViewModel;
                    }
                    catch (Exception serviceEx)
                    {
                        _loggingService.LogWarning($"使用依赖注入创建左X轴失败: {serviceEx.Message}，尝试直接创建", WaferAligner.EventIds.EventIds.Axis_Instance_Create_Failed);
                    }
                }

                // 如果依赖注入失败，直接创建实例
                var plcCommunication = _serviceProvider?.GetService<IPlcCommunication>();
                var axisEventService = _serviceProvider?.GetService<IAxisEventService>();

                if (plcCommunication != null && axisEventService != null)
                {
                    var directViewModel = new CameraAxisViewModelNew("LX", "Left", plcCommunication, _loggingService, axisEventService);
                    await directViewModel.InitializeAsync();
                    return directViewModel;
                }

                // 所有创建方式都失败，抛出异常
                var errorMessage = "无法创建左X轴ViewModel：依赖注入失败且直接创建也失败";
                _loggingService.LogError(errorMessage, WaferAligner.EventIds.EventIds.Axis_Instance_Create_Failed);
                throw new InvalidOperationException(errorMessage);
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, "创建左X轴ViewModel失败", WaferAligner.EventIds.EventIds.Axis_Move_Error);
                throw;
            }
        }

        public async Task<ICameraAxisViewModel> CreateLeftYAxisAsync()
        {
            try
            {
                _loggingService.LogDebug("创建左Y轴ViewModel", WaferAligner.EventIds.EventIds.Axis_Move_Started);
                
                // 检查是否应该创建新的基于PLC的相机轴实现
                if (_serviceProvider != null && _serviceProvider.GetService<CameraAxisViewModelNew>() != null)
                {
                    // 创建相机轴视图模型并设置参数
                    var cameraAxisViewModel = _serviceProvider.GetRequiredService<CameraAxisViewModelNew>();
                    
                    // 设置相机轴参数
                    cameraAxisViewModel.SetAxis("LY");
                    cameraAxisViewModel.CameraPosition = "Left";
                    
                    // 初始化
                    await cameraAxisViewModel.InitializeAsync();
                    
                    return cameraAxisViewModel;
                }
                
                // 如果依赖注入失败，直接创建实例
                var plcCommunication = _serviceProvider?.GetService<IPlcCommunication>();
                var axisEventService = _serviceProvider?.GetService<IAxisEventService>();

                if (plcCommunication != null && axisEventService != null)
                {
                    var directViewModel = new CameraAxisViewModelNew("LY", "Left", plcCommunication, _loggingService, axisEventService);
                    await directViewModel.InitializeAsync();
                    return directViewModel;
                }

                // 所有创建方式都失败，抛出异常
                var errorMessage = "无法创建左Y轴ViewModel：依赖注入失败且直接创建也失败";
                _loggingService.LogError(errorMessage, WaferAligner.EventIds.EventIds.Axis_Instance_Create_Failed);
                throw new InvalidOperationException(errorMessage);
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, "创建左Y轴ViewModel失败", WaferAligner.EventIds.EventIds.Axis_Move_Error);
                throw;
            }
        }

        public async Task<ICameraAxisViewModel> CreateLeftZAxisAsync()
        {
            try
            {
                _loggingService.LogDebug("创建左Z轴ViewModel", WaferAligner.EventIds.EventIds.Axis_Move_Started);

                // 优先使用新的CameraAxisViewModelNew实现
                if (_serviceProvider != null)
                {
                    try
                    {
                        // 创建相机轴视图模型并设置参数
                        var cameraAxisViewModel = _serviceProvider.GetRequiredService<CameraAxisViewModelNew>();

                        // 设置相机轴参数
                        cameraAxisViewModel.SetAxis("LZ");
                        cameraAxisViewModel.CameraPosition = "Left";

                        // 初始化
                        await cameraAxisViewModel.InitializeAsync();

                        return cameraAxisViewModel;
                    }
                    catch (Exception serviceEx)
                    {
                        _loggingService.LogWarning($"使用依赖注入创建左Z轴失败: {serviceEx.Message}，尝试直接创建", WaferAligner.EventIds.EventIds.Axis_Instance_Create_Failed);
                    }
                }

                // 如果依赖注入失败，直接创建实例
                var plcCommunication = _serviceProvider?.GetService<IPlcCommunication>();
                var axisEventService = _serviceProvider?.GetService<IAxisEventService>();

                if (plcCommunication != null && axisEventService != null)
                {
                    var directViewModel = new CameraAxisViewModelNew("LZ", "Left", plcCommunication, _loggingService, axisEventService);
                    await directViewModel.InitializeAsync();
                    return directViewModel;
                }

                // 所有创建方式都失败，抛出异常
                var errorMessage = "无法创建左Z轴ViewModel：依赖注入失败且直接创建也失败";
                _loggingService.LogError(errorMessage, WaferAligner.EventIds.EventIds.Axis_Instance_Create_Failed);
                throw new InvalidOperationException(errorMessage);
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, "创建左Z轴ViewModel失败", WaferAligner.EventIds.EventIds.Axis_Move_Error);
                throw;
            }
        }

        public async Task<ICameraAxisViewModel> CreateRightXAxisAsync()
        {
            try
            {
                _loggingService.LogDebug("创建右X轴ViewModel", WaferAligner.EventIds.EventIds.Axis_Move_Started);

                // 优先使用新的CameraAxisViewModelNew实现
                if (_serviceProvider != null)
                {
                    try
                    {
                        // 创建相机轴视图模型并设置参数
                        var cameraAxisViewModel = _serviceProvider.GetRequiredService<CameraAxisViewModelNew>();

                        // 设置相机轴参数
                        cameraAxisViewModel.SetAxis("RX");
                        cameraAxisViewModel.CameraPosition = "Right";

                        // 初始化
                        await cameraAxisViewModel.InitializeAsync();

                        return cameraAxisViewModel;
                    }
                    catch (Exception serviceEx)
                    {
                        _loggingService.LogWarning($"使用依赖注入创建右X轴失败: {serviceEx.Message}，尝试直接创建", WaferAligner.EventIds.EventIds.Axis_Instance_Create_Failed);
                    }
                }

                // 如果依赖注入失败，直接创建实例
                var plcCommunication = _serviceProvider?.GetService<IPlcCommunication>();
                var axisEventService = _serviceProvider?.GetService<IAxisEventService>();

                if (plcCommunication != null && axisEventService != null)
                {
                    var directViewModel = new CameraAxisViewModelNew("RX", "Right", plcCommunication, _loggingService, axisEventService);
                    await directViewModel.InitializeAsync();
                    return directViewModel;
                }

                // 所有创建方式都失败，抛出异常
                var errorMessage = "无法创建右X轴ViewModel：依赖注入失败且直接创建也失败";
                _loggingService.LogError(errorMessage, WaferAligner.EventIds.EventIds.Axis_Instance_Create_Failed);
                throw new InvalidOperationException(errorMessage);
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, "创建右X轴ViewModel失败", WaferAligner.EventIds.EventIds.Axis_Move_Error);
                throw;
            }
        }

        public async Task<ICameraAxisViewModel> CreateRightYAxisAsync()
        {
            try
            {
                _loggingService.LogDebug("创建右Y轴ViewModel", WaferAligner.EventIds.EventIds.Axis_Move_Started);

                // 优先使用新的CameraAxisViewModelNew实现
                if (_serviceProvider != null)
                {
                    try
                    {
                        // 创建相机轴视图模型并设置参数
                        var cameraAxisViewModel = _serviceProvider.GetRequiredService<CameraAxisViewModelNew>();

                        // 设置相机轴参数
                        cameraAxisViewModel.SetAxis("RY");
                        cameraAxisViewModel.CameraPosition = "Right";

                        // 初始化
                        await cameraAxisViewModel.InitializeAsync();

                        return cameraAxisViewModel;
                    }
                    catch (Exception serviceEx)
                    {
                        _loggingService.LogWarning($"使用依赖注入创建右Y轴失败: {serviceEx.Message}，尝试直接创建", WaferAligner.EventIds.EventIds.Axis_Instance_Create_Failed);
                    }
                }

                // 如果依赖注入失败，直接创建实例
                var plcCommunication = _serviceProvider?.GetService<IPlcCommunication>();
                var axisEventService = _serviceProvider?.GetService<IAxisEventService>();

                if (plcCommunication != null && axisEventService != null)
                {
                    var directViewModel = new CameraAxisViewModelNew("RY", "Right", plcCommunication, _loggingService, axisEventService);
                    await directViewModel.InitializeAsync();
                    return directViewModel;
                }

                // 所有创建方式都失败，抛出异常
                var errorMessage = "无法创建右Y轴ViewModel：依赖注入失败且直接创建也失败";
                _loggingService.LogError(errorMessage, WaferAligner.EventIds.EventIds.Axis_Instance_Create_Failed);
                throw new InvalidOperationException(errorMessage);
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, "创建右Y轴ViewModel失败", WaferAligner.EventIds.EventIds.Axis_Move_Error);
                throw;
            }
        }

        public async Task<ICameraAxisViewModel> CreateRightZAxisAsync()
        {
            try
            {
                _loggingService.LogDebug("创建右Z轴ViewModel", WaferAligner.EventIds.EventIds.Axis_Move_Started);

                // 优先使用新的CameraAxisViewModelNew实现
                if (_serviceProvider != null)
                {
                    try
                    {
                        // 创建相机轴视图模型并设置参数
                        var cameraAxisViewModel = _serviceProvider.GetRequiredService<CameraAxisViewModelNew>();

                        // 设置相机轴参数
                        cameraAxisViewModel.SetAxis("RZ");
                        cameraAxisViewModel.CameraPosition = "Right";

                        // 初始化
                        await cameraAxisViewModel.InitializeAsync();

                        return cameraAxisViewModel;
                    }
                    catch (Exception serviceEx)
                    {
                        _loggingService.LogWarning($"使用依赖注入创建右Z轴失败: {serviceEx.Message}，尝试直接创建", WaferAligner.EventIds.EventIds.Axis_Instance_Create_Failed);
                    }
                }

                // 如果依赖注入失败，直接创建实例
                var plcCommunication = _serviceProvider?.GetService<IPlcCommunication>();
                var axisEventService = _serviceProvider?.GetService<IAxisEventService>();

                if (plcCommunication != null && axisEventService != null)
                {
                    var directViewModel = new CameraAxisViewModelNew("RZ", "Right", plcCommunication, _loggingService, axisEventService);
                    await directViewModel.InitializeAsync();
                    return directViewModel;
                }

                // 所有创建方式都失败，抛出异常
                var errorMessage = "无法创建右Z轴ViewModel：依赖注入失败且直接创建也失败";
                _loggingService.LogError(errorMessage, WaferAligner.EventIds.EventIds.Axis_Instance_Create_Failed);
                throw new InvalidOperationException(errorMessage);
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, "创建右Z轴ViewModel失败", WaferAligner.EventIds.EventIds.Axis_Move_Error);
                throw;
            }
        }
        #endregion

        #region 批量创建
        public async Task<AxisViewModelCollection> CreateAllAxesAsync()
        {
            try
            {
                _loggingService.LogInformation("批量创建所有轴ViewModel", WaferAligner.EventIds.EventIds.Application_Started);

                if (_axisCollection != null)
                {
                    _loggingService.LogDebug("轴集合已存在，返回现有实例", WaferAligner.EventIds.EventIds.Resource_Registered);
                    return _axisCollection;
                }

                _axisCollection = new AxisViewModelCollection
                {
                    XAxis = await CreateXAxisAsync(),
                    YAxis = await CreateYAxisAsync(),
                    RAxis = await CreateRAxisAsync(),
                    ZAxis = await CreateZAxisAsync(),
                    LeftXAxis = await CreateLeftXAxisAsync(),
                    LeftYAxis = await CreateLeftYAxisAsync(),
                    LeftZAxis = await CreateLeftZAxisAsync(),
                    RightXAxis = await CreateRightXAxisAsync(),
                    RightYAxis = await CreateRightYAxisAsync(),
                    RightZAxis = await CreateRightZAxisAsync()
                };

                _loggingService.LogInformation("所有轴ViewModel创建完成", WaferAligner.EventIds.EventIds.Resource_Registered);
                return _axisCollection;
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, "批量创建轴ViewModel失败", WaferAligner.EventIds.EventIds.Unhandled_Exception);
                throw;
            }
        }
        #endregion

        #region 单例模式Get方法
        [Obsolete("请使用GetXAxisViewModelAsync，避免潜在的死锁风险")]
        public IXyrAxisViewModel GetXAxisViewModel()
        {
            // 使用Task.Run包装异步调用，避免死锁风险
            try
            {
                return Task.Run(() => GetXAxisViewModelAsync()).Result;
            }
            catch (AggregateException ae)
            {
                if (ae.InnerException != null)
                {
                    _loggingService.LogError(ae.InnerException, "获取X轴实例时发生错误", WaferAligner.EventIds.EventIds.Axis_Instance_Create_Failed);
                }
                else
                {
                    _loggingService.LogError(ae, "获取X轴实例时发生错误", WaferAligner.EventIds.EventIds.Axis_Instance_Create_Failed);
                }
                
                // 创建空实例作为回退方案
                var emptyController = new EmptySerialAxisController("X", _loggingService);
                return new SerialAxisViewModel(emptyController, _loggingService);
            }
        }

        public async Task<IXyrAxisViewModel> GetXAxisViewModelAsync()
        {
            if (_xAxisInstance == null)
            {
                IXyrAxisViewModel newInstance;
                
                // 使用双检锁，但避免锁内等待
                lock (_lock)
                {
                    if (_xAxisInstance != null)
                    {
                        return _xAxisInstance;
                    }
                }
                
                // 锁外创建实例
                try
                {
                    newInstance = await CreateXAxisAsync().ConfigureAwait(false);
                }
                catch (Exception ex)
                {
                    _loggingService.LogError(ex, "获取X轴实例失败", WaferAligner.EventIds.EventIds.Axis_Instance_Create_Failed);
                    
                    // 创建空实现作为回退方案
                    _loggingService.LogWarning("使用空实现作为X轴回退方案", WaferAligner.EventIds.EventIds.Resource_Registered);
                    var emptyController = new EmptySerialAxisController("X", _loggingService);
                    newInstance = new SerialAxisViewModel(emptyController, _loggingService);
                }
                
                // 再次加锁设置单例
                lock (_lock)
                {
                    if (_xAxisInstance == null)
                    {
                        _xAxisInstance = newInstance;
                    }
                    else
                    {
                        // 在我们创建新实例的同时，其他线程可能已经设置了实例
                        // 如果我们的实例是新创建的，应该释放它
                        if (newInstance is IDisposable disposable && newInstance != _xAxisInstance)
                        {
                            disposable.Dispose();
                        }
                    }
                }
            }
            
            return _xAxisInstance;
        }

        [Obsolete("请使用GetYAxisViewModelAsync，避免潜在的死锁风险")]
        public IXyrAxisViewModel GetYAxisViewModel()
        {
            // 使用Task.Run包装异步调用，避免死锁风险
            try
            {
                return Task.Run(() => GetYAxisViewModelAsync()).Result;
            }
            catch (AggregateException ae)
            {
                if (ae.InnerException != null)
                {
                    _loggingService.LogError(ae.InnerException, "获取Y轴实例时发生错误", WaferAligner.EventIds.EventIds.Axis_Instance_Create_Failed);
                }
                else
                {
                    _loggingService.LogError(ae, "获取Y轴实例时发生错误", WaferAligner.EventIds.EventIds.Axis_Instance_Create_Failed);
                }
                
                // 创建空实例作为回退方案
                var emptyController = new EmptySerialAxisController("Y", _loggingService);
                return new SerialAxisViewModel(emptyController, _loggingService);
            }
        }

        public async Task<IXyrAxisViewModel> GetYAxisViewModelAsync()
        {
            if (_yAxisInstance == null)
            {
                IXyrAxisViewModel newInstance;
                
                // 使用双检锁，但避免锁内等待
                lock (_lock)
                {
                    if (_yAxisInstance != null)
                    {
                        return _yAxisInstance;
                    }
                }
                
                // 锁外创建实例
                try
                {
                    newInstance = await CreateYAxisAsync().ConfigureAwait(false);
                }
                catch (Exception ex)
                {
                    _loggingService.LogError(ex, "获取Y轴实例失败", WaferAligner.EventIds.EventIds.Axis_Instance_Create_Failed);
                    
                    // 创建空实现作为回退方案
                    _loggingService.LogWarning("使用空实现作为Y轴回退方案", WaferAligner.EventIds.EventIds.Resource_Registered);
                    var emptyController = new EmptySerialAxisController("Y", _loggingService);
                    newInstance = new SerialAxisViewModel(emptyController, _loggingService);
                }
                
                // 再次加锁设置单例
                lock (_lock)
                {
                    if (_yAxisInstance == null)
                    {
                        _yAxisInstance = newInstance;
                    }
                    else
                    {
                        // 在我们创建新实例的同时，其他线程可能已经设置了实例
                        // 如果我们的实例是新创建的，应该释放它
                        if (newInstance is IDisposable disposable && newInstance != _yAxisInstance)
                        {
                            disposable.Dispose();
                        }
                    }
                }
            }
            
            return _yAxisInstance;
        }

        [Obsolete("请使用GetRAxisViewModelAsync，避免潜在的死锁风险")]
        public IXyrAxisViewModel GetRAxisViewModel()
        {
            // 使用Task.Run包装异步调用，避免死锁风险
            try
            {
                return Task.Run(() => GetRAxisViewModelAsync()).Result;
            }
            catch (AggregateException ae)
            {
                if (ae.InnerException != null)
                {
                    _loggingService.LogError(ae.InnerException, "获取R轴实例时发生错误", WaferAligner.EventIds.EventIds.Axis_Instance_Create_Failed);
                }
                else
                {
                    _loggingService.LogError(ae, "获取R轴实例时发生错误", WaferAligner.EventIds.EventIds.Axis_Instance_Create_Failed);
                }
                
                // 创建空实例作为回退方案
                var emptyController = new EmptySerialAxisController("R", _loggingService);
                return new SerialAxisViewModel(emptyController, _loggingService);
            }
        }

        public async Task<IXyrAxisViewModel> GetRAxisViewModelAsync()
        {
            if (_rAxisInstance == null)
            {
                IXyrAxisViewModel newInstance;
                
                // 使用双检锁，但避免锁内等待
                lock (_lock)
                {
                    if (_rAxisInstance != null)
                    {
                        return _rAxisInstance;
                    }
                }
                
                // 锁外创建实例
                try
                {
                    newInstance = await CreateRAxisAsync().ConfigureAwait(false);
                }
                catch (Exception ex)
                {
                    _loggingService.LogError(ex, "获取R轴实例失败", WaferAligner.EventIds.EventIds.Axis_Instance_Create_Failed);
                    
                    // 创建空实现作为回退方案
                    _loggingService.LogWarning("使用空实现作为R轴回退方案", WaferAligner.EventIds.EventIds.Resource_Registered);
                    var emptyController = new EmptySerialAxisController("R", _loggingService);
                    newInstance = new SerialAxisViewModel(emptyController, _loggingService);
                }
                
                // 再次加锁设置单例
                lock (_lock)
                {
                    if (_rAxisInstance == null)
                    {
                        _rAxisInstance = newInstance;
                    }
                    else
                    {
                        // 在我们创建新实例的同时，其他线程可能已经设置了实例
                        // 如果我们的实例是新创建的，应该释放它
                        if (newInstance is IDisposable disposable && newInstance != _rAxisInstance)
                        {
                            disposable.Dispose();
                        }
                    }
                }
            }
            
            return _rAxisInstance;
        }

        [Obsolete("请使用GetZAxisViewModelAsync，避免潜在的死锁风险")]
        public IZAxisViewModel GetZAxisViewModel()
        {
            // 使用Task.Run包装异步调用，避免死锁风险
            try
            {
                return Task.Run(() => GetZAxisViewModelAsync()).Result;
            }
            catch (AggregateException ae)
            {
                if (ae.InnerException != null)
                {
                    _loggingService.LogError(ae.InnerException, "获取Z轴实例时发生错误", WaferAligner.EventIds.EventIds.Axis_Instance_Create_Failed);
                }
                else
                {
                    _loggingService.LogError(ae, "获取Z轴实例时发生错误", WaferAligner.EventIds.EventIds.Axis_Instance_Create_Failed);
                }
                
                // 创建一个基本实例作为回退方案
                if (_serviceProvider != null && _serviceProvider.GetService<ZAxisViewModelNew>() != null)
                {
                    var basicInstance = _serviceProvider.GetRequiredService<ZAxisViewModelNew>();
                    return basicInstance;
                }
                else
                {
                    throw new InvalidOperationException("无法获取Z轴实例，请检查依赖注入或PLC连接");
                }
            }
        }

        public async Task<IZAxisViewModel> GetZAxisViewModelAsync()
        {
            if (_zAxisInstance == null)
            {
                IZAxisViewModel newInstance;
                
                // 使用双检锁，但避免锁内等待
                lock (_lock)
                {
                    if (_zAxisInstance != null)
                    {
                        return _zAxisInstance;
                    }
                }
                
                // 锁外创建实例
                try
                {
                    newInstance = await CreateZAxisAsync().ConfigureAwait(false);
                }
                catch (Exception ex)
                {
                    _loggingService.LogError(ex, "获取Z轴实例失败", WaferAligner.EventIds.EventIds.Axis_Instance_Create_Failed);
                    
                    // 尝试使用依赖注入创建实例
                    if (_serviceProvider?.GetService<ZAxisViewModelNew>() is ZAxisViewModelNew viewModel)
                    {
                        // 使用依赖注入获取的实例
                        _loggingService.LogWarning("使用依赖注入获取的Z轴实例作为回退方案", WaferAligner.EventIds.EventIds.Resource_Registered);
                        newInstance = viewModel;
                    }
                    else
                    {
                        // 无法创建实例，抛出异常
                        var errorMessage = "无法创建Z轴实例：依赖注入服务不可用";
                        _loggingService.LogError(errorMessage, WaferAligner.EventIds.EventIds.Axis_Instance_Create_Failed);
                        throw new InvalidOperationException(errorMessage);
                    }
                }
                
                // 再次加锁设置单例
                lock (_lock)
                {
                    if (_zAxisInstance == null)
                    {
                        _zAxisInstance = newInstance;
                    }
                    else
                    {
                        // 在我们创建新实例的同时，其他线程可能已经设置了实例
                        // 如果我们的实例是新创建的，不是来自依赖注入的，我们应该释放它
                        if (newInstance is IDisposable disposable && newInstance != _zAxisInstance && 
                            !(newInstance is ZAxisViewModelNew && _serviceProvider?.GetService<ZAxisViewModelNew>() == newInstance))
                        {
                            disposable.Dispose();
                        }
                    }
                }
            }
            
            return _zAxisInstance;
        }

        [Obsolete("请使用GetLXAxisViewModelAsync，避免潜在的死锁风险")]
        public ICameraAxisViewModel GetLXAxisViewModel()
        {
            // 使用Task.Run包装异步调用，避免死锁风险
            try
            {
                return Task.Run(() => GetLXAxisViewModelAsync()).Result;
            }
            catch (AggregateException ae)
            {
                if (ae.InnerException != null)
                {
                    _loggingService.LogError(ae.InnerException, "获取左X轴实例时发生错误", WaferAligner.EventIds.EventIds.Axis_Instance_Create_Failed);
                }
                else
                {
                    _loggingService.LogError(ae, "获取左X轴实例时发生错误", WaferAligner.EventIds.EventIds.Axis_Instance_Create_Failed);
                }
                
                // 创建一个基本实例作为回退方案
                if (_serviceProvider != null && _serviceProvider.GetService<CameraAxisViewModelNew>() != null)
                {
                    var basicInstance = _serviceProvider.GetRequiredService<CameraAxisViewModelNew>();
                    basicInstance.SetAxis("LX");
                    basicInstance.CameraPosition = "Left";
                    return basicInstance;
                }
                else
                {
                    throw new InvalidOperationException("无法获取左X轴实例，请检查依赖注入或PLC连接");
                }
            }
        }

        public async Task<ICameraAxisViewModel> GetLXAxisViewModelAsync()
        {
            if (_lxAxisInstance == null)
            {
                ICameraAxisViewModel newInstance;
                
                // 使用双检锁，但避免锁内等待
                lock (_lock)
                {
                    if (_lxAxisInstance != null)
                    {
                        return _lxAxisInstance;
                    }
                }
                
                // 锁外创建实例
                try
                {
                    // 直接使用异步方法，而不是ConfigureAwait(false)
                    newInstance = await CreateLeftXAxisAsync();
                }
                catch (Exception ex)
                {
                    _loggingService.LogError(ex, "获取左X轴实例失败", WaferAligner.EventIds.EventIds.Axis_Instance_Create_Failed);
                    
                    // 尝试使用依赖注入创建实例
                    if (_serviceProvider?.GetService<CameraAxisViewModelNew>() is CameraAxisViewModelNew viewModel)
                    {
                        // 使用依赖注入获取的实例
                        _loggingService.LogWarning("使用依赖注入获取的左X轴实例作为回退方案", WaferAligner.EventIds.EventIds.Resource_Registered);
                        viewModel.SetAxis("LX");
                        viewModel.CameraPosition = "Left";
                        newInstance = viewModel;
                    }
                    else
                    {
                        // 无法创建实例，记录错误并返回null
                        _loggingService.LogError("无法创建左X轴实例：依赖注入服务不可用", WaferAligner.EventIds.EventIds.Axis_Instance_Create_Failed);
                        newInstance = null;
                    }
                }
                
                // 再次加锁设置单例
                lock (_lock)
                {
                    if (_lxAxisInstance == null)
                    {
                        _lxAxisInstance = newInstance;
                    }
                    else
                    {
                        // 在我们创建新实例的同时，其他线程可能已经设置了实例
                        // 如果我们的实例是新创建的，不是来自依赖注入的，我们应该释放它
                        if (newInstance is IDisposable disposable && newInstance != _lxAxisInstance &&
                            !(newInstance is CameraAxisViewModelNew && _serviceProvider?.GetService<CameraAxisViewModelNew>() == newInstance))
                        {
                            disposable.Dispose();
                        }
                    }
                }
            }
            
            return _lxAxisInstance;
        }

        [Obsolete("请使用GetLYAxisViewModelAsync，避免潜在的死锁风险")]
        public ICameraAxisViewModel GetLYAxisViewModel()
        {
            // 使用Task.Run包装异步调用，避免死锁风险
            try
            {
                return Task.Run(() => GetLYAxisViewModelAsync()).Result;
            }
            catch (AggregateException ae)
            {
                if (ae.InnerException != null)
                {
                    _loggingService.LogError(ae.InnerException, "获取左Y轴实例时发生错误", WaferAligner.EventIds.EventIds.Axis_Instance_Create_Failed);
                }
                else
                {
                    _loggingService.LogError(ae, "获取左Y轴实例时发生错误", WaferAligner.EventIds.EventIds.Axis_Instance_Create_Failed);
                }
                
                // 创建一个基本实例作为回退方案
                if (_serviceProvider != null && _serviceProvider.GetService<CameraAxisViewModelNew>() != null)
                {
                    var basicInstance = _serviceProvider.GetRequiredService<CameraAxisViewModelNew>();
                    basicInstance.SetAxis("LY");
                    basicInstance.CameraPosition = "Left";
                    return basicInstance;
                }
                else
                {
                    throw new InvalidOperationException("无法获取左Y轴实例，请检查依赖注入或PLC连接");
                }
            }
        }

        public async Task<ICameraAxisViewModel> GetLYAxisViewModelAsync()
        {
            if (_lyAxisInstance == null)
            {
                ICameraAxisViewModel newInstance;
                
                // 使用双检锁，但避免锁内等待
                lock (_lock)
                {
                    if (_lyAxisInstance != null)
                    {
                        return _lyAxisInstance;
                    }
                }
                
                // 锁外创建实例
                try
                {
                    // 直接使用异步方法，而不是ConfigureAwait(false)
                    newInstance = await CreateLeftYAxisAsync();
                }
                catch (Exception ex)
                {
                    _loggingService.LogError(ex, "获取左Y轴实例失败", WaferAligner.EventIds.EventIds.Axis_Instance_Create_Failed);
                    
                    // 尝试使用依赖注入创建实例
                    if (_serviceProvider?.GetService<CameraAxisViewModelNew>() is CameraAxisViewModelNew viewModel)
                    {
                        // 使用依赖注入获取的实例
                        _loggingService.LogWarning("使用依赖注入获取的左Y轴实例作为回退方案", WaferAligner.EventIds.EventIds.Resource_Registered);
                        viewModel.SetAxis("LY");
                        viewModel.CameraPosition = "Left";
                        newInstance = viewModel;
                    }
                    else
                    {
                        // 无法创建实例，记录错误并返回null
                        _loggingService.LogError("无法创建左Y轴实例：依赖注入服务不可用", WaferAligner.EventIds.EventIds.Axis_Instance_Create_Failed);
                        newInstance = null;
                    }
                }
                
                // 再次加锁设置单例
                lock (_lock)
                {
                    if (_lyAxisInstance == null)
                    {
                        _lyAxisInstance = newInstance;
                    }
                    else
                    {
                        // 在我们创建新实例的同时，其他线程可能已经设置了实例
                        // 如果我们的实例是新创建的，不是来自依赖注入的，我们应该释放它
                        if (newInstance is IDisposable disposable && newInstance != _lyAxisInstance &&
                            !(newInstance is CameraAxisViewModelNew && _serviceProvider?.GetService<CameraAxisViewModelNew>() == newInstance))
                        {
                            disposable.Dispose();
                        }
                    }
                }
            }
            
            return _lyAxisInstance;
        }

        [Obsolete("请使用GetLZAxisViewModelAsync，避免潜在的死锁风险")]
        public ICameraAxisViewModel GetLZAxisViewModel()
        {
            // 使用Task.Run包装异步调用，避免死锁风险
            try
            {
                return Task.Run(() => GetLZAxisViewModelAsync()).Result;
            }
            catch (AggregateException ae)
            {
                if (ae.InnerException != null)
                {
                    _loggingService.LogError(ae.InnerException, "获取左Z轴实例时发生错误", WaferAligner.EventIds.EventIds.Axis_Instance_Create_Failed);
                }
                else
                {
                    _loggingService.LogError(ae, "获取左Z轴实例时发生错误", WaferAligner.EventIds.EventIds.Axis_Instance_Create_Failed);
                }
                
                // 创建一个基本实例作为回退方案
                if (_serviceProvider != null && _serviceProvider.GetService<CameraAxisViewModelNew>() != null)
                {
                    var basicInstance = _serviceProvider.GetRequiredService<CameraAxisViewModelNew>();
                    basicInstance.SetAxis("LZ");
                    basicInstance.CameraPosition = "Left";
                    return basicInstance;
                }
                else
                {
                    throw new InvalidOperationException("无法获取左Z轴实例，请检查依赖注入或PLC连接");
                }
            }
        }

        public async Task<ICameraAxisViewModel> GetLZAxisViewModelAsync()
        {
            if (_lzAxisInstance == null)
            {
                ICameraAxisViewModel newInstance;
                
                // 使用双检锁，但避免锁内等待
                lock (_lock)
                {
                    if (_lzAxisInstance != null)
                    {
                        return _lzAxisInstance;
                    }
                }
                
                // 锁外创建实例
                try
                {
                    // 直接使用异步方法，而不是ConfigureAwait(false)
                    newInstance = await CreateLeftZAxisAsync();
                }
                catch (Exception ex)
                {
                    _loggingService.LogError(ex, "获取左Z轴实例失败", WaferAligner.EventIds.EventIds.Axis_Instance_Create_Failed);
                    
                    // 尝试使用依赖注入创建实例
                    if (_serviceProvider?.GetService<CameraAxisViewModelNew>() is CameraAxisViewModelNew viewModel)
                    {
                        // 使用依赖注入获取的实例
                        _loggingService.LogWarning("使用依赖注入获取的左Z轴实例作为回退方案", WaferAligner.EventIds.EventIds.Resource_Registered);
                        viewModel.SetAxis("LZ");
                        viewModel.CameraPosition = "Left";
                        newInstance = viewModel;
                    }
                    else
                    {
                        // 无法创建实例，记录错误并返回null
                        _loggingService.LogError("无法创建左Z轴实例：依赖注入服务不可用", WaferAligner.EventIds.EventIds.Axis_Instance_Create_Failed);
                        newInstance = null;
                    }
                }
                
                // 再次加锁设置单例
                lock (_lock)
                {
                    if (_lzAxisInstance == null)
                    {
                        _lzAxisInstance = newInstance;
                    }
                    else
                    {
                        // 在我们创建新实例的同时，其他线程可能已经设置了实例
                        // 如果我们的实例是新创建的，不是来自依赖注入的，我们应该释放它
                        if (newInstance is IDisposable disposable && newInstance != _lzAxisInstance && 
                            !(newInstance is CameraAxisViewModelNew && _serviceProvider?.GetService<CameraAxisViewModelNew>() == newInstance))
                        {
                            disposable.Dispose();
                        }
                    }
                }
            }
            
            return _lzAxisInstance;
        }

        [Obsolete("请使用GetRXAxisViewModelAsync，避免潜在的死锁风险")]
        public ICameraAxisViewModel GetRXAxisViewModel()
        {
            // 使用Task.Run包装异步调用，避免死锁风险
            try
            {
                return Task.Run(() => GetRXAxisViewModelAsync()).Result;
            }
            catch (AggregateException ae)
            {
                if (ae.InnerException != null)
                {
                    _loggingService.LogError(ae.InnerException, "获取右X轴实例时发生错误", WaferAligner.EventIds.EventIds.Axis_Instance_Create_Failed);
                }
                else
                {
                    _loggingService.LogError(ae, "获取右X轴实例时发生错误", WaferAligner.EventIds.EventIds.Axis_Instance_Create_Failed);
                }
                
                // 创建一个基本实例作为回退方案
                if (_serviceProvider != null && _serviceProvider.GetService<CameraAxisViewModelNew>() != null)
                {
                    var basicInstance = _serviceProvider.GetRequiredService<CameraAxisViewModelNew>();
                    basicInstance.SetAxis("RX");
                    basicInstance.CameraPosition = "Right";
                    return basicInstance;
                }
                else
                {
                    throw new InvalidOperationException("无法获取右X轴实例，请检查依赖注入或PLC连接");
                }
            }
        }

        public async Task<ICameraAxisViewModel> GetRXAxisViewModelAsync()
        {
            if (_rxAxisInstance == null)
            {
                ICameraAxisViewModel newInstance;
                
                // 使用双检锁，但避免锁内等待
                lock (_lock)
                {
                    if (_rxAxisInstance != null)
                    {
                        return _rxAxisInstance;
                    }
                }
                
                // 锁外创建实例
                try
                {
                    // 直接使用异步方法，而不是ConfigureAwait(false)
                    newInstance = await CreateRightXAxisAsync();
                }
                catch (Exception ex)
                {
                    _loggingService.LogError(ex, "获取右X轴实例失败", WaferAligner.EventIds.EventIds.Axis_Instance_Create_Failed);
                    
                    // 尝试使用依赖注入创建实例
                    if (_serviceProvider?.GetService<CameraAxisViewModelNew>() is CameraAxisViewModelNew viewModel)
                    {
                        // 使用依赖注入获取的实例
                        _loggingService.LogWarning("使用依赖注入获取的右X轴实例作为回退方案", WaferAligner.EventIds.EventIds.Resource_Registered);
                        viewModel.SetAxis("RX");
                        viewModel.CameraPosition = "Right";
                        newInstance = viewModel;
                    }
                    else
                    {
                        // 无法创建实例，记录错误并返回null
                        _loggingService.LogError("无法创建右X轴实例：依赖注入服务不可用", WaferAligner.EventIds.EventIds.Axis_Instance_Create_Failed);
                        newInstance = null;
                    }
                }
                
                // 再次加锁设置单例
                lock (_lock)
                {
                    if (_rxAxisInstance == null)
                    {
                        _rxAxisInstance = newInstance;
                    }
                    else
                    {
                        // 在我们创建新实例的同时，其他线程可能已经设置了实例
                        // 如果我们的实例是新创建的，不是来自依赖注入的，我们应该释放它
                        if (newInstance is IDisposable disposable && newInstance != _rxAxisInstance && 
                            !(newInstance is CameraAxisViewModelNew && _serviceProvider?.GetService<CameraAxisViewModelNew>() == newInstance))
                        {
                            disposable.Dispose();
                        }
                    }
                }
            }
            
            return _rxAxisInstance;
        }

        [Obsolete("请使用GetRYAxisViewModelAsync，避免潜在的死锁风险")]
        public ICameraAxisViewModel GetRYAxisViewModel()
        {
            // 使用Task.Run包装异步调用，避免死锁风险
            try
            {
                return Task.Run(() => GetRYAxisViewModelAsync()).Result;
            }
            catch (AggregateException ae)
            {
                if (ae.InnerException != null)
                {
                    _loggingService.LogError(ae.InnerException, "获取右Y轴实例时发生错误", WaferAligner.EventIds.EventIds.Axis_Instance_Create_Failed);
                }
                else
                {
                    _loggingService.LogError(ae, "获取右Y轴实例时发生错误", WaferAligner.EventIds.EventIds.Axis_Instance_Create_Failed);
                }
                
                // 创建一个基本实例作为回退方案
                if (_serviceProvider != null && _serviceProvider.GetService<CameraAxisViewModelNew>() != null)
                {
                    var basicInstance = _serviceProvider.GetRequiredService<CameraAxisViewModelNew>();
                    basicInstance.SetAxis("RY");
                    basicInstance.CameraPosition = "Right";
                    return basicInstance;
                }
                else
                {
                    throw new InvalidOperationException("无法获取右Y轴实例，请检查依赖注入或PLC连接");
                }
            }
        }

        public async Task<ICameraAxisViewModel> GetRYAxisViewModelAsync()
        {
            if (_ryAxisInstance == null)
            {
                ICameraAxisViewModel newInstance;
                
                // 使用双检锁，但避免锁内等待
                lock (_lock)
                {
                    if (_ryAxisInstance != null)
                    {
                        return _ryAxisInstance;
                    }
                }
                
                // 锁外创建实例
                try
                {
                    // 直接使用异步方法，而不是ConfigureAwait(false)
                    newInstance = await CreateRightYAxisAsync();
                }
                catch (Exception ex)
                {
                    _loggingService.LogError(ex, "获取右Y轴实例失败", WaferAligner.EventIds.EventIds.Axis_Instance_Create_Failed);
                    
                    // 尝试使用依赖注入创建实例
                    if (_serviceProvider?.GetService<CameraAxisViewModelNew>() is CameraAxisViewModelNew viewModel)
                    {
                        // 使用依赖注入获取的实例
                        _loggingService.LogWarning("使用依赖注入获取的右Y轴实例作为回退方案", WaferAligner.EventIds.EventIds.Resource_Registered);
                        viewModel.SetAxis("RY");
                        viewModel.CameraPosition = "Right";
                        newInstance = viewModel;
                    }
                    else
                    {
                        // 无法创建实例，记录错误并返回null
                        _loggingService.LogError("无法创建右Y轴实例：依赖注入服务不可用", WaferAligner.EventIds.EventIds.Axis_Instance_Create_Failed);
                        newInstance = null;
                    }
                }
                
                // 再次加锁设置单例
                lock (_lock)
                {
                    if (_ryAxisInstance == null)
                    {
                        _ryAxisInstance = newInstance;
                    }
                    else
                    {
                        // 在我们创建新实例的同时，其他线程可能已经设置了实例
                        // 如果我们的实例是新创建的，不是来自依赖注入的，我们应该释放它
                        if (newInstance is IDisposable disposable && newInstance != _ryAxisInstance && 
                            !(newInstance is CameraAxisViewModelNew && _serviceProvider?.GetService<CameraAxisViewModelNew>() == newInstance))
                        {
                            disposable.Dispose();
                        }
                    }
                }
            }
            
            return _ryAxisInstance;
        }

        [Obsolete("请使用GetRZAxisViewModelAsync，避免潜在的死锁风险")]
        public ICameraAxisViewModel GetRZAxisViewModel()
        {
            // 使用Task.Run包装异步调用，避免死锁风险
            try
            {
                return Task.Run(() => GetRZAxisViewModelAsync()).Result;
            }
            catch (AggregateException ae)
            {
                if (ae.InnerException != null)
                {
                    _loggingService.LogError(ae.InnerException, "获取右Z轴实例时发生错误", WaferAligner.EventIds.EventIds.Axis_Instance_Create_Failed);
                }
                else
                {
                    _loggingService.LogError(ae, "获取右Z轴实例时发生错误", WaferAligner.EventIds.EventIds.Axis_Instance_Create_Failed);
                }
                
                // 创建一个基本实例作为回退方案
                if (_serviceProvider != null && _serviceProvider.GetService<CameraAxisViewModelNew>() != null)
                {
                    var basicInstance = _serviceProvider.GetRequiredService<CameraAxisViewModelNew>();
                    basicInstance.SetAxis("RZ");
                    basicInstance.CameraPosition = "Right";
                    return basicInstance;
                }
                else
                {
                    throw new InvalidOperationException("无法获取右Z轴实例，请检查依赖注入或PLC连接");
                }
            }
        }

        public async Task<ICameraAxisViewModel> GetRZAxisViewModelAsync()
        {
            if (_rzAxisInstance == null)
            {
                ICameraAxisViewModel newInstance;
                
                // 使用双检锁，但避免锁内等待
                lock (_lock)
                {
                    if (_rzAxisInstance != null)
                    {
                        return _rzAxisInstance;
                    }
                }
                
                // 锁外创建实例
                try
                {
                    // 直接使用异步方法，而不是ConfigureAwait(false)
                    newInstance = await CreateRightZAxisAsync();
                }
                catch (Exception ex)
                {
                    _loggingService.LogError(ex, "获取右Z轴实例失败", WaferAligner.EventIds.EventIds.Axis_Instance_Create_Failed);
                    
                    // 尝试使用依赖注入创建实例
                    if (_serviceProvider?.GetService<CameraAxisViewModelNew>() is CameraAxisViewModelNew viewModel)
                    {
                        // 使用依赖注入获取的实例
                        _loggingService.LogWarning("使用依赖注入获取的右Z轴实例作为回退方案", WaferAligner.EventIds.EventIds.Resource_Registered);
                        viewModel.SetAxis("RZ");
                        viewModel.CameraPosition = "Right";
                        newInstance = viewModel;
                    }
                    else
                    {
                        // 无法创建实例，记录错误并返回null
                        _loggingService.LogError("无法创建右Z轴实例：依赖注入服务不可用", WaferAligner.EventIds.EventIds.Axis_Instance_Create_Failed);
                        newInstance = null;
                    }
                }
                
                // 再次加锁设置单例
                lock (_lock)
                {
                    if (_rzAxisInstance == null)
                    {
                        _rzAxisInstance = newInstance;
                    }
                    else
                    {
                        // 在我们创建新实例的同时，其他线程可能已经设置了实例
                        // 如果我们的实例是新创建的，不是来自依赖注入的，我们应该释放它
                        if (newInstance is IDisposable disposable && newInstance != _rzAxisInstance && 
                            !(newInstance is CameraAxisViewModelNew && _serviceProvider?.GetService<CameraAxisViewModelNew>() == newInstance))
                        {
                            disposable.Dispose();
                        }
                    }
                }
            }
            
            return _rzAxisInstance;
        }
        #endregion

        #region 资源管理
        public async Task CleanupAsync()
        {
            try
            {
                _loggingService.LogInformation("清理所有轴ViewModel", WaferAligner.EventIds.EventIds.Application_Stopped);

                // 清理单例实例
                await CleanupAxisAsync(_xAxisInstance, "X轴实例");
                await CleanupAxisAsync(_yAxisInstance, "Y轴实例");
                await CleanupAxisAsync(_rAxisInstance, "R轴实例");
                await CleanupAxisAsync(_zAxisInstance, "Z轴实例");
                await CleanupAxisAsync(_lxAxisInstance, "左X轴实例");
                await CleanupAxisAsync(_lyAxisInstance, "左Y轴实例");
                await CleanupAxisAsync(_lzAxisInstance, "左Z轴实例");
                await CleanupAxisAsync(_rxAxisInstance, "右X轴实例");
                await CleanupAxisAsync(_ryAxisInstance, "右Y轴实例");
                await CleanupAxisAsync(_rzAxisInstance, "右Z轴实例");

                // 清理单例实例引用
                _xAxisInstance = null;
                _yAxisInstance = null;
                _rAxisInstance = null;
                _zAxisInstance = null;
                _lxAxisInstance = null;
                _lyAxisInstance = null;
                _lzAxisInstance = null;
                _rxAxisInstance = null;
                _ryAxisInstance = null;
                _rzAxisInstance = null;

                if (_axisCollection != null)
                {
                    // 清理各个轴
                    await CleanupAxisAsync(_axisCollection.XAxis, "X轴");
                    await CleanupAxisAsync(_axisCollection.YAxis, "Y轴");
                    await CleanupAxisAsync(_axisCollection.RAxis, "R轴");
                    await CleanupAxisAsync(_axisCollection.ZAxis, "Z轴");
                    await CleanupAxisAsync(_axisCollection.LeftXAxis, "左X轴");
                    await CleanupAxisAsync(_axisCollection.LeftYAxis, "左Y轴");
                    await CleanupAxisAsync(_axisCollection.LeftZAxis, "左Z轴");
                    await CleanupAxisAsync(_axisCollection.RightXAxis, "右X轴");
                    await CleanupAxisAsync(_axisCollection.RightYAxis, "右Y轴");
                    await CleanupAxisAsync(_axisCollection.RightZAxis, "右Z轴");

                    _axisCollection = null;
                }

                _loggingService.LogInformation("所有轴ViewModel清理完成", WaferAligner.EventIds.EventIds.Application_Stopped);
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, "清理轴ViewModel时发生异常", WaferAligner.EventIds.EventIds.Unhandled_Exception);
            }
        }

        private async Task CleanupAxisAsync(IAxisViewModel axis, string axisName)
        {
            try
            {
                if (axis != null)
                {
                    await axis.DisconnectAsync();
                    _loggingService.LogDebug($"{axisName} 清理完成", WaferAligner.EventIds.EventIds.Application_Stopped);
                }
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"清理{axisName}时发生异常", WaferAligner.EventIds.EventIds.Unhandled_Exception);
            }
        }
        #endregion

        /// <summary>
        /// 检查是否应该记录串口连接失败日志
        /// </summary>
        private bool ShouldLogSerialConnectFailure(string axisName)
        {
            lock (_serialLogLock)
            {
                DateTime now = DateTime.Now;
                
                // 检查是否已达到最大连续失败日志次数
                if (_serialConnectFailCount >= MaxSerialConnectFailLogCount)
                {
                    // 检查是否已经过了抑制间隔
                    if ((now - _lastSerialConnectLogTime).TotalSeconds < SerialConnectLogIntervalSeconds)
                    {
                        return false; // 在抑制期内，不记录日志
                    }
                    else
                    {
                        // 抑制期已过，重置计数，允许记录日志
                        _serialConnectFailCount = 1;
                        _lastSerialConnectLogTime = now;
                        return true;
                    }
                }
                else
                {
                    // 未达到最大次数，记录日志并增加计数
                    _serialConnectFailCount++;
                    _lastSerialConnectLogTime = now;
                    
                    // 如果达到最大次数，记录一条特殊警告
                    if (_serialConnectFailCount == MaxSerialConnectFailLogCount)
                    {
                        _loggingService.LogWarning($"串口连接失败日志将被抑制{SerialConnectLogIntervalSeconds}秒", 
                            WaferAligner.EventIds.EventIds.Plc_Connection_Failed);
                    }
                    
                    return true;
                }
            }
        }
    }
    
    /// <summary>
    /// 空实现的SerialAxisController，用于回退情况
    /// </summary>
    internal class EmptySerialAxisController : ISerialAxisController
    {
        private readonly string _axisName;
        private readonly WaferAligner.Services.Logging.Abstractions.ILoggingService _loggingService;

        public EmptySerialAxisController(string axisName, WaferAligner.Services.Logging.Abstractions.ILoggingService loggingService)
        {
            _axisName = axisName;
            _loggingService = loggingService;
        }
        
        public string AxisName => _axisName;
        
        public bool IsConnected => false;
        
        public event EventHandler<SerialAxisEventArgs> PositionChanged;
        public event EventHandler<SerialAxisEventArgs> StateChanged;
        
        public Task<bool> ClearErrorAsync(CancellationToken cancellationToken = default)
        {
            _loggingService?.LogWarning($"{_axisName}轴使用空实现，ClearErrorAsync无效", WaferAligner.EventIds.EventIds.Axis_Move_Error);
            return Task.FromResult(false);
        }
        
        public Task<bool> EnableAxisAsync(CancellationToken cancellationToken = default)
        {
            _loggingService?.LogWarning($"{_axisName}轴使用空实现，EnableAxisAsync无效", WaferAligner.EventIds.EventIds.Axis_Move_Error);
            return Task.FromResult(false);
        }
        
        public Task<int> GetAlarmStateAsync(CancellationToken cancellationToken = default)
        {
            return Task.FromResult(0);
        }
        
        public Task<int> GetPositionAsync(CancellationToken cancellationToken = default)
        {
            return Task.FromResult(0);
        }
        
        public Task<int> GetRunStateAsync(CancellationToken cancellationToken = default)
        {
            return Task.FromResult(0);
        }
        
        public Task<bool> HomeAsync(CancellationToken cancellationToken = default)
        {
            _loggingService?.LogWarning($"{_axisName}轴使用空实现，HomeAsync无效", WaferAligner.EventIds.EventIds.Axis_Move_Error);
            return Task.FromResult(false);
        }
        
        public Task<bool> MoveToPositionAsync(int position, bool isRelative = false, CancellationToken cancellationToken = default)
        {
            _loggingService?.LogWarning($"{_axisName}轴使用空实现，MoveToPositionAsync无效", WaferAligner.EventIds.EventIds.Axis_Move_Error);
            return Task.FromResult(false);
        }
        
        public Task<bool> StopAsync(CancellationToken cancellationToken = default)
        {
            return Task.FromResult(true);
        }
        
        public Task<bool> SetRunSpeedAsync(uint speed, CancellationToken cancellationToken = default)
        {
            _loggingService?.LogWarning($"{_axisName}轴使用空实现，SetRunSpeedAsync无效，速度值: {speed}", WaferAligner.EventIds.EventIds.Serial_Axis_Operation);
            return Task.FromResult(false);
        }
        
        public Task<bool> SetJogSpeedAsync(uint speed, CancellationToken cancellationToken = default)
        {
            _loggingService?.LogWarning($"{_axisName}轴使用空实现，SetJogSpeedAsync无效，速度值: {speed}", WaferAligner.EventIds.EventIds.Serial_Axis_Operation);
            return Task.FromResult(false);
        }
        
        public void Dispose()
        {
            // 空实现无需释放资源
        }
    }
} 