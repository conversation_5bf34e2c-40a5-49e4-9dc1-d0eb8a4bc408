using Microsoft.Extensions.DependencyInjection;
using System;
using WaferAligner.Communication.Inovance.Abstractions;
using WaferAligner.Communication.Inovance.Management;
using WaferAligner.Services;

namespace WaferAligner
{
    /// <summary>
    /// 服务集合扩展方法
    /// </summary>
    public static class ServiceCollectionExtensions
    {
        /// <summary>
        /// 注册WaferAligner服务
        /// </summary>
        /// <param name="services">服务集合</param>
        /// <returns>服务集合</returns>
        public static IServiceCollection AddWaferAlignerServices(this IServiceCollection services)
        {
            if (services == null)
            {
                throw new ArgumentNullException(nameof(services));
            }

            // 注册基础服务
            services.AddSingleton<IPlcVariableService, PlcVariableService>();
            services.AddSingleton<IAxisEventService, AxisEventService>();
            services.AddSingleton<IUIUpdateService, UIUpdateService>();
            
            // 注册AlignerParaService
            services.AddSingleton<IAlignerParaService, AlignerParaService>();
            
            // 注册RecipeService
            services.AddSingleton<IRecipeService, RecipeService>();
            
            return services;
        }
    }
} 