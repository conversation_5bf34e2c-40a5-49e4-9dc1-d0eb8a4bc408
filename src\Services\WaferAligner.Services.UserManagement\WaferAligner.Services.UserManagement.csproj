<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0-windows</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Services\Service.Common\WaferAligner.Services.Abstractions\WaferAligner.Services.Abstractions.csproj" />
    <ProjectReference Include="..\Services\Service.Common\WaferAligner.Services.Core\WaferAligner.Services.Core.csproj" />
    <ProjectReference Include="..\Business\WaferAligner.Core.Business\WaferAligner.Core.Business.csproj" />
    <ProjectReference Include="..\WaferAligner.EventIds\WaferAligner.EventIds.csproj" />
  </ItemGroup>

</Project>
