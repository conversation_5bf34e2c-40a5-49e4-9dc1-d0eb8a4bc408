//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Reflection;

[assembly: System.Reflection.AssemblyCompanyAttribute("WaferAligner Team")]
[assembly: System.Reflection.AssemblyConfigurationAttribute("Debug")]
[assembly: System.Reflection.AssemblyDescriptionAttribute("WaferAligner基础设施通用组件库，包含资源管理、定时器封装、UI线程安全调用、任务扩展、性能监控等功能")]
[assembly: System.Reflection.AssemblyFileVersionAttribute("*******")]
[assembly: System.Reflection.AssemblyInformationalVersionAttribute("1.0.0")]
[assembly: System.Reflection.AssemblyProductAttribute("WaferAligner.Infrastructure.Common")]
[assembly: System.Reflection.AssemblyTitleAttribute("WaferAligner.Infrastructure.Common")]
[assembly: System.Reflection.AssemblyVersionAttribute("*******")]
[assembly: System.Reflection.AssemblyMetadataAttribute("RepositoryUrl", "https://github.com/your-org/WaferAligner")]
[assembly: System.Runtime.Versioning.TargetPlatformAttribute("Windows7.0")]
[assembly: System.Runtime.Versioning.SupportedOSPlatformAttribute("Windows7.0")]

// 由 MSBuild WriteCodeFragment 类生成。

