// using AlignerUI; // 已迁移到WaferAligner.Models
using WaferAligner.Communication.Inovance.Abstractions;
using WaferAligner.Services.Logging.Abstractions;
using Microsoft.Extensions.DependencyInjection;
using WaferAligner.Communication.Inovance;
using WaferAligner.Communication.Inovance.Client;
using System;
using System.ComponentModel;
using System.Threading.Channels;
using System.Windows.Forms;
using System.Xml.Linq;
using WaferAligner;
using WaferAligner.Common;
using static System.Net.WebRequestMethods;
using System.Threading;
using WaferAligner.Services.Logging.Extensions;
using WaferAligner.EventIds;
using WaferAligner.Interfaces;
using WaferAligner.Services.UserManagement;
using Microsoft.Extensions.Logging;
using System.Reflection;
using WaferAligner.CustomClass;
// using WaferAligner.Forms.CustomContro;
using WaferAligner.Forms.Pages;
using System.Linq;
using WaferAligner.Services;
using WaferAligner.Infrastructure.Common;

namespace Sunny.UI.Demo
{
    public partial class FHeaderMainFooter : UIHeaderMainFooterFrame
    {
        private CancellationTokenSource _cancellationTokenSource = new CancellationTokenSource();

        // 静态实例，将在第一次使用时初始化
        // 静态兼容层已移除，不再需要静态实例
        bool SoftEncry = true;
        private readonly ILoggingService _loggingService;
        private readonly ResourceManager _resourceManager;
        private readonly IPlcConnectionManager _plcConnectionManager;
        private readonly IMainWindowViewModel _mainWindowViewModel;
        private readonly IAxisViewModelFactory _axisFactory;
        private readonly IUserContext _userContext; // 添加用户上下文服务
        private readonly IStatusUpdateService _statusUpdateService; // 状态更新服务
        
        // 替换System.Windows.Forms.Timer为TimerWrapper
        private TimerWrapper _uiUpdateTimer;

        private LoadingIndicator _loadingIndicator = null;
        private PerformanceMonitor _perfMonitor = null;
        
        FTitlePage1 FTP1;
        FTitlePage2 FTP2;
        FTitlePage3 FTP3;
        FTitlePage4 FTP5;

        //子窗体改变主窗体控件文本
        public string LalSoftwareStateTest
        {
            get
            {
                return _statusUpdateService?.GetCurrentStatus() ?? string.Empty;
            }
            set
            {
                _statusUpdateService?.UpdateStatus(value);
            }
        }
        public FHeaderMainFooter(
            ILoggingService loggingService,
            ResourceManager resourceManager,
            IPlcConnectionManager plcConnectionManager,
            IMainWindowViewModel mainWindowViewModel,
            IAxisViewModelFactory axisFactory,
            IUserContext userContext,
            PerformanceMonitor performanceMonitor,
            IStatusUpdateService statusUpdateService)
        {
            try
            {
                InitializeComponent();

                // 通过构造函数注入服务
                _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
                _resourceManager = resourceManager ?? throw new ArgumentNullException(nameof(resourceManager));
                _plcConnectionManager = plcConnectionManager ?? throw new ArgumentNullException(nameof(plcConnectionManager));
                _mainWindowViewModel = mainWindowViewModel ?? throw new ArgumentNullException(nameof(mainWindowViewModel));
                _axisFactory = axisFactory ?? throw new ArgumentNullException(nameof(axisFactory));
                _userContext = userContext ?? throw new ArgumentNullException(nameof(userContext));
                _perfMonitor = performanceMonitor; // 性能监控器可以为空，某些情况下可能不需要
                _statusUpdateService = statusUpdateService ?? throw new ArgumentNullException(nameof(statusUpdateService));

                // 静态兼容层已移除，不再需要静态实例
                // 状态更新通过依赖注入的IStatusUpdateService进行

                // 注册主要资源到资源管理器
                try
                {
                    // 注册取消令牌源
                    _resourceManager.RegisterResource("CancellationTokenSource", _cancellationTokenSource);

                    // 注册自定义清理操作
                    _resourceManager.RegisterCustomCleanup(async () => await CleanupMainWindowViewModelAsync());
                    _resourceManager.RegisterCustomCleanup(async () => await CleanupPLCConnectionsAsync());
                }
                catch (Exception ex)
                {
                    _loggingService?.LogWarning($"注册资源到资源管理器时发生错误: {ex.Message}");
                    // 不阻止程序继续运行
                }
                
                // 创建并配置UI更新定时器
                _uiUpdateTimer = new TimerWrapper("MainForm_UIUpdateTimer", 1000, UiUpdateTimer_Tick, _loggingService);
                
                // 启用状态跟踪
                _uiUpdateTimer.EnableStateTracking();
                
                // 注册到资源管理器
                _resourceManager.RegisterResource("MainForm_UIUpdateTimer", _uiUpdateTimer);
                
                // 启动定时器
                _uiUpdateTimer.Start();
                
                // 注册状态更新处理器
                _statusUpdateService.RegisterStatusUpdateHandler(OnStatusUpdate);

            }
            catch (Exception ex)
            {
                MessageBox.Show($"主窗体初始化失败:\n\n{ex.Message}\n\n详细信息:\n{ex.StackTrace}",
                    "严重错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                throw; // 重新抛出异常，因为这是严重的初始化错误
            }

            // 在构造函数中设置关联是安全的，因为控件已经通过InitializeComponent创建
            Header.TabControl = MainTabControl;

            // 初始化用户信息显示
            InitializeUserInfo();
        }



        private void Header_MenuItemClick(string text, int menuIndex, int pageIndex)
        {
            Footer.Text = "PageIndex: " + pageIndex;
        }
        
        /// <summary>
        /// 状态更新处理器，用于接收状态更新服务的通知
        /// </summary>
        /// <param name="status">新的状态文本</param>
        private void OnStatusUpdate(string status)
        {
            try
            {
                if (LalSoftwareState.InvokeRequired)
                {
                    LalSoftwareState.Invoke(new Action(() => LalSoftwareState.Text = status));
                }
                else
                {
                    LalSoftwareState.Text = status;
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError(ex, $"状态更新处理器执行失败: {ex.Message}", EventIds.Status_Update_Handler_Error);
            }
        }

        private void InitializeUserInfo()
        {
            // 在UiUpdateTimer_Tick方法中会更新用户信息显示，这里只需要初始化事件处理
            // 给UiPanelTip添加单击事件处理，用于快速修改密码
            UiPanelTip.Click += UiPanelTip_Click;
            UiPanelTip.Cursor = Cursors.Hand;
        }

        private void UiPanelTip_Click(object sender, EventArgs e)
        {
            ShowChangePasswordDialog();
        }

        private void ShowChangePasswordDialog()
        {
            try
            {
                // 获取用户管理服务
                // 不再需要通过CommonFun.host.Services获取服务，而是使用构造函数中注入的服务

                if (_userContext.CurrentUser != null)
                {
                    // 创建修改密码对话框
                    using (var changePasswordDialog = new WaferAligner.Forms.Pages.FChangePasswordDialog(_loggingService, _userContext, _userContext.CurrentUser.Username))
                    {
                        if (changePasswordDialog.ShowDialog(this) == DialogResult.OK)
                        {
                            if (changePasswordDialog.PasswordChanged)
                            {
                                MessageBox.Show("密码修改成功！", "成功",
                                    MessageBoxButtons.OK, MessageBoxIcon.Information);

                                _loggingService?.LogInformation($"用户 {_userContext.CurrentUser.Username} 修改了自己的密码",
                                    EventIds.User_Self_Password_Changed);
                            }
                        }
                    }
                }
                else
                {
                    MessageBox.Show("用户信息无效，请重新登录", "错误",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"打开修改密码对话框时发生错误: {ex.Message}",
                    EventIds.Change_Password_Dialog_Error);
                MessageBox.Show($"打开修改密码对话框失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }




        private async void FHeaderMainFooter_Load(object sender, EventArgs e)
        {
            try
            {
                // 获取性能监控器
                // 性能监控器已通过构造函数注入
                if (_perfMonitor != null)
                {
                    _perfMonitor.StartOperation("主窗体加载");
                }
                
                // 创建并显示加载指示器
                ShowLoadingIndicator("正在加载页面...");
                
                // 阶段1：仅加载UI框架和第一个页面
                _perfMonitor?.StartNamedOperation("CreateFTP1");
                FTP1 = new FTitlePage1(_statusUpdateService);
                FTP1.Enabled = true;
                _perfMonitor?.EndNamedOperation("CreateFTP1");

                _perfMonitor?.StartNamedOperation("AddFirstPage");
                AddPage(FTP1, 1001);
                Header.CreateNode("键合对准", 1001);
                _perfMonitor?.EndNamedOperation("AddFirstPage");
                
                // 更新加载指示器
                UpdateLoadingIndicator(20, "正在加载其他页面...");
                _loggingService?.LogInformation("第一阶段加载完成，开始加载其他页面", EventIds.Main_Form_Load_Phase);

                // 阶段2：异步创建其他页面实例
                await Task.Run(() => 
                {
                    try
                    {
                        // 根据用户权限决定是否创建对准参数页面
                        if (_userContext.CanConfigParameters())
                        {
                            _perfMonitor?.StartNamedOperation("CreateFTP2");
                            FTP2 = new FTitlePage2(_statusUpdateService);
                            FTP2.Enabled = true;
                            _perfMonitor?.EndNamedOperation("CreateFTP2");
                        }

                        // 更新加载指示器
                        UpdateLoadingIndicator(40, "正在加载运动参数页面...");

                        // 根据用户权限决定是否创建运动参数页面
                        if (_userContext.CanConfigMotion())
                        {
                            _perfMonitor?.StartNamedOperation("CreateFTP3");
                            FTP3 = new FTitlePage3(_statusUpdateService);
                            FTP3.Enabled = true;
                            _perfMonitor?.EndNamedOperation("CreateFTP3");
                        }

                        // 更新加载指示器
                        UpdateLoadingIndicator(60, "正在加载用户管理页面...");

                        // 根据用户权限决定是否创建用户管理页面
                        if (_userContext.CanManageUsers())
                        {
                            _perfMonitor?.StartNamedOperation("CreateFTP5");
                            FTP5 = new FTitlePage4(_statusUpdateService);
                            FTP5.Enabled = true;
                            _perfMonitor?.EndNamedOperation("CreateFTP5");
                        }
                    }
                    catch (Exception ex)
                    {
                        _loggingService?.LogError(ex, "异步创建页面时发生错误", EventIds.Page_Load_Phase);
                    }
                });
                
                // 更新加载指示器
                UpdateLoadingIndicator(80, "正在添加页面到UI...");

                // 阶段3：在UI线程中添加页面到主窗体
                _perfMonitor?.StartNamedOperation("AddSecondaryPages");

                // 根据用户权限添加页面
                if (_userContext.CanConfigParameters() && FTP2 != null)
                {
                    if (!IsPageAdded(FTP2))
                    {
                        AddPage(FTP2, 1002);
                        Header.CreateNode("对准参数", 1002);
                    }
                }

                if (_userContext.CanConfigMotion() && FTP3 != null)
                {
                    if (!IsPageAdded(FTP3))
                    {
                        AddPage(FTP3, 1003);
                        Header.CreateNode("运动参数", 1003);
                    }
                }

                if (_userContext.CanManageUsers() && FTP5 != null)
                {
                    if (!IsPageAdded(FTP5))
                    {
                        AddPage(FTP5, 1005);
                        Header.CreateNode("用户管理", 1005);
                    }
                }

                _perfMonitor?.EndNamedOperation("AddSecondaryPages");

                #region  初始化

                // 使用_mainWindowViewModel替代ConstValue.MAINWINDOWVIEWMODEL
                _mainWindowViewModel.RegistryAction($"{AxisConstants.AXIS_GVL}.初始化step", (obj) =>
                {
                    //LalStepNum.Text = obj.ToString();
                    if (obj.ToString() == "330" && LalSoftwareState.Text == "设备初始化中...")
                    {
                        LalSoftwareState.Text = "设备初始化完成";
                    }
                });

                #endregion  初始化

                _loggingService?.LogInformation("页面初始化完成，UINavBar将自动选择默认页面",
                    EventIds.Header_Auto_Selection);

                // 更新加载指示器
                UpdateLoadingIndicator(90, "正在初始化UI...");

                _perfMonitor?.StartNamedOperation("FinalizeUI");
                _uiUpdateTimer.Start();
                UIStyle style = UIStyle.Gray;
                StyleManager1.Style = style;

                // 显式刷新Header控件，确保所有选项卡立即可见
                _perfMonitor?.StartNamedOperation("RefreshUINavigation");
                Header.Refresh();
                Header.Update();
                // 选择第一个选项卡，确保默认页面显示
                Header.SelectedIndex = 0;
                _perfMonitor?.EndNamedOperation("RefreshUINavigation");
                
                _perfMonitor?.EndNamedOperation("FinalizeUI");

                // 标记窗体已完全加载
                _isFormLoaded = true;

                // 现在可以安全地更新页面权限了
                UpdatePagePermissions();

                // 结束性能监控
                if (_perfMonitor != null)
                {
                    _perfMonitor.EndOperation();
                }

                // 隐藏加载指示器
                HideLoadingIndicator();

                _loggingService?.LogInformation("FHeaderMainFooter窗体加载完成",
                    EventIds.Main_Form_Load_Completed);
            }
            catch (Exception ex)
            {
                // 隐藏加载指示器
                HideLoadingIndicator();
                
                MessageBox.Show($"主窗体初始化失败:\n\n{ex.Message}\n\n详细信息:\n{ex.StackTrace}",
                    "严重错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                throw; // 重新抛出异常，因为这是严重的初始化错误
            }
        }

        /// <summary>
        /// 显示加载指示器
        /// </summary>
        private void ShowLoadingIndicator(string message)
        {
            try
            {
                if (_loadingIndicator == null)
                {
                    _loadingIndicator = new LoadingIndicator();
                    _loadingIndicator.Location = new Point(
                        (this.ClientSize.Width - _loadingIndicator.Width) / 2,
                        (this.ClientSize.Height - _loadingIndicator.Height) / 2);
                    _loadingIndicator.Title = "正在加载系统";
                    _loadingIndicator.Progress = 0;
                    this.Controls.Add(_loadingIndicator);
                    _loadingIndicator.BringToFront();
                }
                
                _loadingIndicator.Message = message;
                _loadingIndicator.Visible = true;
            }
            catch (Exception ex)
            {
                _loggingService?.LogError(ex, "显示加载指示器失败", EventIds.UI_Update_Failed);
            }
        }
        
        /// <summary>
        /// 更新加载指示器
        /// </summary>
        private void UpdateLoadingIndicator(int progress, string message)
        {
            try
            {
                if (_loadingIndicator != null && !this.IsDisposed)
                {
                    if (this.InvokeRequired)
                    {
                        this.Invoke(new Action(() => 
                        {
                            _loadingIndicator.Progress = progress;
                            _loadingIndicator.Message = message;
                        }));
                    }
                    else
                    {
                        _loadingIndicator.Progress = progress;
                        _loadingIndicator.Message = message;
                    }
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError(ex, "更新加载指示器失败", EventIds.UI_Update_Failed);
            }
        }
        
        /// <summary>
        /// 隐藏加载指示器
        /// </summary>
        private void HideLoadingIndicator()
        {
            try
            {
                if (_loadingIndicator != null && !this.IsDisposed)
                {
                    if (this.InvokeRequired)
                    {
                        this.Invoke(new Action(() => 
                        {
                            _loadingIndicator.Visible = false;
                            this.Controls.Remove(_loadingIndicator);
                            _loadingIndicator.Dispose();
                            _loadingIndicator = null;
                        }));
                    }
                    else
                    {
                        _loadingIndicator.Visible = false;
                        this.Controls.Remove(_loadingIndicator);
                        _loadingIndicator.Dispose();
                        _loadingIndicator = null;
                    }
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError(ex, "隐藏加载指示器失败", EventIds.UI_Update_Failed);
            }
        }

        private async void BtnAllHome_Click(object sender, EventArgs e)
        {
            try
            {
                _loggingService?.LogInformation("开始设备初始化", EventIds.Device_Init_Started);
                LalSoftwareState.Text = "设备开始初始化";
                
                if (MessageBox.Show("请将卡盘取走\r\n 卡盘取走后请点\"确定\"按钮", "提示") != DialogResult.OK)
                {
                    LalSoftwareState.Text = "卡盘未取走，设备无法初始化";
                    MessageBox.Show("卡盘未取走，设备无法初始化");
                    _loggingService?.LogWarning("用户取消了设备初始化（卡盘未取走）", EventIds.Device_Init_Cancelled);
                    return;
                }
                
                // 静态兼容层已移除，使用依赖注入的IMainWindowViewModel
                bool Res = await _mainWindowViewModel.Init();
                if (!Res)
                {
                    LalSoftwareState.Text = "设备初始化失败";
                    MessageBox.Show("设备初始化失败");
                    _loggingService?.LogError("设备初始化失败", EventIds.Device_Init_Failed);
                }
                else
                {
                    LalSoftwareState.Text = "设备初始化完成";
                    _loggingService?.LogInformation("设备初始化完成", EventIds.Device_Init_Completed);
                }
            }
            catch (Exception ex)
            {
                LalSoftwareState.Text = "设备初始化过程发生错误";
                _loggingService?.LogError($"设备初始化过程发生错误: {ex.Message}", EventIds.Device_Init_Failed);
                MessageBox.Show($"设备初始化过程发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        private void BtnClearAlarm_Click(object sender, EventArgs e)
        {
            try
            {
                _loggingService?.LogInformation("开始清除轴报警", EventIds.Clear_Alarm_Started);
                
                // 使用_plcConnectionManager检查连接状态
                bool isConnected = _plcConnectionManager != null && _plcConnectionManager.IsConnected("Main");
                
                if (!isConnected)
                {
                    _loggingService?.LogError("XYR轴的连接断开，无法清除报警", EventIds.Clear_Alarm_Failed);
                    MessageBox.Show("XYR轴的连接断开，无法控制XYR轴");
                    return;
                }
                
                // 使用_axisFactory获取轴视图模型
                if (_axisFactory != null)
                {
                    // 使用异步方法清除X、Y、R轴报警
                    Task.Run(async () =>
                    {
                        try
                        {
                            var xAxis = _axisFactory.GetXAxisViewModel();
                            var yAxis = _axisFactory.GetYAxisViewModel();
                            var rAxis = _axisFactory.GetRAxisViewModel();
                            
                            if (xAxis != null) await xAxis.ResetAsync();
                            if (yAxis != null) await yAxis.ResetAsync();
                            if (rAxis != null) await rAxis.ResetAsync();
                            
                            _loggingService?.LogInformation("XYR轴报警已清除", EventIds.Clear_Alarm_Completed);
                        }
                        catch (Exception ex)
                        {
                            _loggingService?.LogError(ex, $"清除轴报警过程中发生错误: {ex.Message}", EventIds.Clear_Alarm_Failed);
                        }
                    });
                }
                else
                {
                    _loggingService?.LogError("轴工厂服务不可用，无法清除轴报警", EventIds.Service_Unavailable);
                    MessageBox.Show("轴控制服务不可用，无法清除轴报警");
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError(ex, $"清除轴报警过程中发生错误: {ex.Message}", EventIds.Clear_Alarm_Failed);
                MessageBox.Show($"清除轴报警失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }


        private async void FHeaderMainFooter_FormClosing(object sender, FormClosingEventArgs e)
        {
            // 防止重复调用
            if (_isClosing)
                return;
            _isClosing = true;

            try
            {
                _loggingService?.LogWarning("程序开始关闭", EventIds.Program_Closing_Started);

                // 通知UI线程管理器应用正在关闭
                UIThreadManager.SetShuttingDown();

                // 立即停止所有Timer和后台任务，不等待
                Stop_All_Background_Tasks();
                
                // 注销状态更新处理器
                _statusUpdateService?.UnregisterStatusUpdateHandler(OnStatusUpdate);

                // 使用ResourceManager进行统一资源清理（3秒超时）
                var cleanupTask = Task.Run(async () =>
                {
                    await _resourceManager.BeginShutdownAsync(3000);
                    await PerformAdditionalCleanupAsync();
                });

                // 等待清理完成，但最多等待5秒
                if (!cleanupTask.Wait(5000))
                {
                    _loggingService?.LogWarning("清理操作超时(5秒)，强制退出", EventIds.Cleanup_Timeout);
                }
                else
                {
                    _loggingService?.LogInformation("正常清理完成", EventIds.Normal_Cleanup_Complete_Specific);
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"清理过程发生错误: {ex.Message}", EventIds.Cleanup_Error_Specific);
            }
            finally
            {
                // 无论如何都要强制退出
                _loggingService?.LogWarning("执行强制退出", EventIds.Force_Exit);
                Force_Exit();
            }
        }

        private bool _isClosing = false;

        /// <summary>
        /// MainWindowViewModel异步清理
        /// </summary>
        private async Task CleanupMainWindowViewModelAsync()
        {
            try
            {
                _loggingService?.LogInformation("开始清理MainWindowViewModel", EventIds.Start_Main_Window_View_Model_Cleanup);
                // 使用接口定义的CleanupAsync方法
                if (_mainWindowViewModel != null)
                {
                    await _mainWindowViewModel.CleanupAsync();
                }
                _loggingService?.LogInformation("MainWindowViewModel清理完成", EventIds.Main_Window_View_Model_Cleanup_Complete_Specific);
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"MainWindowViewModel清理时发生错误: {ex.Message}", EventIds.Main_Window_View_Model_Cleanup_Error_Specific);
            }
        }

        /// <summary>
        /// PLC连接异步清理
        /// </summary>
        private async Task CleanupPLCConnectionsAsync()
        {
            try
            {
                // 使用_plcConnectionManager替代直接访问ConstValue.PLC等静态引用
                if (_plcConnectionManager != null)
                {
                    _loggingService?.LogInformation("开始清理PLC连接", EventIds.Plc_Disconnection_Started);
                    await _plcConnectionManager.DisconnectAllAsync();
                    _loggingService?.LogInformation("PLC连接清理完成", EventIds.Plc_Cleanup_Complete_Specific);
                }
                else
                {
                    _loggingService?.LogWarning("无法清理PLC连接：PLC连接管理器不可用", EventIds.Plc_Cleanup_Error_Specific);
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError(ex, $"PLC清理过程发生错误: {ex.Message}", EventIds.Plc_Cleanup_Error_Specific);
            }
        }

        /// <summary>
        /// 执行额外的清理操作（补充ResourceManager未覆盖的部分）
        /// </summary>
        private async Task PerformAdditionalCleanupAsync()
        {
            try
            {
                // 并行清理子页面资源
                await CleanupPagesWithTimeout();

                // 标记所有PLC连接为关闭状态
                try
                {
                    if (_plcConnectionManager != null)
                    {
                        await _plcConnectionManager.DisconnectAllAsync();
                    }
                }
                catch (Exception ex) 
                {
                    _loggingService?.LogError(ex, $"关闭PLC连接时发生错误: {ex.Message}", EventIds.Plc_Cleanup_Error_Specific);
                }

                _loggingService?.LogInformation("额外清理流程完成", EventIds.Cleanup_Process_Complete_Specific);
            }
            catch (Exception ex)
            {
                _loggingService?.LogError(ex, $"额外清理过程发生错误: {ex.Message}", EventIds.Async_Cleanup_Error);
            }
        }

        private async Task PerformCleanupAsync()
        {
            try
            {
                // 1. 快速停止主Timer
                try
                {
                    _uiUpdateTimer?.Stop();
                    _uiUpdateTimer?.Dispose();
                    _uiUpdateTimer = null;
                }
                catch { }

                // 2. 并行清理子页面资源（有超时保护）
                await CleanupPagesWithTimeout();

                // 3. 清理MainWindowViewModel（无超时限制，确保完成）
                try
                {
                    _loggingService?.LogInformation("开始清理MainWindowViewModel", EventIds.Start_Main_Window_View_Model_Cleanup);
                    if (_mainWindowViewModel != null)
                    {
                        await _mainWindowViewModel.CleanupAsync();
                    }
                    _loggingService?.LogInformation("MainWindowViewModel清理完成", EventIds.Main_Window_View_Model_Cleanup_Complete_Specific);
                }
                catch (Exception ex)
                {
                    _loggingService?.LogError($"MainWindowViewModel清理时发生错误: {ex.Message}", EventIds.Main_Window_View_Model_Cleanup_Error_Specific);
                }

                // 4. 并行清理PLC连接（有超时保护）
                await CleanupPLCWithTimeout();

                // 5. 取消后台任务
                try
                {
                    _cancellationTokenSource?.Cancel();
                    _cancellationTokenSource?.Dispose();
                    _cancellationTokenSource = null;
                }
                catch { }

                // 6. 标记所有PLC连接为关闭状态
                try
                {
                    if (_plcConnectionManager != null)
                    {
                        await _plcConnectionManager.DisconnectAllAsync();
                    }
                }
                catch (Exception ex) 
                {
                    _loggingService?.LogError(ex, $"关闭PLC连接时发生错误: {ex.Message}", EventIds.Plc_Cleanup_Error_Specific);
                }

                _loggingService?.LogInformation("程序清理流程完成", EventIds.Cleanup_Process_Complete_Specific);
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"异步清理过程发生错误: {ex.Message}", EventIds.Async_Cleanup_Error);
            }
        }

        private async Task CleanupPagesWithTimeout()
        {
            var pageCleanupTasks = new List<Task>();

            if (FTP1 != null)
                pageCleanupTasks.Add(Task.Run(() => SafeCleanup(() => FTP1.CleanUp(), "FTP1")));

            if (FTP2 != null)
                pageCleanupTasks.Add(Task.Run(() => SafeCleanup(() => FTP2.CleanUp(), "FTP2")));

            if (FTP3 != null)
                pageCleanupTasks.Add(Task.Run(() => SafeCleanup(() => FTP3.CleanUp(), "FTP3")));

            if (FTP5 != null)
                pageCleanupTasks.Add(Task.Run(() => SafeCleanup(() => FTP5.CleanUp(), "FTP5")));

            if (pageCleanupTasks.Count > 0)
            {
                try
                {
                    var pageCleanupTimeout = Task.Delay(1000); // 1秒超时
                    var completedTask = await Task.WhenAny(Task.WhenAll(pageCleanupTasks), pageCleanupTimeout);

                    if (completedTask == pageCleanupTimeout)
                    {
                        _loggingService?.LogWarning($"页面清理超时(1秒)，已启动{pageCleanupTasks.Count}个清理任务，继续执行后续清理",
                            EventIds.Page_Cleanup_Timeout);
                    }
                    else
                    {
                        _loggingService?.LogInformation("页面清理完成", EventIds.Page_Cleanup_Complete_Header);
                    }
                }
                catch (Exception ex)
                {
                    _loggingService?.LogError($"页面清理过程发生错误: {ex.Message}", EventIds.Page_Cleanup_Error_Header);
                }
            }
        }

        private async Task CleanupPLCWithTimeout()
        {
            try
            {
                if (_plcConnectionManager != null)
                {
                    _loggingService?.LogInformation("开始清理PLC连接（带超时保护）", EventIds.Plc_Disconnection_Started);
                    
                    // 使用超时保护机制
                    var plcCleanupTask = _plcConnectionManager.DisconnectAllAsync();
                    var plcCleanupTimeout = Task.Delay(1000); // 1秒超时
                    
                    var completedTask = await Task.WhenAny(plcCleanupTask, plcCleanupTimeout);
                    
                    if (completedTask == plcCleanupTimeout)
                    {
                        _loggingService?.LogWarning("PLC清理超时(1秒)，继续执行程序退出",
                            EventIds.Plc_Cleanup_Timeout);
                    }
                    else
                    {
                        _loggingService?.LogInformation("PLC清理完成", EventIds.Plc_Cleanup_Complete_Specific);
                    }
                }
                else
                {
                    _loggingService?.LogWarning("PLC连接管理器不可用，跳过PLC清理", EventIds.Plc_Cleanup_Error_Specific);
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError(ex, $"PLC清理过程发生错误: {ex.Message}", EventIds.Plc_Cleanup_Error_Specific);
            }
        }

        private void SafeCleanup(Action cleanupAction, string componentName)
        {
            try
            {
                cleanupAction?.Invoke();
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"清理{componentName}时发生错误: {ex.Message}", EventIds.Component_Cleanup_Error);
            }
        }

        private void SafeDispose(object obj, string objectName)
        {
            try
            {
                if (obj is IDisposable disposable)
                {
                    disposable.Dispose();
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"释放{objectName}时发生错误: {ex.Message}", EventIds.Object_Dispose_Error);
            }
        }

        private void Force_Exit()
        {
            try
            {
                _loggingService?.LogWarning("开始强制退出程序", EventIds.Force_Exit_Started);

                // 1. 立即停止所有可能的Timer和后台任务
                Stop_All_Background_Tasks();

                // 2. 快速退出UI线程
                System.Windows.Forms.Application.ExitThread();

                // 3. 异步强制退出，给清理操作一点时间但不等太久
                Task.Run(async () =>
                {
                    try
                    {
                        // 给清理操作最多1秒时间
                        await Task.Delay(1000);

                        _loggingService?.LogWarning("执行Environment.Exit(0)", EventIds.Environment_Exit);
                        Environment.Exit(0);
                    }
                    catch
                    {
                        // 如果Environment.Exit失败，使用Kill
                        try
                        {
                            _loggingService?.LogWarning("Environment.Exit失败，使用Process.Kill", EventIds.Process_Kill);
                            System.Diagnostics.Process.GetCurrentProcess().Kill();
                        }
                        catch
                        {
                            // 最后的手段，直接终止
                            System.Environment.FailFast("强制退出程序");
                        }
                    }
                });

                // 4. 如果异步退出也失败，2秒后强制Kill
                Task.Run(async () =>
                {
                    await Task.Delay(2000);
                    try
                    {
                        System.Diagnostics.Process.GetCurrentProcess().Kill();
                    }
                    catch
                    {
                        System.Environment.FailFast("强制退出程序");
                    }
                });
            }
            catch
            {
                // 如果所有方法都失败，直接终止进程
                try
                {
                    System.Diagnostics.Process.GetCurrentProcess().Kill();
                }
                catch
                {
                    System.Environment.FailFast("强制退出程序");
                }
            }
        }

        private void Stop_All_Background_Tasks()
        {
            try
            {
                _loggingService?.LogWarning("开始停止所有后台任务", EventIds.Stop_All_Background_Tasks);

                // 1. 立即停止主Timer
                try
                {
                    _uiUpdateTimer?.Stop();
                    _uiUpdateTimer?.Dispose();
                    _uiUpdateTimer = null;
                }
                catch { }

                // 2. 强制停止页面中的所有Timer（通过反射）
                StopAllTimersInPages();

                // 3. 取消所有可能的后台任务
                try
                {
                    _cancellationTokenSource?.Cancel();
                    _cancellationTokenSource?.Dispose();
                    _cancellationTokenSource = null;
                }
                catch { }

                // 4. 强制关闭PLC连接
                ForceClosePLCConnections();

                // 5. 终止所有应用程序域中的后台线程
                TerminateAllBackgroundThreads();

                _loggingService?.LogInformation("后台任务停止完成", EventIds.Background_Tasks_Stopped);
            }
            catch (Exception ex)
            {
                _loggingService?.LogError($"停止后台任务时发生错误: {ex.Message}", EventIds.Stop_Background_Tasks_Error);
            }
        }

        private void ForceClosePLCConnections()
        {
            try
            {
                _loggingService?.LogWarning("强制关闭PLC连接", EventIds.Force_Exit_Started);
                
                // 使用_plcConnectionManager替代直接访问静态类
                if (_plcConnectionManager != null)
                {
                    // 异步断开所有PLC连接
                    Task.Run(async () =>
                    {
                        try
                        {
                            await _plcConnectionManager.DisconnectAllAsync();
                            _loggingService?.LogInformation("已强制关闭所有PLC连接", EventIds.Plc_Disconnection_Completed);
                        }
                        catch (Exception ex)
                        {
                            _loggingService?.LogError(ex, $"强制关闭PLC连接时发生错误: {ex.Message}", EventIds.Plc_Disconnect_Exception);
                        }
                    });
                }
                else
                {
                    _loggingService?.LogWarning("PLC连接管理器不可用，无法关闭PLC连接", EventIds.Plc_Unavailable);
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError(ex, $"强制关闭PLC连接时发生错误: {ex.Message}", EventIds.Plc_Disconnect_Exception);
            }
        }

        private void TerminateAllBackgroundThreads()
        {
            try
            {
                // 获取当前进程的所有线程
                var process = System.Diagnostics.Process.GetCurrentProcess();
                var threads = process.Threads;

                _loggingService?.LogWarning($"当前进程有 {threads.Count} 个线程", EventIds.Thread_Count);

                // 设置线程优先级为最低，让它们尽快结束
                foreach (System.Diagnostics.ProcessThread thread in threads)
                {
                    try
                    {
                        if (thread.ThreadState == System.Diagnostics.ThreadState.Running)
                        {
                            // 注意：不能直接终止线程，这里只是降低优先级
                            // 实际的线程终止需要通过其他方式
                        }
                    }
                    catch { }
                }
            }
            catch { }
        }

        private void StopAllTimersInPages()
        {
            try
            {
                _loggingService?.LogInformation("开始停止所有页面中的定时器", EventIds.Timer_Stop_Error);

                // 获取当前加载的所有页面
                foreach (var page in this.Controls.OfType<UIPage>())
                {
                    try
                    {
                        // 获取页面中的所有Timer字段
                        var pageType = page.GetType();
                        var fields = pageType.GetFields(System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

                        foreach (var field in fields)
                        {
                            if (field.FieldType == typeof(TimerWrapper) ||
                                field.FieldType == typeof(System.Timers.Timer) ||
                                field.FieldType == typeof(System.Windows.Forms.Timer))
                            {
                                var timer = field.GetValue(page);
                                if (timer != null)
                                {
                                    // 停止Timer
                                    var stopMethod = timer.GetType().GetMethod("Stop");
                                    stopMethod?.Invoke(timer, null);

                                    // 如果是IDisposable，释放它
                                    if (timer is IDisposable disposableTimer)
                                    {
                                        disposableTimer.Dispose();
                                    }
                                }
                            }
                        }
                    }
                    catch
                    {
                        // 单个页面失败不影响其他页面
                    }
                }
            }
            catch
            {
                // 反射操作失败也不影响程序退出
            }
        }

        private bool ReadFromXML(string FileName)
        {
            try
            {
                XmlOperation oper = new();
                oper.LoadXML(FileName);
                string temp = oper.Select(FileName, "软件有效期");
                SoftEncry = Convert.ToBoolean(temp);
                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message.ToString());
                Application.Exit();
                return false;
            }
        }

        public bool WriteToXML(string FileName)
        {
            try
            {
                if (System.IO.File.Exists(FileName))
                {
                    System.IO.File.Delete(FileName);
                }
                XmlOperation oper = new();
                oper.CreateXMLTreeAndSave(FileName);
                oper.Create(FileName, "软件有效期", SoftEncry.ToString());
                oper.SaveXML(FileName);
                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message.ToString());
                return false;
            }
        }

        private void LalSoftwareState_TextChanged(object sender, EventArgs e)
        {
            //if (e.ToString() == "设备初始化完成")
            //{
            //    FTP1.Enabled = true;
            //    FTP2.Enabled = true;
            //    FTP3.Enabled = true;
            //}
        }

        private void UiUpdateTimer_Tick(object sender, System.Timers.ElapsedEventArgs e)
        {
            try
            {
                if (this.IsDisposed || !this.IsHandleCreated)
                    return;
                    
                // 安全地在UI线程上更新UI
                this.Invoke((MethodInvoker)(() => {
                    UiPanelTime.Text = DateTime.Now.DateTimeString();

                    // 更新用户信息显示
                    if (_userContext.CurrentUser != null)
                    {
                        string roleDisplay = _userContext.GetRoleDisplayName();
                        UiPanelTip.Text = $"当前用户: {_userContext.CurrentUser.Username} ({roleDisplay}) | 单击修改密码";
                    }
                    else
                    {
                        UiPanelTip.Text = "未登录 | 单击登录";
                    }
                }));
            }
            catch (Exception ex)
            {
                // 记录错误但不阻止定时器继续运行
                _loggingService?.LogWarning(ex, "UI更新定时器执行时发生错误", EventIds.Timer_Error);
            }
        }

        private async void btnStop_Click(object sender, EventArgs e)
        {
            try
            {
                _loggingService?.LogWarning("紧急停止按钮被按下，开始停止所有轴运动", EventIds.Emergency_Stop_Pressed);
                btnStop.Enabled = false;
                
                // 使用已注入的服务替代获取ConstValueCompatibilityService
                if (_axisFactory == null || _mainWindowViewModel == null)
                {
                    _loggingService?.LogError("轴工厂或主窗口视图模型服务不可用，无法执行紧急停止", EventIds.Service_Unavailable);
                    btnStop.Enabled = true;
                    return;
                }
                
                try
                {
                    // 获取Z轴并执行特殊停止操作
                    var zAxis = _axisFactory.GetZAxisViewModel();
                    if (zAxis != null)
                    {
                        await zAxis.ZHomeStop();
                        await zAxis.ZTakeForceStop();
                        _loggingService?.LogInformation("Z轴特殊停止操作已执行", EventIds.Emergency_Stop_Completed);
                    }
                    
                    // 停止XYR轴
                    var xAxis = _axisFactory.GetXAxisViewModel();
                    var yAxis = _axisFactory.GetYAxisViewModel();
                    var rAxis = _axisFactory.GetRAxisViewModel();
                    
                    if (xAxis != null) await xAxis.StopAsync();
                    if (yAxis != null) await yAxis.StopAsync();
                    if (rAxis != null) await rAxis.StopAsync();
                    
                    // 停止相机轴
                    var lxAxis = _axisFactory.GetLXAxisViewModel();
                    var lyAxis = _axisFactory.GetLYAxisViewModel();
                    var lzAxis = _axisFactory.GetLZAxisViewModel();
                    var rxAxis = _axisFactory.GetRXAxisViewModel();
                    var ryAxis = _axisFactory.GetRYAxisViewModel();
                    var rzAxis = _axisFactory.GetRZAxisViewModel();
                    
                    if (lxAxis != null) await lxAxis.StopAsync();
                    if (lyAxis != null) await lyAxis.StopAsync();
                    if (lzAxis != null) await lzAxis.StopAsync();
                    if (rxAxis != null) await rxAxis.StopAsync();
                    if (ryAxis != null) await ryAxis.StopAsync();
                    if (rzAxis != null) await rzAxis.StopAsync();
                    
                    _loggingService?.LogInformation("所有轴运动已停止", EventIds.Emergency_Stop_Completed);
                }
                catch (Exception ex)
                {
                    _loggingService?.LogError(ex, $"停止轴运动过程中发生错误: {ex.Message}", EventIds.Axis_Stop_Error);
                }
                finally
                {
                    btnStop.Enabled = true;
                }
            }
            catch (Exception ex)
            {
                _loggingService?.LogError(ex, $"紧急停止过程中发生错误: {ex.Message}", EventIds.Axis_Move_Error);
                btnStop.Enabled = true;
            }
        }

        private void T_Update_Tick(object sender, EventArgs e)
        {

        }

        private void uiSymbolButton1_Click(object sender, EventArgs e)
        {

        }

        /// <summary>
        /// 检查页面是否已添加到TabControl
        /// </summary>
        /// <param name="page">要检查的页面</param>
        /// <returns>页面是否已添加</returns>
        private bool IsPageAdded(UIPage page)
        {
            try
            {
                if (page == null || MainTabControl == null)
                    return false;
                    
                // 检查页面是否已经在TabControl中
                // 通过遍历TabPages集合并检查每个页面的Text属性
                foreach (TabPage tabPage in MainTabControl.TabPages)
                {
                    // 使用页面的Text属性进行比较
                    if (tabPage.Text == page.Text)
                    {
                        return true;
                    }
                }
                
                return false;
            }
            catch (Exception ex)
            {
                _loggingService?.LogWarning($"检查页面是否已添加时发生错误: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 根据当前用户权限更新页面访问权限
        /// </summary>
        private bool _isFormLoaded = false;

        public void UpdatePagePermissions()
        {
            // 如果表单未加载，跳过权限更新
            if (!_isFormLoaded)
            {
                _loggingService?.LogInformation("跳过权限更新 - 表单未完全加载", EventIds.Skip_Permission_Update);
                return;
            }

            try
            {
                // 检查用户权限并显示/隐藏相关页面
                bool canConfigParameters = _userContext.CanConfigParameters();
                bool canConfigMotion = _userContext.CanConfigMotion();
                bool canManageUsers = _userContext.CanManageUsers();

                // 处理对准参数页面
                if (canConfigParameters && FTP2 != null)
                {
                    if (!IsPageAdded(FTP2))
                    {
                        AddPage(FTP2, 1002);
                        Header.CreateNode("对准参数", 1002);
                    }
                }
                // 处理运动参数页面
                if (canConfigMotion && FTP3 != null)
                {
                    if (!IsPageAdded(FTP3))
                    {
                        AddPage(FTP3, 1003);
                        Header.CreateNode("运动参数", 1003);
                    }
                }
                // 处理用户管理页面
                if (canManageUsers && FTP5 != null)
                {
                    if (!IsPageAdded(FTP5))
                    {
                        AddPage(FTP5, 1005);
                        Header.CreateNode("用户管理", 1005);
                    }
                }

                _loggingService?.LogInformation("页面权限已更新", EventIds.Page_Permissions_Updated);
            }
            catch (Exception ex)
            {
                _loggingService?.LogError(ex, "更新页面权限时发生错误", EventIds.Update_Page_Permissions_Error);
            }
        }

        /// <summary>
        /// 检查Header是否包含指定ID的节点
        /// </summary>
        /// <param name="nodeId">节点ID</param>
        /// <returns>是否存在该ID的节点</returns>
        private bool HasNode(int nodeId)
        {
            // 遍历所有节点，检查是否有匹配的NodeID
            foreach (TreeNode node in Header.Nodes)
            {
                if (node.Tag != null && node.Tag is int id && id == nodeId)
                {
                    return true;
                }
            }
            return false;
        }


        public void CleanUp()
        {
            // 注销所有子页面的StateWatch事件及资源
            FTP1?.CleanUp();
            FTP2?.CleanUp();
            FTP3?.CleanUp();
            FTP5?.CleanUp();
            // 其他资源清理可在此补充
        }
    }
}
