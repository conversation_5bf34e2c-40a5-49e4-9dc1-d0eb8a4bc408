<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0-windows</TargetFramework>
    <UseWindowsForms>true</UseWindowsForms>
    <EnableWindowsTargeting>true</EnableWindowsTargeting>
    <Nullable>enable</Nullable>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\Infrastructure\WaferAligner.Infrastructure.Common\WaferAligner.Infrastructure.Common.csproj" />
    <ProjectReference Include="..\..\..\Services\Service.Common\WaferAligner.Services.Abstractions\WaferAligner.Services.Abstractions.csproj" />
    <ProjectReference Include="..\..\..\Services\Service.Common\WaferAligner.Services.Extensions\WaferAligner.Services.Extensions.csproj" />
    <ProjectReference Include="..\..\Core\WaferAligner.EventIds\WaferAligner.EventIds.csproj" />
    <ProjectReference Include="..\..\..\Business\WaferAligner.Core.Business\WaferAligner.Core.Business.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.5" />
    <PackageReference Include="System.IO.Ports" Version="9.0.7" />
  </ItemGroup>

</Project>
