﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Configuration;
using Sunny.UI;
using WaferAligner.Infrastructure.Common;

namespace WaferAligner.Common
{
    public static class CommonFun
    {
        public static IHost host;
        
        /// <summary>
        /// 全局资源管理器实例，在应用程序启动时通过Program.cs初始化
        /// </summary>
        public static ResourceManager resourceManager;

        public static bool ShowAskDialog2(string message, bool showMask = false, UIMessageDialogButtons defaultButton = UIMessageDialogButtons.Cancel)
        {
            return UIMessageBox.ShowMessageDialog2(UIStyles.CurrentResources.AskTitle, message, UINotifierType.Ask, showMask, defaultButton);
        }


    }
}
