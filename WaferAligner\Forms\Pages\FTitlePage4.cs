// using AlignerUI; // 已迁移到WaferAligner.Models
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System.ComponentModel;
using WaferAligner;
using WaferAligner.Common;
using WaferAligner.Services.Logging.Abstractions;
using WaferAligner.Services.Logging.Extensions;
using WaferAligner.Services.UserManagement;
using System.Linq;
using WaferAligner.EventIds;
using WaferAligner.Forms.Pages;
using WaferAligner.Services;
using WaferAligner.Services.Logging.Models;

namespace Sunny.UI.Demo
{
    public partial class FTitlePage4 : BasePage, INotifyPropertyChanged
    {
        private IUserManagement _userManagement;

        #region Bottom
        private string autobiography = null;
        public Dictionary<Microsoft.Extensions.Logging.LogLevel, bool> LogLevels { get; set; } = new()
        {
            [Microsoft.Extensions.Logging.LogLevel.Trace] = false,
            [Microsoft.Extensions.Logging.LogLevel.Debug] = false,
            [Microsoft.Extensions.Logging.LogLevel.Information] = true,
            [Microsoft.Extensions.Logging.LogLevel.Warning] = true,
            [Microsoft.Extensions.Logging.LogLevel.Error] = true,
        };
        private readonly LogObserver<string> logObserver = new LogObserver<string>();
        #endregion Bottom

        public FTitlePage4(IStatusUpdateService statusUpdateService)
        {
            InitializeComponent();
            #region GetService
            _userManagement = GetRequiredService<IUserManagement>();
            #endregion GetService
            
            // 初始化DataGridView
            InitializeDataGridView();
            
            // 绑定按钮事件
            btn_Add.Click += BtnAdd_Click;
            btn_Delete.Click += BtnDelete_Click;
            
            // 绑定数据网格事件
            uiDataGridView1.CellDoubleClick += UiDataGridView1_CellDoubleClick;
            
            // 添加搜索功能
            AddSearchFunctionality();
        }

        #region Initialize
        protected override async Task OnInitializeAsync()
        {
            // 检查用户是否有权限访问用户管理页面
            if (!UserContext.CanAccessUserManagementPage())
            {
                Logger?.LogWarning($"用户 {UserContext.CurrentUser?.Username ?? "未知"} ({UserContext.GetRoleDisplayName()}) 尝试访问用户管理页面但没有权限。", EventIds.Unauthorized_Access_Attempt);
                
                // 显示权限不足的消息
                ShowWarning($"您没有权限访问用户管理页面。\n\n当前角色：{UserContext.GetRoleDisplayName()}\n只有管理员可以管理用户。");
                
                // 关闭当前页面或禁用控件
                this.Enabled = false;
                return;
            }
            
            Logger?.LogInformation($"用户 {UserContext.CurrentUser?.Username ?? "未知"} 成功访问用户管理页面", 
                EventIds.User_Management_Page_Accessed);
                
            // 加载用户列表
            LoadUserList();
            
            await base.OnInitializeAsync();
        }
        #endregion Initialize

        #region Initialize DataGridView
        private void InitializeDataGridView()
        {
            uiDataGridView1.Columns.Clear();
            uiDataGridView1.Columns.Add("Username", "用户名");
            uiDataGridView1.Columns.Add("Roles", "角色");
            uiDataGridView1.Columns.Add("Description", "描述");
            uiDataGridView1.Columns.Add("LastLoginDate", "最后登录时间");
            uiDataGridView1.Columns.Add("CreatedDate", "创建时间");
            
            // 设置列宽自适应
            foreach (DataGridViewColumn column in uiDataGridView1.Columns)
            {
                column.AutoSizeMode = DataGridViewAutoSizeColumnMode.AllCells;
            }
            // 最后一列填充剩余空间
            uiDataGridView1.Columns[uiDataGridView1.Columns.Count - 1].AutoSizeMode = DataGridViewAutoSizeColumnMode.Fill;
            
            // 设置只读
            uiDataGridView1.ReadOnly = true;
            uiDataGridView1.AllowUserToAddRows = false;
            uiDataGridView1.AllowUserToDeleteRows = false;
            uiDataGridView1.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            
            // 禁止用户调整列宽和行高
            uiDataGridView1.AllowUserToResizeColumns = false;
            uiDataGridView1.AllowUserToResizeRows = false;
            uiDataGridView1.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.DisableResizing;
            uiDataGridView1.RowHeadersWidthSizeMode = DataGridViewRowHeadersWidthSizeMode.DisableResizing;
            
            // 设置选中行的颜色
            uiDataGridView1.DefaultCellStyle.SelectionBackColor = System.Drawing.Color.LightBlue;
            uiDataGridView1.DefaultCellStyle.SelectionForeColor = System.Drawing.Color.Red;
            uiDataGridView1.DefaultCellStyle.ForeColor = System.Drawing.Color.Black;
            uiDataGridView1.DefaultCellStyle.BackColor = System.Drawing.Color.White;
            
            // 创建右键菜单
            CreateContextMenu();
        }

        private void CreateContextMenu()
        {
            var contextMenu = new ContextMenuStrip();
            
            var editMenuItem = new ToolStripMenuItem("编辑用户信息");
            editMenuItem.Click += (s, e) =>
            {
                if (uiDataGridView1.SelectedRows.Count > 0)
                {
                    var user = uiDataGridView1.SelectedRows[0].Tag as WaferAligner.Services.UserManagement.UserInfo;
                    if (user != null)
                    {
                        EditUser(user);
                    }
                }
            };
            
            var changePasswordMenuItem = new ToolStripMenuItem("修改密码");
            changePasswordMenuItem.Click += (s, e) =>
            {
                if (uiDataGridView1.SelectedRows.Count > 0)
                {
                    var user = uiDataGridView1.SelectedRows[0].Tag as WaferAligner.Services.UserManagement.UserInfo;
                    if (user != null)
                    {
                        ChangeUserPassword(user);
                    }
                }
            };
            
            var deleteMenuItem = new ToolStripMenuItem("删除用户");
            deleteMenuItem.Click += (s, e) => BtnDelete_Click(s, e);
            
            var refreshMenuItem = new ToolStripMenuItem("刷新列表");
            refreshMenuItem.Click += (s, e) => LoadUserList();
            
            contextMenu.Items.Add(editMenuItem);
            contextMenu.Items.Add(changePasswordMenuItem);
            contextMenu.Items.Add(new ToolStripSeparator());
            contextMenu.Items.Add(deleteMenuItem);
            contextMenu.Items.Add(new ToolStripSeparator());
            contextMenu.Items.Add(refreshMenuItem);
            
            uiDataGridView1.ContextMenuStrip = contextMenu;
        }

        private void AddSearchFunctionality()
        {
            // 创建搜索文本框（如果设计器中没有的话，可以动态添加）
            // 这里假设有一个搜索文本框控件，如果没有可以动态创建
            // 为演示目的，我们添加键盘快捷键来快速搜索
            this.KeyPreview = true;
            this.KeyDown += FTitlePage5_KeyDown;
        }

        private void FTitlePage5_KeyDown(object sender, KeyEventArgs e)
        {
            // Ctrl+F 打开搜索
            if (e.Control && e.KeyCode == Keys.F)
            {
                ShowSearchDialog();
                e.Handled = true;
            }
            // F5 刷新用户列表
            else if (e.KeyCode == Keys.F5)
            {
                LoadUserList();
                e.Handled = true;
            }
        }

        private void ShowSearchDialog()
        {
            string searchText = Microsoft.VisualBasic.Interaction.InputBox(
                "请输入要搜索的用户名或描述：", "搜索用户", "");
            
            if (!string.IsNullOrWhiteSpace(searchText))
            {
                SearchUsers(searchText);
            }
        }

        private void SearchUsers(string searchText)
        {
            try
            {
                var allUsers = _userManagement.GetAllUsers();
                var filteredUsers = allUsers.Where(u => 
                    u.Username.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                    (u.Description?.Contains(searchText, StringComparison.OrdinalIgnoreCase) ?? false) ||
                    string.Join(", ", u.Roles).Contains(searchText, StringComparison.OrdinalIgnoreCase)
                ).ToList();

                // 如果只找到一个用户，自动选中
                string selectUser = filteredUsers.Count == 1 ? filteredUsers.First().Username : null;
                DisplayUsers(filteredUsers, selectUser);
                
                Logger?.LogInformation($"搜索用户，找到{filteredUsers.Count} 个结果", 
                    EventIds.User_Search);
            }
            catch (Exception ex)
            {
                Logger?.LogError($"搜索用户时发生错误{ex.Message}", 
                    EventIds.User_Search_Error);
                MessageBox.Show($"搜索失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void DisplayUsers(IEnumerable<WaferAligner.Services.UserManagement.UserInfo> users)
        {
            DisplayUsers(users, null);
        }

        private void DisplayUsers(IEnumerable<WaferAligner.Services.UserManagement.UserInfo> users, string selectUserName)
        {
            uiDataGridView1.Rows.Clear();
            
            int adminCount = 0;
            int engineerCount = 0;
            int operatorCount = 0;
            int targetRowIndex = -1;
            
            foreach (var user in users)
            {
                var rowIndex = uiDataGridView1.Rows.Add();
                var row = uiDataGridView1.Rows[rowIndex];
                
                row.Cells["Username"].Value = user.Username;
                row.Cells["Roles"].Value = string.Join(", ", user.Roles);
                row.Cells["Description"].Value = user.Description ?? "";
                row.Cells["LastLoginDate"].Value = user.LastLoginDate?.ToString("yyyy-MM-dd HH:mm:ss") ?? "从未登录";
                row.Cells["CreatedDate"].Value = user.CreatedDate.ToString("yyyy-MM-dd HH:mm:ss");
                
                // 统计用户角色
                if (user.Roles.Contains("Admin"))
                    adminCount++;
                else if (user.Roles.Contains("Engineer"))
                    engineerCount++;
                else if (user.Roles.Contains("Operator"))
                    operatorCount++;
                
                // 默认所有用户字体为黑色
                row.DefaultCellStyle.ForeColor = System.Drawing.Color.Black;
                
                // 确保选中时的颜色设置为红色
                row.DefaultCellStyle.SelectionForeColor = System.Drawing.Color.Red;
                row.DefaultCellStyle.SelectionBackColor = System.Drawing.Color.LightBlue;
                
                // 存储用户对象引用
                row.Tag = user;
                
                // 记录目标行索引
                if (!string.IsNullOrEmpty(selectUserName) && user.Username == selectUserName)
                {
                    targetRowIndex = rowIndex;
                }
            }
            
            // 选中指定用户
            if (targetRowIndex >= 0 && targetRowIndex < uiDataGridView1.Rows.Count)
            {
                uiDataGridView1.ClearSelection();
                uiDataGridView1.Rows[targetRowIndex].Selected = true;
                uiDataGridView1.CurrentCell = uiDataGridView1.Rows[targetRowIndex].Cells[0];
                uiDataGridView1.FirstDisplayedScrollingRowIndex = targetRowIndex;
                
                Logger?.LogInformation($"自动选中用户: {selectUserName}", 
                    EventIds.User_Auto_Selected);
            }
            
            // 更新标题显示统计信息
            int totalUsers = users.Count();
            this.Text = $"用户管理 - 总计: {totalUsers} 个用户| 管理员{adminCount} | 工程师{engineerCount} | 操作员{operatorCount}";
        }
        #endregion

        #region Load User List
        private void LoadUserList()
        {
            LoadUserList(null);
        }

        private void LoadUserList(string selectUserName)
        {
            try
            {
                var users = _userManagement.GetAllUsers();
                DisplayUsers(users, selectUserName);
                
                Logger?.LogInformation($"用户列表加载完成，共 {users.Count()} 个用户", 
                    EventIds.User_List_Loaded);
            }
            catch (Exception ex)
            {
                Logger?.LogError($"加载用户列表时发生错误{ex.Message}", 
                    EventIds.Load_User_List_Error);
                MessageBox.Show($"加载用户列表失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        #endregion

        #region Button Events
        private void BtnAdd_Click(object sender, EventArgs e)
        {
            try
            {
                // 创建添加用户对话框
                using (var addUserDialog = new FAddUserDialog(_userManagement))
                {
                    if (addUserDialog.ShowDialog(this) == DialogResult.OK)
                    {
                        var newUser = addUserDialog.NewUser;
                        var password = addUserDialog.GetPassword();
                        
                        // 创建新用户
                        if (_userManagement.CreateUser(newUser, password))
                        {
                            MessageBox.Show($"用户 {newUser.Username} 创建成功", "成功", 
                                MessageBoxButtons.OK, MessageBoxIcon.Information);
                            
                            // 重新加载用户列表并选中新创建的用户
                            LoadUserList(newUser.Username);
                            
                            Logger?.LogInformation($"用户 {newUser.Username} 被 {UserContext.CurrentUser?.Username ?? "未知"} 创建", 
                                EventIds.User_Created);
                        }
                        else
                        {
                            MessageBox.Show("创建用户失败，请检查输入信息", "错误", 
                                MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Logger?.LogError($"添加用户时发生错误{ex.Message}", 
                    EventIds.Add_User_Error);
                MessageBox.Show($"添加用户失败: {ex.Message}", "错误", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnDelete_Click(object sender, EventArgs e)
        {
            if (uiDataGridView1.SelectedRows.Count == 0)
            {
                MessageBox.Show("请选择要删除的用户", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var selectedRow = uiDataGridView1.SelectedRows[0];
            var user = selectedRow.Tag as WaferAligner.Services.UserManagement.UserInfo;
            
            if (user == null)
            {
                MessageBox.Show("选择的用户信息无效", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            // 不能删除当前登录用户
            if (user.Username == UserContext.CurrentUser?.Username)
            {
                MessageBox.Show("不能删除当前登录的用户", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // 确认删除
            var result = MessageBox.Show($"确定要删除用户{user.Username} 吗？", "确认删除", 
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                
            if (result == DialogResult.Yes)
            {
                try
                {
                    if (_userManagement.DeleteUser(user.Username))
                    {
                        MessageBox.Show("用户删除成功", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        LoadUserList(); // 重新加载用户列表
                        
                        Logger?.LogInformation($"用户 {user.Username} 被删除", 
                            EventIds.User_Deleted);
                    }
                    else
                    {
                        MessageBox.Show("删除用户失败", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
                catch (Exception ex)
                {
                    Logger?.LogError($"删除用户时发生错误{ex.Message}", 
                        EventIds.Delete_User_Error);
                    MessageBox.Show($"删除用户失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void UiDataGridView1_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                var selectedRow = uiDataGridView1.Rows[e.RowIndex];
                var user = selectedRow.Tag as WaferAligner.Services.UserManagement.UserInfo;
                
                if (user != null)
                {
                    EditUser(user);
                }
            }
        }


        private void EditUser(WaferAligner.Services.UserManagement.UserInfo user)
        {
            try
            {
                // 不能编辑当前登录用户的角色（防止自己把自己的权限改没了）
                if (user.Username == UserContext.CurrentUser?.Username && user.Roles.Contains("Admin"))
                {
                    var result = MessageBox.Show("您正在编辑自己的账户信息。\n\n警告：如果您将自己的角色从管理员改为其他角色，将失去管理权限，无法再次修改用户信息。\n\n确定要继续吗？", 
                        "警告", MessageBoxButtons.YesNo, MessageBoxIcon.Warning);
                    
                    if (result != DialogResult.Yes)
                    {
                        return;
                    }
                }

                // 创建编辑用户对话框
                using (var editUserDialog = new FEditUserDialog(_userManagement, user))
                {
                    if (editUserDialog.ShowDialog(this) == DialogResult.OK)
                    {
                        var editedUser = editUserDialog.EditedUser;
                        
                        // 更新用户信息
                        if (_userManagement.UpdateUser(editedUser))
                        {
                            MessageBox.Show($"用户 {editedUser.Username} 信息更新成功", "成功", 
                                MessageBoxButtons.OK, MessageBoxIcon.Information);
                            
                            // 重新加载用户列表并选中被编辑的用户
                            LoadUserList(editedUser.Username);
                            
                            Logger?.LogInformation($"用户 {editedUser.Username} 的信息被 {UserContext.CurrentUser?.Username ?? "未知"} 修改", 
                                EventIds.User_Information_Updated);
                        }
                        else
                        {
                            MessageBox.Show("更新用户信息失败，请检查输入信息", "错误", 
                                MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Logger?.LogError($"编辑用户时发生错误{ex.Message}", 
                    EventIds.Edit_User_Error);
                MessageBox.Show($"编辑用户失败: {ex.Message}", "错误", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ChangeUserPassword(WaferAligner.Services.UserManagement.UserInfo user)
        {
            try
            {
                // 检查权限：只有管理员或用户自己才能修改密码
                bool canChange = UserContext.CurrentUser?.Username == user.Username || // 自己修改自己的密码
                                (UserContext.CurrentUser?.Roles?.Contains("Admin") == true); // 管理员修改其他用户密码

                if (!canChange)
                {
                    MessageBox.Show("您没有权限修改此用户的密码", "权限不足", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // 如果是管理员修改其他用户密码，需要确认
                if (UserContext.CurrentUser?.Username != user.Username && UserContext.CurrentUser?.Roles?.Contains("Admin") == true)
                {
                    var result = MessageBox.Show($"您正在修改用户{user.Username} 的密码。\n\n确定要继续吗？", 
                        "确认修改", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                    
                    if (result != DialogResult.Yes)
                    {
                        return;
                    }
                }

                // 创建修改密码对话框
                using (var changePasswordDialog = new FChangePasswordDialog(Logger, UserContext, user.Username))
                {
                    if (changePasswordDialog.ShowDialog(this) == DialogResult.OK)
                    {
                        if (changePasswordDialog.PasswordChanged)
                        {
                            MessageBox.Show($"用户 {user.Username} 的密码修改成功！", "成功", 
                                MessageBoxButtons.OK, MessageBoxIcon.Information);
                            
                            Logger?.LogInformation($"用户 {user.Username} 的密码被 {UserContext.CurrentUser?.Username ?? "未知"} 修改", 
                                EventIds.User_Password_Changed);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Logger?.LogError($"修改用户密码时发生错误{ex.Message}", 
                    EventIds.Change_Password_Error);
                MessageBox.Show($"修改密码失败: {ex.Message}", "错误", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        public event PropertyChangedEventHandler? PropertyChanged;

        protected override void OnDispose()
        {
            try
            {
                // BasePage会自动处理基础资源清理
                Logger?.LogInformation("FTitlePage5资源清理完成", EventIds.FTitle_Page5_Cleanup_Complete);
                
                base.OnDispose();
            }
            catch (Exception ex)
            {
                Logger?.LogError(ex, "FTitlePage5清理资源时发生错误", EventIds.Unhandled_Exception);
            }
        }

        // 保持向后兼容
        public void CleanUp()
        {
            try
            {
                // 转发到新的资源管理机制
                this.Dispose();
                
                Logger?.LogInformation("FTitlePage5资源清理完成", EventIds.FTitle_Page5_Cleanup_Complete);
            }
            catch (Exception ex)
            {
                Logger?.LogError(ex, "FTitlePage5清理资源时发生错误", EventIds.Unhandled_Exception);
            }
        }
    }
} 
