using WaferAligner.Services.UserManagement;
using WaferAligner.Core.Business;
using Sunny.UI;
using System;
using System.Linq;
using System.Windows.Forms;

namespace WaferAligner.Forms.Pages
{
    public partial class FEditUserDialog : UIForm
    {
        public UserInfo EditedUser { get; private set; }
        private IUserManagement _userManagement;
        private UserInfo _originalUser;

        public FEditUserDialog(IUserManagement userManagement, UserInfo userToEdit)
        {
            InitializeComponent();
            _userManagement = userManagement;
            _originalUser = userToEdit;
            InitializeForm();
            LoadUserData();
        }

        private void InitializeForm()
        {
            this.Text = "编辑用户信息";
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.StartPosition = FormStartPosition.CenterParent;
            this.Size = new System.Drawing.Size(420, 320);

            // 加载可用角色
            LoadAvailableRoles();
        }

        private void LoadAvailableRoles()
        {
            try
            {
                var roles = _userManagement.GetAllRoles();
                cmbRoles.Items.Clear();
                
                foreach (var role in roles)
                {
                    cmbRoles.Items.Add(role.Name);
                }
            }
            catch (Exception ex)
            {
                UIMessageBox.ShowError($"加载角色列表失败: {ex.Message}");
            }
        }

        private void LoadUserData()
        {
            try
            {
                // 加载用户数据到控件
                txtUsername.Text = _originalUser.Username;
                txtUsername.ReadOnly = true; // 用户名不允许修改
                txtUsername.BackColor = System.Drawing.Color.LightGray;
                
                txtDescription.Text = _originalUser.Description ?? "";
                
                // 设置角色选择
                if (_originalUser.Roles != null && _originalUser.Roles.Any())
                {
                    string userRole = _originalUser.Roles.First();
                    int roleIndex = cmbRoles.Items.IndexOf(userRole);
                    if (roleIndex >= 0)
                    {
                        cmbRoles.SelectedIndex = roleIndex;
                    }
                }
            }
            catch (Exception ex)
            {
                UIMessageBox.ShowError($"加载用户数据失败: {ex.Message}");
            }
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if (ValidateInput())
            {
                UpdateUser();
            }
        }

        private bool ValidateInput()
        {
            // 验证角色选择
            if (cmbRoles.SelectedIndex == -1)
            {
                UIMessageBox.ShowWarning("请选择用户角色");
                cmbRoles.Focus();
                return false;
            }

            return true;
        }

        private void UpdateUser()
        {
            try
            {
                EditedUser = new UserInfo
                {
                    Username = _originalUser.Username, // 用户名不变
                    Roles = new[] { cmbRoles.SelectedItem.ToString() },
                    Description = txtDescription.Text.Trim(),
                    IsActive = _originalUser.IsActive, // 保持原有状态
                    CreatedDate = _originalUser.CreatedDate,
                    LastLoginDate = _originalUser.LastLoginDate
                };

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                UIMessageBox.ShowError($"更新用户信息失败: {ex.Message}");
            }
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private void cmbRoles_SelectedIndexChanged(object sender, EventArgs e)
        {
            // 显示选中角色的权限说明
            if (cmbRoles.SelectedItem != null)
            {
                string roleName = cmbRoles.SelectedItem.ToString();
                string roleDescription = GetRoleDescription(roleName);
                lblRoleDescription.Text = roleDescription;
            }
        }

        private string GetRoleDescription(string roleName)
        {
            switch (roleName)
            {
                case "Admin":
                    return "管理员：拥有所有系统权限，可以管理用户和配置所有参数";
                case "Engineer":
                    return "工程师：可以管理用户、配置对准参数和运动参数";
                case "Operator":
                    return "操作员：只能进行基本的键合对准操作";
                default:
                    return "未知角色";
            }
        }
    }
} 