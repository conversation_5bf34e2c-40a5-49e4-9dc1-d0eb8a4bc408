﻿using System;
using System.Threading;
using System.Threading.Tasks;
using WaferAligner.Services.Logging.Abstractions;
using WaferAligner.Interfaces;
using WaferAligner.Services;
using WaferAligner.Communication.Inovance.Abstractions;
using WaferAligner.EventIds;
using Microsoft.Extensions.Logging;
using System.Collections.Generic; // Added for Dictionary
using AlignerUI; // 添加引用以使用AxisAction枚举

namespace WaferAligner.Models
{
    /// <summary>
    /// 相机轴视图模型实现，继承自PlcAxisViewModelBase，包含相机轴特有功能
    /// </summary>
    public class CameraAxisViewModelNew : PlcAxisViewModelBase, ICameraAxisViewModel
    {
        private readonly IAxisEventService _axisEventService;
        private string _cameraPosition;
        
        /// <summary>
        /// 相机位置："Left"或"Right"
        /// </summary>
        public string CameraPosition 
        { 
            get => _cameraPosition;
            set
            {
                if (string.IsNullOrEmpty(value))
                    throw new ArgumentNullException(nameof(value));
                
                if (value != "Left" && value != "Right")
                    throw new ArgumentException("相机位置必须是\"Left\"或\"Right\"", nameof(value));
                    
                _cameraPosition = value;
            }
        }
        
        /// <summary>
        /// 重写轴名属性，保持访问修饰符一致
        /// </summary>
        public override string AxisName 
        { 
            get => base.AxisName; 
            protected set => base.AxisName = value; 
        }

        /// <summary>
        /// 公开方法设置轴名称
        /// </summary>
        public void SetAxis(string axisName)
        {
            if (string.IsNullOrEmpty(axisName))
                throw new ArgumentNullException(nameof(axisName));
                
            AxisName = axisName;
        }
        
        // 轴名前缀映射
        private static readonly System.Collections.Generic.Dictionary<string, string> AxisPrefixMap = 
            new System.Collections.Generic.Dictionary<string, string>
            {
                ["Left"] = "L",
                ["Right"] = "R"
            };
        
        public CameraAxisViewModelNew(
            string axisName,
            string cameraPosition,
            IPlcCommunication plcCommunication,
            ILoggingService loggingService,
            IAxisEventService axisEventService) 
            : base(axisName, plcCommunication, loggingService)
        {
            if (string.IsNullOrEmpty(cameraPosition))
                throw new ArgumentNullException(nameof(cameraPosition));
            
            if (cameraPosition != "Left" && cameraPosition != "Right")
                throw new ArgumentException("相机位置必须是\"Left\"或\"Right\"", nameof(cameraPosition));
            
            _cameraPosition = cameraPosition;
            _axisEventService = axisEventService ?? throw new ArgumentNullException(nameof(axisEventService));
        }
        
        /// <summary>
        /// 无参构造函数，用于依赖注入，属性将在后续设置
        /// </summary>
        public CameraAxisViewModelNew(
            IPlcCommunication plcCommunication,
            ILoggingService loggingService,
            IAxisEventService axisEventService)
            : base(string.Empty, plcCommunication, loggingService)
        {
            _axisEventService = axisEventService ?? throw new ArgumentNullException(nameof(axisEventService));
            _cameraPosition = "Left"; // 默认值，需要后续设置
        }
        
        /// <summary>
        /// 获取当前位置，支持取消操作
        /// </summary>
        public async Task<double> GetCurrentPositionAsync(CancellationToken cancellationToken)
        {
            try
            {
                cancellationToken.ThrowIfCancellationRequested();
                return await ReadPlcVariableAsync<double>($"{AxisConstants.AXIS_GVL}.{AxisName}Position", cancellationToken);
            }
            catch (OperationCanceledException)
            {
                LogWarning($"{CameraPosition}相机{AxisName}轴获取当前位置操作被取消", WaferAligner.EventIds.EventIds.Operation_Cancelled);
                throw;
            }
            catch (Exception ex)
            {
                LogError(ex, $"{CameraPosition}相机{AxisName}轴获取当前位置失败: {ex.Message}", WaferAligner.EventIds.EventIds.Axis_Position_Read_Error);
                throw;
            }
        }

        #region 初始化和事件订阅
        
        public override async Task InitializeAsync()
        {
            await base.InitializeAsync();

            // 直接注册PLC变量，避免通过AxisEventService造成循环依赖
            // 注册位置变化事件
            RegisterAction("position", OnAxisPositionChanged);

            // 注册状态变化事件
            RegisterAction("state", OnAxisStateChanged);

            LogInformation($"{CameraPosition}相机{AxisName}轴初始化完成", WaferAligner.EventIds.EventIds.Axis_Initialize_Succeeded);
        }
        
        private void OnAxisPositionChanged(object value)
        {
            try
            {
                if (value is int position)
                {
                    OnPositionChanged(position);
                }
            }
            catch (Exception ex)
            {
                LogError(ex, $"处理{CameraPosition}相机{AxisName}轴位置事件失败: {ex.Message}", WaferAligner.EventIds.EventIds.Axis_Position_Read_Error);
            }
        }
        
        private void OnAxisStateChanged(object value)
        {
            try
            {
                // 解析状态对象
                dynamic stateData = value;
                bool isMoving = stateData?.IsMoving ?? false;
                bool isHomed = stateData?.IsHomed ?? false;
                bool isConnected = stateData?.IsConnected ?? false;
                
                OnStateChanged(isMoving, isHomed, isConnected);
            }
            catch (Exception ex)
            {
                LogError(ex, $"处理{CameraPosition}相机{AxisName}轴状态事件失败: {ex.Message}", WaferAligner.EventIds.EventIds.Axis_Position_Read_Error);
            }
        }
        
        #endregion
        
        #region 相机轴特有功能

/// <summary>
/// 移动到工作位置
/// </summary>
public async Task<bool> MoveToWorkPositionAsync(CancellationToken cancellationToken = default)
{
    try
    {
        cancellationToken.ThrowIfCancellationRequested();
        
        LogInformation($"{CameraPosition}相机{AxisName}轴移动到工作位置", WaferAligner.EventIds.EventIds.Axis_Operation_Info);
        int position = await GetPresetPosition(AxisName, "Work", cancellationToken);
        return await MoveToPositionAsync(position, cancellationToken);
    }
    catch (OperationCanceledException)
    {
        LogWarning($"{CameraPosition}相机{AxisName}轴移动到工作位置操作被取消", WaferAligner.EventIds.EventIds.Operation_Cancelled);
        throw;
    }
    catch (Exception ex)
    {
        LogError(ex, $"{CameraPosition}相机{AxisName}轴移动到工作位置失败: {ex.Message}", WaferAligner.EventIds.EventIds.Axis_Move_Error);
        return false;
    }
}

/// <summary>
/// 为向后兼容保留的方法
/// </summary>
[Obsolete("请使用带取消令牌的MoveToWorkPositionAsync")]
public Task<bool> MoveToWorkPositionAsync()
{
    return MoveToWorkPositionAsync(CancellationToken.None);
}

/// <summary>
/// 移动到安全位置
/// </summary>
public async Task<bool> MoveToSafePositionAsync(CancellationToken cancellationToken = default)
{
    try
    {
        cancellationToken.ThrowIfCancellationRequested();
        
        LogInformation($"{CameraPosition}相机{AxisName}轴移动到安全位置", WaferAligner.EventIds.EventIds.Axis_Operation_Info);
        int position = await GetPresetPosition(AxisName, "Safe", cancellationToken);
        return await MoveToPositionAsync(position, cancellationToken);
    }
    catch (OperationCanceledException)
    {
        LogWarning($"{CameraPosition}相机{AxisName}轴移动到安全位置操作被取消", WaferAligner.EventIds.EventIds.Operation_Cancelled);
        throw;
    }
    catch (Exception ex)
    {
        LogError(ex, $"{CameraPosition}相机{AxisName}轴移动到安全位置失败: {ex.Message}", WaferAligner.EventIds.EventIds.Axis_Move_Error);
        return false;
    }
}

/// <summary>
/// 为向后兼容保留的方法
/// </summary>
[Obsolete("请使用带取消令牌的MoveToSafePositionAsync")]
public Task<bool> MoveToSafePositionAsync()
{
    return MoveToSafePositionAsync(CancellationToken.None);
}

/// <summary>
/// 移动到观察位置
/// </summary>
public async Task<bool> MoveToObservePositionAsync(CancellationToken cancellationToken = default)
{
    try
    {
        cancellationToken.ThrowIfCancellationRequested();
        
        LogInformation($"{CameraPosition}相机{AxisName}轴移动到观察位置", WaferAligner.EventIds.EventIds.Axis_Operation_Info);
        int position = await GetPresetPosition(AxisName, "Observe", cancellationToken);
        return await MoveToPositionAsync(position, cancellationToken);
    }
    catch (OperationCanceledException)
    {
        LogWarning($"{CameraPosition}相机{AxisName}轴移动到观察位置操作被取消", WaferAligner.EventIds.EventIds.Operation_Cancelled);
        throw;
    }
    catch (Exception ex)
    {
        LogError(ex, $"{CameraPosition}相机{AxisName}轴移动到观察位置失败: {ex.Message}", WaferAligner.EventIds.EventIds.Axis_Move_Error);
        return false;
    }
}

/// <summary>
/// 为向后兼容保留的方法
/// </summary>
[Obsolete("请使用带取消令牌的MoveToObservePositionAsync")]
public Task<bool> MoveToObservePositionAsync()
{
    return MoveToObservePositionAsync(CancellationToken.None);
}

/// <summary>
/// 设置相机轴速度
/// </summary>
public async Task<bool> SetCameraSpeedAsync(double speed, CancellationToken cancellationToken = default)
{
    try
    {
        cancellationToken.ThrowIfCancellationRequested();
        
        LogInformation($"设置{CameraPosition}相机{AxisName}轴速度: {speed}", WaferAligner.EventIds.EventIds.Axis_Operation_Info);
        return await SetRunSpeedAsync(speed, cancellationToken);
    }
    catch (OperationCanceledException)
    {
        LogWarning($"设置{CameraPosition}相机{AxisName}轴速度操作被取消", WaferAligner.EventIds.EventIds.Operation_Cancelled);
        throw;
    }
    catch (Exception ex)
    {
        LogError(ex, $"设置{CameraPosition}相机{AxisName}轴速度失败: {ex.Message}", WaferAligner.EventIds.EventIds.Axis_Move_Error);
        return false;
    }
}

/// <summary>
/// 为向后兼容保留的方法
/// </summary>
[Obsolete("请使用带取消令牌的SetCameraSpeedAsync")]
public Task<bool> SetCameraSpeedAsync(double speed)
{
    return SetCameraSpeedAsync(speed, CancellationToken.None);
}

/// <summary>
/// 获取预设位置值
/// </summary>
private async Task<int> GetPresetPosition(string axisName, string positionType, CancellationToken cancellationToken = default)
{
    try
    {
        // 尝试从PLC读取预设位置值
        string variableName = $"{AxisConstants.AXIS_GVL}.{axisName}{positionType}Position";
        
        try
        {
            // 先尝试从PLC读取实际配置的位置值
            int position = await ReadPlcVariableAsync<int>(variableName, cancellationToken);
            LogInformation($"从PLC读取{CameraPosition}相机{axisName}轴{positionType}位置: {position}", 
                WaferAligner.EventIds.EventIds.Axis_Operation_Info);
            return position;
        }
        catch (Exception ex)
        {
            LogWarning($"从PLC读取位置失败: {ex.Message}，将使用本地缓存值", 
                WaferAligner.EventIds.EventIds.Plc_Variables_Read_Failed);
        }
        
        // 如果从PLC读取失败，使用硬编码缓存值作为备选
        var positionMap = new Dictionary<string, Dictionary<string, int>>
        {
            // 左相机X轴
            ["LX"] = new Dictionary<string, int>
            {
                ["Work"] = 1000,
                ["Safe"] = 0,
                ["Observe"] = 500
            },
            // 左相机Y轴
            ["LY"] = new Dictionary<string, int>
            {
                ["Work"] = 1000,
                ["Safe"] = 0,
                ["Observe"] = 500
            },
            // 左相机Z轴
            ["LZ"] = new Dictionary<string, int>
            {
                ["Work"] = 1000,
                ["Safe"] = 0,
                ["Observe"] = 500
            },
            // 右相机X轴
            ["RX"] = new Dictionary<string, int>
            {
                ["Work"] = 1000,
                ["Safe"] = 0,
                ["Observe"] = 500
            },
            // 右相机Y轴
            ["RY"] = new Dictionary<string, int>
            {
                ["Work"] = 1000,
                ["Safe"] = 0,
                ["Observe"] = 500
            },
            // 右相机Z轴
            ["RZ"] = new Dictionary<string, int>
            {
                ["Work"] = 1000,
                ["Safe"] = 0,
                ["Observe"] = 500
            }
        };

        if (positionMap.TryGetValue(axisName, out var positions) && 
            positions.TryGetValue(positionType, out int cachedPosition))
        {
            LogInformation($"使用本地缓存的{CameraPosition}相机{axisName}轴{positionType}位置: {cachedPosition}", 
                WaferAligner.EventIds.EventIds.Axis_Operation_Info);
            return cachedPosition;
        }

        LogWarning($"未找到{CameraPosition}相机{AxisName}轴{positionType}位置的预设值，使用默认值0", 
            WaferAligner.EventIds.EventIds.Axis_Operation_Info);
        return 0;
    }
    catch (OperationCanceledException)
    {
        throw;
    }
    catch (Exception ex)
    {
        LogError(ex, $"获取{CameraPosition}相机{axisName}轴{positionType}位置预设值失败", 
            WaferAligner.EventIds.EventIds.Axis_Operation_Failed);
        return 0; // 发生异常时返回默认值0
    }
}

/// <summary>
/// 判断相机轴是否在安全位置
/// </summary>
public async Task<bool> IsAtSafePositionAsync(CancellationToken cancellationToken = default)
{
    try
    {
        cancellationToken.ThrowIfCancellationRequested();
        
        // 使用支持取消操作的GetCurrentPositionAsync方法
        double currentPosition = await GetCurrentPositionAsync(cancellationToken);
        int safePosition = await GetPresetPosition(AxisName, "Safe", cancellationToken);
        
        // 允许一定的误差范围
        const int tolerance = 5;
        bool isAtSafePosition = Math.Abs(currentPosition - safePosition) <= tolerance;
        
        if (!isAtSafePosition)
        {
            LogInformation($"{CameraPosition}相机{AxisName}轴不在安全位置：当前={currentPosition}，安全={safePosition}", WaferAligner.EventIds.EventIds.Axis_Operation_Info);
        }
        
        return isAtSafePosition;
    }
    catch (OperationCanceledException)
    {
        LogWarning($"检查{CameraPosition}相机{AxisName}轴安全位置操作被取消", WaferAligner.EventIds.EventIds.Operation_Cancelled);
        throw;
    }
    catch (Exception ex)
    {
        LogError(ex, $"检查{CameraPosition}相机{AxisName}轴安全位置失败: {ex.Message}", WaferAligner.EventIds.EventIds.Axis_Position_Read_Error);
        return false;
    }
}

/// <summary>
/// 为向后兼容保留的方法
/// </summary>
[Obsolete("请使用带取消令牌的IsAtSafePositionAsync")]
public Task<bool> IsAtSafePositionAsync()
{
    return IsAtSafePositionAsync(CancellationToken.None);
}

/// <summary>
/// 执行相机轴复位操作，清除错误状态
/// </summary>
public async Task ResetPosition(CancellationToken cancellationToken = default)
{
    try
    {
        cancellationToken.ThrowIfCancellationRequested();
        LogInformation($"开始{CameraPosition}相机{AxisName}轴复位操作", WaferAligner.EventIds.EventIds.Axis_Reset_Start);
        
        // 复位前先确保轴停止
        await StopAsync(cancellationToken);
        
        using (var delayTokenSource = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken))
        {
            await Task.Delay(200, delayTokenSource.Token);
        }
        
        // 执行复位操作
        await WritePlcVariableAsync($"{AxisConstants.AXIS_GVL}.{AxisName}AxisActions", AxisAction.Reset, cancellationToken);
        
        using (var delayTokenSource = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken))
        {
            await Task.Delay(200, delayTokenSource.Token);
        }
        
        await WritePlcVariableAsync($"{AxisConstants.AXIS_GVL}.{AxisName}Execute", true, cancellationToken);
        
        // 等待复位完成信号
        int timeout = 0;
        bool isResetDone = false;
        
        while (!isResetDone && timeout < 50) // 最多等待10秒
        {
            cancellationToken.ThrowIfCancellationRequested();
            
            try
            {
                isResetDone = await ReadPlcVariableAsync<bool>($"{AxisConstants.AXIS_GVL}.{AxisName}ResetDone", cancellationToken);
            }
            catch (Exception ex)
            {
                LogWarning($"读取{CameraPosition}相机{AxisName}轴复位状态失败: {ex.Message}", 
                    WaferAligner.EventIds.EventIds.Plc_Variables_Read_Failed);
            }
            
            if (!isResetDone)
            {
                using (var delayTokenSource = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken))
                {
                    await Task.Delay(200, delayTokenSource.Token);
                }
                timeout++;
            }
        }
        
        if (isResetDone)
        {
            LogInformation($"{CameraPosition}相机{AxisName}轴复位操作完成", WaferAligner.EventIds.EventIds.Axis_Reset_Complete);
        }
        else
        {
            LogWarning($"{CameraPosition}相机{AxisName}轴复位操作超时", WaferAligner.EventIds.EventIds.Axis_Reset_Timeout);
        }
    }
    catch (OperationCanceledException)
    {
        LogWarning($"{CameraPosition}相机{AxisName}轴复位操作被取消", WaferAligner.EventIds.EventIds.Operation_Cancelled);
        throw;
    }
    catch (Exception ex)
    {
        LogError(ex, $"{CameraPosition}相机{AxisName}轴复位操作失败: {ex.Message}", WaferAligner.EventIds.EventIds.Axis_Move_Error);
        throw; // 重新抛出异常，让调用者处理
    }
}

/// <summary>
/// 为向后兼容保留的方法
/// </summary>
[Obsolete("请使用带CancellationToken参数的ResetPosition方法")]
public async Task ResetPosition()
{
    try
    {
        await ResetPosition(CancellationToken.None);
    }
    catch (OperationCanceledException)
    {
        LogWarning($"{CameraPosition}相机{AxisName}轴复位操作被取消", WaferAligner.EventIds.EventIds.Operation_Cancelled);
    }
    catch (Exception ex)
    {
        LogError(ex, $"{CameraPosition}相机{AxisName}轴复位操作失败", WaferAligner.EventIds.EventIds.Axis_Move_Error);
    }
}

/// <summary>
/// 执行复位操作，清除错误状态
/// </summary>
public override async Task<bool> ResetAsync(CancellationToken cancellationToken = default)
{
    try
    {
        await ResetPosition(cancellationToken);
        return true;
    }
    catch (OperationCanceledException)
    {
        LogWarning($"{CameraPosition}相机{AxisName}轴复位操作被取消", WaferAligner.EventIds.EventIds.Operation_Cancelled);
        throw;
    }
    catch (Exception ex)
    {
        LogError(ex, $"{CameraPosition}相机{AxisName}轴复位失败: {ex.Message}", WaferAligner.EventIds.EventIds.Axis_Move_Error);
        return false;
    }
}

/// <summary>
/// 为向后兼容保留的方法
/// </summary>
[Obsolete("请使用带CancellationToken参数的ResetAsync方法")]
public override Task<bool> ResetAsync()
{
    return ResetAsync(CancellationToken.None);
}

/// <summary>
/// 执行相机轴正向点动
/// </summary>
public override async Task<bool> JogForwardAsync(CancellationToken cancellationToken = default)
{
    try
    {
        cancellationToken.ThrowIfCancellationRequested();
        LogInformation($"{CameraPosition}相机{AxisName}轴开始正向点动", WaferAligner.EventIds.EventIds.Axis_Move_Started);
        
        // 向PLC发送正向点动命令
        await WritePlcVariableAsync($"{AxisConstants.AXIS_GVL}.{AxisName}AxisActions", AxisAction.JogF, cancellationToken);
        await WritePlcVariableAsync($"{AxisConstants.AXIS_GVL}.{AxisName}Execute", true, cancellationToken);
        
        return true;
    }
    catch (OperationCanceledException)
    {
        LogWarning($"{CameraPosition}相机{AxisName}轴正向点动操作被取消", WaferAligner.EventIds.EventIds.Operation_Cancelled);
        throw;
    }
    catch (Exception ex)
    {
        LogError(ex, $"{CameraPosition}相机{AxisName}轴正向点动失败: {ex.Message}", WaferAligner.EventIds.EventIds.Axis_Move_Error);
        return false;
    }
}

/// <summary>
/// 为向后兼容保留的方法
/// </summary>
[Obsolete("请使用带CancellationToken参数的JogForwardAsync方法")]
public new async Task<bool> JogForwardAsync()
{
    try
    {
        return await JogForwardAsync(CancellationToken.None);
    }
    catch (OperationCanceledException)
    {
        LogWarning($"{CameraPosition}相机{AxisName}轴正向点动操作被取消", WaferAligner.EventIds.EventIds.Operation_Cancelled);
        return false;
    }
}

/// <summary>
/// 执行相机轴反向点动
/// </summary>
public override async Task<bool> JogBackwardAsync(CancellationToken cancellationToken = default)
{
    try
    {
        cancellationToken.ThrowIfCancellationRequested();
        LogInformation($"{CameraPosition}相机{AxisName}轴开始反向点动", WaferAligner.EventIds.EventIds.Axis_Move_Started);
        
        // 向PLC发送反向点动命令
        await WritePlcVariableAsync($"{AxisConstants.AXIS_GVL}.{AxisName}AxisActions", AxisAction.JogB, cancellationToken);
        await WritePlcVariableAsync($"{AxisConstants.AXIS_GVL}.{AxisName}Execute", true, cancellationToken);
        
        return true;
    }
    catch (OperationCanceledException)
    {
        LogWarning($"{CameraPosition}相机{AxisName}轴反向点动操作被取消", WaferAligner.EventIds.EventIds.Operation_Cancelled);
        throw;
    }
    catch (Exception ex)
    {
        LogError(ex, $"{CameraPosition}相机{AxisName}轴反向点动失败: {ex.Message}", WaferAligner.EventIds.EventIds.Axis_Move_Error);
        return false;
    }
}

/// <summary>
/// 为向后兼容保留的方法
/// </summary>
[Obsolete("请使用带CancellationToken参数的JogBackwardAsync方法")]
public new async Task<bool> JogBackwardAsync()
{
    try
    {
        return await JogBackwardAsync(CancellationToken.None);
    }
    catch (OperationCanceledException)
    {
        LogWarning($"{CameraPosition}相机{AxisName}轴反向点动操作被取消", WaferAligner.EventIds.EventIds.Operation_Cancelled);
        return false;
    }
}

/// <summary>
/// 相机轴点动停止
/// </summary>
public override async Task<bool> JogStopAsync(CancellationToken cancellationToken = default)
{
    try
    {
        cancellationToken.ThrowIfCancellationRequested();
        LogInformation($"{CameraPosition}相机{AxisName}轴点动停止", WaferAligner.EventIds.EventIds.Axis_Operation_Info);
        
        // 向PLC发送点动停止命令
        await WritePlcVariableAsync($"{AxisConstants.AXIS_GVL}.{AxisName}AxisActions", AxisAction.Stop, cancellationToken);
        await WritePlcVariableAsync($"{AxisConstants.AXIS_GVL}.{AxisName}Execute", true, cancellationToken);
        
        return true;
    }
    catch (OperationCanceledException)
    {
        LogWarning($"{CameraPosition}相机{AxisName}轴点动停止操作被取消", WaferAligner.EventIds.EventIds.Operation_Cancelled);
        throw;
    }
    catch (Exception ex)
    {
        LogError(ex, $"{CameraPosition}相机{AxisName}轴点动停止失败: {ex.Message}", WaferAligner.EventIds.EventIds.Axis_Move_Error);
        return false;
    }
}

/// <summary>
/// 为向后兼容保留的方法
/// </summary>
[Obsolete("请使用带CancellationToken参数的JogStopAsync方法")]
public new async Task<bool> JogStopAsync()
{
    try
    {
        return await JogStopAsync(CancellationToken.None);
    }
    catch (OperationCanceledException)
    {
        LogWarning($"{CameraPosition}相机{AxisName}轴点动停止操作被取消", WaferAligner.EventIds.EventIds.Operation_Cancelled);
        return false;
    }
}

#endregion
    }
}
