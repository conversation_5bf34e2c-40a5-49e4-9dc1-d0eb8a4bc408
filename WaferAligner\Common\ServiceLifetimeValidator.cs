using System;
using WaferAligner.Services.Abstractions;
using Microsoft.Extensions.DependencyInjection;
using WaferAligner.EventIds;
using WaferAligner.Interfaces;
using WaferAligner.Services;
using WaferAligner.Services.Extensions;
using WaferAligner.Communication.Inovance.Abstractions;

namespace WaferAligner.Common
{
    /// <summary>
    /// 服务生命周期验证器
    /// 用于验证关键服务是否正确注册为单例
    /// </summary>
    public static class ServiceLifetimeValidator
    {
        /// <summary>
        /// 验证服务生命周期
        /// </summary>
        /// <param name="serviceProvider">服务提供者</param>
        public static void ValidateServiceLifetimes(IServiceProvider serviceProvider)
        {
            // 获取日志服务
            var loggingService = serviceProvider.GetService<ILoggingService>();
            if (loggingService == null)
            {
                Console.WriteLine("警告：无法获取日志服务，服务生命周期验证将无法记录日志");
            }
            
            // 检查核心服务 - 必须是单例
            ValidateSingletonService<ILoggingService>(serviceProvider, loggingService);
            ValidateSingletonService<WaferAligner.Common.ResourceManager>(serviceProvider, loggingService);
            ValidateSingletonService<IAxisViewModelFactory>(serviceProvider, loggingService);
            ValidateSingletonService<IAxisEventService>(serviceProvider, loggingService);
            ValidateSingletonService<IPlcConnectionManager>(serviceProvider, loggingService);
            ValidateSingletonService<IPlcVariableService>(serviceProvider, loggingService);
            ValidateSingletonService<IAlignerParaService>(serviceProvider, loggingService);
            ValidateSingletonService<IRecipeService>(serviceProvider, loggingService);
            // 验证PLC服务
            ValidateSingletonService<IPlcCommunication>(serviceProvider, loggingService);
            
            // 日志记录验证结果
            loggingService?.LogInformation("服务生命周期验证完成", EventIds.EventIds.Configuration_Loaded);
        }
        
        /// <summary>
        /// 验证服务是否为单例
        /// </summary>
        /// <typeparam name="T">服务类型</typeparam>
        /// <param name="serviceProvider">服务提供者</param>
        /// <param name="loggingService">日志服务</param>
        private static void ValidateSingletonService<T>(IServiceProvider serviceProvider, ILoggingService loggingService) where T : class
        {
            var serviceType = typeof(T);
            
            try
            {
                // 获取两次服务实例，检查是否为同一实例
                var instance1 = serviceProvider.GetService<T>();
                var instance2 = serviceProvider.GetService<T>();
                
                if (instance1 == null)
                {
                    loggingService?.LogWarning($"服务 {serviceType.Name} 未注册", EventIds.EventIds.Service_Unavailable);
                    return;
                }
                
                if (!ReferenceEquals(instance1, instance2))
                {
                    loggingService?.LogWarning($"服务 {serviceType.Name} 注册为非单例，但应该是单例", EventIds.EventIds.Configuration_Error);
                }
                else
                {
                    loggingService?.LogInformation($"服务 {serviceType.Name} 已正确注册为单例", EventIds.EventIds.Configuration_Loaded);
                }
            }
            catch (Exception ex)
            {
                loggingService?.LogError(ex, $"验证服务 {serviceType.Name} 时发生异常", EventIds.EventIds.Service_Initialization_Failed);
            }
        }
    }
} 