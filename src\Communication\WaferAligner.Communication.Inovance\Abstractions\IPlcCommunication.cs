﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace WaferAligner.Communication.Inovance.Abstractions
{
    /// <summary>
    /// PLC通信接口，定义与PLC交互所需的方法
    /// </summary>
    public interface IPlcCommunication
    {
        /// <summary>
        /// 是否已连接
        /// </summary>
        bool IsConnected { get; }
        
        /// <summary>
        /// 获取轴位置
        /// </summary>
        /// <param name="axisName">轴名称</param>
        /// <param name="cancellationToken">取消令牌</param>
        Task<int> GetPositionAsync(string axisName, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 移动到指定位置
        /// </summary>
        /// <param name="axisName">轴名称</param>
        /// <param name="position">目标位置</param>
        /// <param name="isRelative">是否为相对位置</param>
        /// <param name="cancellationToken">取消令牌</param>
        Task<bool> MoveToPositionAsync(string axisName, int position, bool isRelative = false, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 轴回零
        /// </summary>
        /// <param name="axisName">轴名称</param>
        /// <param name="cancellationToken">取消令牌</param>
        Task<bool> HomeAsync(string axisName, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 停止轴
        /// </summary>
        /// <param name="axisName">轴名称</param>
        /// <param name="cancellationToken">取消令牌</param>
        Task<bool> StopAsync(string axisName, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 复位轴
        /// </summary>
        /// <param name="axisName">轴名称</param>
        /// <param name="cancellationToken">取消令牌</param>
        Task<bool> ResetAsync(string axisName, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 设置点动速度
        /// </summary>
        /// <param name="axisName">轴名称</param>
        /// <param name="speed">速度值</param>
        /// <param name="cancellationToken">取消令牌</param>
        Task<bool> SetJogSpeedAsync(string axisName, int speed, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 设置运行速度
        /// </summary>
        /// <param name="axisName">轴名称</param>
        /// <param name="speed">速度值</param>
        /// <param name="cancellationToken">取消令牌</param>
        Task<bool> SetRunSpeedAsync(string axisName, int speed, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 获取轴是否已回零
        /// </summary>
        /// <param name="axisName">轴名称</param>
        /// <param name="cancellationToken">取消令牌</param>
        Task<bool> IsHomedAsync(string axisName, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 获取轴是否已使能
        /// </summary>
        /// <param name="axisName">轴名称</param>
        /// <param name="cancellationToken">取消令牌</param>
        Task<bool> IsEnabledAsync(string axisName, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 获取轴是否有错误
        /// </summary>
        /// <param name="axisName">轴名称</param>
        /// <param name="cancellationToken">取消令牌</param>
        Task<bool> HasErrorAsync(string axisName, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 向PLC写入变量值
        /// </summary>
        /// <typeparam name="T">变量类型</typeparam>
        /// <param name="axisName">轴名称</param>
        /// <param name="variableName">变量名称</param>
        /// <param name="value">要写入的值</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>写入是否成功</returns>
        Task<bool> WriteVariableAsync<T>(string axisName, string variableName, T value, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 从PLC读取变量值
        /// </summary>
        /// <typeparam name="T">变量类型</typeparam>
        /// <param name="axisName">轴名称</param>
        /// <param name="variableName">变量名称</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>读取的变量值</returns>
        Task<T> ReadVariableAsync<T>(string axisName, string variableName, CancellationToken cancellationToken = default);
    }
}
