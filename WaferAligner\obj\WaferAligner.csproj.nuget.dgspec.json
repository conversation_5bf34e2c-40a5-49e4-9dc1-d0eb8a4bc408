{"format": 1, "restore": {"C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\WaferAligner\\WaferAligner.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\Business\\WaferAligner.Core.Business\\WaferAligner.Core.Business.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\Business\\WaferAligner.Core.Business\\WaferAligner.Core.Business.csproj", "projectName": "WaferAligner.Core.Business", "projectPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\Business\\WaferAligner.Core.Business\\WaferAligner.Core.Business.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\Business\\WaferAligner.Core.Business\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": {"target": "Package", "version": "[7.0.0, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[7.0.0, )"}, "System.Reactive": {"target": "Package", "version": "[5.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\Common\\WaferAligner.Infrastructure.Extensions\\WaferAligner.Infrastructure.Extensions.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\Common\\WaferAligner.Infrastructure.Extensions\\WaferAligner.Infrastructure.Extensions.csproj", "projectName": "WaferAligner.Infrastructure.Extensions", "projectPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\Common\\WaferAligner.Infrastructure.Extensions\\WaferAligner.Infrastructure.Extensions.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\Common\\WaferAligner.Infrastructure.Extensions\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "dependencies": {"System.Reactive": {"target": "Package", "version": "[5.0.0, )"}, "System.Reflection": {"target": "Package", "version": "[4.3.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\Common\\WaferAligner.Infrastructure.Logging\\WaferAligner.Infrastructure.Logging.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\Common\\WaferAligner.Infrastructure.Logging\\WaferAligner.Infrastructure.Logging.csproj", "projectName": "WaferAligner.Infrastructure.Logging", "projectPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\Common\\WaferAligner.Infrastructure.Logging\\WaferAligner.Infrastructure.Logging.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\Common\\WaferAligner.Infrastructure.Logging\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "projectReferences": {"C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\Services\\Service.Common\\WaferAligner.Services.Abstractions\\WaferAligner.Services.Abstractions.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\Services\\Service.Common\\WaferAligner.Services.Abstractions\\WaferAligner.Services.Abstractions.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "dependencies": {"Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[8.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\Services\\Service.Common\\WaferAligner.Services.Abstractions\\WaferAligner.Services.Abstractions.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\Services\\Service.Common\\WaferAligner.Services.Abstractions\\WaferAligner.Services.Abstractions.csproj", "projectName": "WaferAligner.Services.Abstractions", "projectPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\Services\\Service.Common\\WaferAligner.Services.Abstractions\\WaferAligner.Services.Abstractions.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\Services\\Service.Common\\WaferAligner.Services.Abstractions\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "projectReferences": {"C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\Business\\WaferAligner.Core.Business\\WaferAligner.Core.Business.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\Business\\WaferAligner.Core.Business\\WaferAligner.Core.Business.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\Services\\Service.Common\\WaferAligner.Services.Core\\WaferAligner.Services.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\Services\\Service.Common\\WaferAligner.Services.Core\\WaferAligner.Services.Core.csproj", "projectName": "WaferAligner.Services.Core", "projectPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\Services\\Service.Common\\WaferAligner.Services.Core\\WaferAligner.Services.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\Services\\Service.Common\\WaferAligner.Services.Core\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "projectReferences": {"C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\Common\\WaferAligner.Infrastructure.Extensions\\WaferAligner.Infrastructure.Extensions.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\Common\\WaferAligner.Infrastructure.Extensions\\WaferAligner.Infrastructure.Extensions.csproj"}, "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\Common\\WaferAligner.Infrastructure.Logging\\WaferAligner.Infrastructure.Logging.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\Common\\WaferAligner.Infrastructure.Logging\\WaferAligner.Infrastructure.Logging.csproj"}, "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\Services\\Service.Common\\WaferAligner.Services.Abstractions\\WaferAligner.Services.Abstractions.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\Services\\Service.Common\\WaferAligner.Services.Abstractions\\WaferAligner.Services.Abstractions.csproj"}, "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\Services\\Service.Common\\WaferAligner.Services.Extensions\\WaferAligner.Services.Extensions.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\Services\\Service.Common\\WaferAligner.Services.Extensions\\WaferAligner.Services.Extensions.csproj"}, "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\src\\Core\\WaferAligner.EventIds\\WaferAligner.EventIds.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\src\\Core\\WaferAligner.EventIds\\WaferAligner.EventIds.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "dependencies": {"Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "System.Reactive": {"target": "Package", "version": "[5.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\Services\\Service.Common\\WaferAligner.Services.Extensions\\WaferAligner.Services.Extensions.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\Services\\Service.Common\\WaferAligner.Services.Extensions\\WaferAligner.Services.Extensions.csproj", "projectName": "WaferAligner.Services.Extensions", "projectPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\Services\\Service.Common\\WaferAligner.Services.Extensions\\WaferAligner.Services.Extensions.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\Services\\Service.Common\\WaferAligner.Services.Extensions\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "projectReferences": {"C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\Services\\Service.Common\\WaferAligner.Services.Abstractions\\WaferAligner.Services.Abstractions.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\Services\\Service.Common\\WaferAligner.Services.Abstractions\\WaferAligner.Services.Abstractions.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\src\\Communication\\WaferAligner.Communication.Abstractions\\WaferAligner.Communication.Abstractions.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\src\\Communication\\WaferAligner.Communication.Abstractions\\WaferAligner.Communication.Abstractions.csproj", "projectName": "WaferAligner.Communication.Abstractions", "projectPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\src\\Communication\\WaferAligner.Communication.Abstractions\\WaferAligner.Communication.Abstractions.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\src\\Communication\\WaferAligner.Communication.Abstractions\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\src\\Communication\\WaferAligner.Communication.Inovance\\WaferAligner.Communication.Inovance.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\src\\Communication\\WaferAligner.Communication.Inovance\\WaferAligner.Communication.Inovance.csproj", "projectName": "WaferAligner.Communication.Inovance", "projectPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\src\\Communication\\WaferAligner.Communication.Inovance\\WaferAligner.Communication.Inovance.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\src\\Communication\\WaferAligner.Communication.Inovance\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "projectReferences": {"C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\Services\\Service.Common\\WaferAligner.Services.Abstractions\\WaferAligner.Services.Abstractions.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\Services\\Service.Common\\WaferAligner.Services.Abstractions\\WaferAligner.Services.Abstractions.csproj"}, "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\Services\\Service.Common\\WaferAligner.Services.Extensions\\WaferAligner.Services.Extensions.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\Services\\Service.Common\\WaferAligner.Services.Extensions\\WaferAligner.Services.Extensions.csproj"}, "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\src\\Core\\WaferAligner.EventIds\\WaferAligner.EventIds.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\src\\Core\\WaferAligner.EventIds\\WaferAligner.EventIds.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "dependencies": {"System.Configuration.ConfigurationManager": {"target": "Package", "version": "[6.0.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\src\\Communication\\WaferAligner.Communication.Serial\\WaferAligner.Communication.Serial.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\src\\Communication\\WaferAligner.Communication.Serial\\WaferAligner.Communication.Serial.csproj", "projectName": "WaferAligner.Communication.Serial", "projectPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\src\\Communication\\WaferAligner.Communication.Serial\\WaferAligner.Communication.Serial.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\src\\Communication\\WaferAligner.Communication.Serial\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "projectReferences": {"C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\Business\\WaferAligner.Core.Business\\WaferAligner.Core.Business.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\Business\\WaferAligner.Core.Business\\WaferAligner.Core.Business.csproj"}, "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\Services\\Service.Common\\WaferAligner.Services.Abstractions\\WaferAligner.Services.Abstractions.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\Services\\Service.Common\\WaferAligner.Services.Abstractions\\WaferAligner.Services.Abstractions.csproj"}, "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\Services\\Service.Common\\WaferAligner.Services.Extensions\\WaferAligner.Services.Extensions.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\Services\\Service.Common\\WaferAligner.Services.Extensions\\WaferAligner.Services.Extensions.csproj"}, "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\src\\Core\\WaferAligner.EventIds\\WaferAligner.EventIds.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\src\\Core\\WaferAligner.EventIds\\WaferAligner.EventIds.csproj"}, "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\src\\Infrastructure\\WaferAligner.Infrastructure.Common\\WaferAligner.Infrastructure.Common.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\src\\Infrastructure\\WaferAligner.Infrastructure.Common\\WaferAligner.Infrastructure.Common.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "dependencies": {"Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[9.0.5, )"}, "System.IO.Ports": {"target": "Package", "version": "[9.0.7, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WindowsForms": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\src\\Core\\WaferAligner.EventIds\\WaferAligner.EventIds.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\src\\Core\\WaferAligner.EventIds\\WaferAligner.EventIds.csproj", "projectName": "WaferAligner.EventIds", "projectPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\src\\Core\\WaferAligner.EventIds\\WaferAligner.EventIds.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\src\\Core\\WaferAligner.EventIds\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[9.0.6, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\src\\Infrastructure\\WaferAligner.Infrastructure.Common\\WaferAligner.Infrastructure.Common.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\src\\Infrastructure\\WaferAligner.Infrastructure.Common\\WaferAligner.Infrastructure.Common.csproj", "projectName": "WaferAligner.Infrastructure.Common", "projectPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\src\\Infrastructure\\WaferAligner.Infrastructure.Common\\WaferAligner.Infrastructure.Common.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\src\\Infrastructure\\WaferAligner.Infrastructure.Common\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "projectReferences": {"C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\Services\\Service.Common\\WaferAligner.Services.Abstractions\\WaferAligner.Services.Abstractions.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\Services\\Service.Common\\WaferAligner.Services.Abstractions\\WaferAligner.Services.Abstractions.csproj"}, "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\Services\\Service.Common\\WaferAligner.Services.Extensions\\WaferAligner.Services.Extensions.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\Services\\Service.Common\\WaferAligner.Services.Extensions\\WaferAligner.Services.Extensions.csproj"}, "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\src\\Core\\WaferAligner.EventIds\\WaferAligner.EventIds.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\src\\Core\\WaferAligner.EventIds\\WaferAligner.EventIds.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "dependencies": {"Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[9.0.6, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WindowsForms": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\src\\Services\\WaferAligner.Services.UserManagement\\WaferAligner.Services.UserManagement.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\src\\Services\\WaferAligner.Services.UserManagement\\WaferAligner.Services.UserManagement.csproj", "projectName": "WaferAligner.Services.UserManagement", "projectPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\src\\Services\\WaferAligner.Services.UserManagement\\WaferAligner.Services.UserManagement.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\src\\Services\\WaferAligner.Services.UserManagement\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "projectReferences": {"C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\Business\\WaferAligner.Core.Business\\WaferAligner.Core.Business.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\Business\\WaferAligner.Core.Business\\WaferAligner.Core.Business.csproj"}, "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\Services\\Service.Common\\WaferAligner.Services.Abstractions\\WaferAligner.Services.Abstractions.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\Services\\Service.Common\\WaferAligner.Services.Abstractions\\WaferAligner.Services.Abstractions.csproj"}, "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\Services\\Service.Common\\WaferAligner.Services.Core\\WaferAligner.Services.Core.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\Services\\Service.Common\\WaferAligner.Services.Core\\WaferAligner.Services.Core.csproj"}, "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\src\\Core\\WaferAligner.EventIds\\WaferAligner.EventIds.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\src\\Core\\WaferAligner.EventIds\\WaferAligner.EventIds.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "dependencies": {"Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\WaferAligner\\WaferAligner.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\WaferAligner\\WaferAligner.csproj", "projectName": "<PERSON><PERSON>er<PERSON>ligne<PERSON>", "projectPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\WaferAligner\\WaferAligner.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\WaferAligner\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0-windows7.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows7.0", "projectReferences": {"C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\Services\\Service.Common\\WaferAligner.Services.Abstractions\\WaferAligner.Services.Abstractions.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\Services\\Service.Common\\WaferAligner.Services.Abstractions\\WaferAligner.Services.Abstractions.csproj"}, "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\Services\\Service.Common\\WaferAligner.Services.Core\\WaferAligner.Services.Core.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\Services\\Service.Common\\WaferAligner.Services.Core\\WaferAligner.Services.Core.csproj"}, "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\Services\\Service.Common\\WaferAligner.Services.Extensions\\WaferAligner.Services.Extensions.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\Services\\Service.Common\\WaferAligner.Services.Extensions\\WaferAligner.Services.Extensions.csproj"}, "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\src\\Communication\\WaferAligner.Communication.Abstractions\\WaferAligner.Communication.Abstractions.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\src\\Communication\\WaferAligner.Communication.Abstractions\\WaferAligner.Communication.Abstractions.csproj"}, "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\src\\Communication\\WaferAligner.Communication.Inovance\\WaferAligner.Communication.Inovance.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\src\\Communication\\WaferAligner.Communication.Inovance\\WaferAligner.Communication.Inovance.csproj"}, "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\src\\Communication\\WaferAligner.Communication.Serial\\WaferAligner.Communication.Serial.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\src\\Communication\\WaferAligner.Communication.Serial\\WaferAligner.Communication.Serial.csproj"}, "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\src\\Infrastructure\\WaferAligner.Infrastructure.Common\\WaferAligner.Infrastructure.Common.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\src\\Infrastructure\\WaferAligner.Infrastructure.Common\\WaferAligner.Infrastructure.Common.csproj"}, "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\src\\Services\\WaferAligner.Services.UserManagement\\WaferAligner.Services.UserManagement.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\src\\Services\\WaferAligner.Services.UserManagement\\WaferAligner.Services.UserManagement.csproj"}, "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\WaferAligner.EventIds\\WaferAligner.EventIds.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3- part\\WaferAligner.EventIds\\WaferAligner.EventIds.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows7.0", "dependencies": {"CommunityToolkit.Mvvm": {"target": "Package", "version": "[8.2.2, )"}, "MSTest.TestAdapter": {"target": "Package", "version": "[3.9.3, )"}, "MSTest.TestFramework": {"target": "Package", "version": "[3.9.3, )"}, "Microsoft.Extensions.Hosting": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.Extensions.Hosting.Abstractions": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[9.0.6, )"}, "Moq": {"target": "Package", "version": "[4.20.72, )"}, "SunnyUI": {"target": "Package", "version": "[*******, )"}, "SunnyUI.Common": {"target": "Package", "version": "[3.8.0, )"}, "System.IO.Ports": {"target": "Package", "version": "[9.0.7, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Host.win-x64", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WindowsForms": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}}}