using System.ComponentModel;
using System.Diagnostics;
using System.Runtime.InteropServices;
using System.Text.Json;
using System.Threading.Channels;
using WaferAligner.Services.Abstractions;
using WaferAligner.Services.Extensions;
using System;
using System.Threading.Tasks;

namespace WaferAligner.Communication.Inovance.Client
{

    public class InvoancePlcClient : IDisposable
    {
        #region //标准库
        [DllImport("StandardModbusApi.dll", EntryPoint = "Init_ETH_String", CallingConvention = CallingConvention.Cdecl)]
        public static extern bool Init_ETH_String(string sIpAddr, int nNetId = 0, int IpPort = 502);
        [DllImport("StandardModbusApi.dll", EntryPoint = "Exit_ETH", CallingConvention = CallingConvention.Cdecl)]
        public static extern bool Exit_ETH(int nNetId = 0);
        [DllImport("StandardModbusApi.dll", EntryPoint = "Am600_Write_Soft_Elem", CallingConvention = CallingConvention.Cdecl)]
        public static extern int Am600_Write_Soft_Elem(SoftElemType eType, int nStartAddr, int nCount, byte[] pValue, int nNetId = 0);
        [DllImport("StandardModbusApi.dll", EntryPoint = "Am600_Read_Soft_Elem", CallingConvention = CallingConvention.Cdecl)]
        public static extern int Am600_Read_Soft_Elem(SoftElemType eType, int nStartAddr, int nCount, byte[] pValue, int nNetId = 0);
        
        [DllImport("StandardModbusApi.dll", EntryPoint = "Am600_Write_Soft_Elem_Int16", CallingConvention = CallingConvention.Cdecl)]
        public static extern int Am600_Write_Soft_Elem_Int16(SoftElemType eType, int nStartAddr, int nCount, short[] pValue, int nNetId = 0);
        [DllImport("StandardModbusApi.dll", EntryPoint = "Am600_Write_Soft_Elem_Int32", CallingConvention = CallingConvention.Cdecl)]
        public static extern int Am600_Write_Soft_Elem_Int32(SoftElemType eType, int nStartAddr, int nCount, int[] pValue, int nNetId = 0);
        [DllImport("StandardModbusApi.dll", EntryPoint = "Am600_Write_Soft_Elem_UInt16", CallingConvention = CallingConvention.Cdecl)]
        public static extern int Am600_Write_Soft_Elem_UInt16(SoftElemType eType, int nStartAddr, int nCount, ushort[] pValue, int nNetId = 0);
        [DllImport("StandardModbusApi.dll", EntryPoint = "Am600_Write_Soft_Elem_UInt32", CallingConvention = CallingConvention.Cdecl)]
        public static extern int Am600_Write_Soft_Elem_UInt32(SoftElemType eType, int nStartAddr, int nCount, uint[] pValue, int nNetId = 0);
        [DllImport("StandardModbusApi.dll", EntryPoint = "Am600_Write_Soft_Elem_Float", CallingConvention = CallingConvention.Cdecl)]
        public static extern int Am600_Write_Soft_Elem_Float(SoftElemType eType, int nStartAddr, int nCount, float[] pValue, int nNetId = 0);
        
        [DllImport("StandardModbusApi.dll", EntryPoint = "Am600_Read_Soft_Elem_Int16", CallingConvention = CallingConvention.Cdecl)]
        public static extern int Am600_Read_Soft_Elem_Int16(SoftElemType eType, int nStartAddr, int nCount, short[] pValue, int nNetId = 0);
        [DllImport("StandardModbusApi.dll", EntryPoint = "Am600_Read_Soft_Elem_Int32", CallingConvention = CallingConvention.Cdecl)]
        public static extern int Am600_Read_Soft_Elem_Int32(SoftElemType eType, int nStartAddr, int nCount, int[] pValue, int nNetId = 0);
        [DllImport("StandardModbusApi.dll", EntryPoint = "Am600_Read_Soft_Elem_UInt16", CallingConvention = CallingConvention.Cdecl)]
        public static extern int Am600_Read_Soft_Elem_UInt16(SoftElemType eType, int nStartAddr, int nCount, ushort[] pValue, int nNetId = 0);
        [DllImport("StandardModbusApi.dll", EntryPoint = "Am600_Read_Soft_Elem_UInt32", CallingConvention = CallingConvention.Cdecl)]
        public static extern int Am600_Read_Soft_Elem_UInt32(SoftElemType eType, int nStartAddr, int nCount, uint[] pValue, int nNetId = 0);
        [DllImport("StandardModbusApi.dll", EntryPoint = "Am600_Read_Soft_Elem_Float", CallingConvention = CallingConvention.Cdecl)]
        public static extern int Am600_Read_Soft_Elem_Float(SoftElemType eType, int nStartAddr, int nCount, float[] pValue, int nNetId = 0);
        #endregion

        private readonly ILoggingService _loggingService;
        private string _ipAddress = "";
        private int _port = 502;
        private int _netId = 0;
        private bool _isConnected = false;

        public InvoancePlcClient(ILoggingService loggingService)
        {
            _loggingService = loggingService;
        }

        public bool Connect(string ipAddress, int port = 502, int netId = 0)
        {
            _ipAddress = ipAddress;
            _port = port;
            _netId = netId;

            try
            {
                bool result = Init_ETH_String(_ipAddress, _netId, _port);
                _isConnected = result;
                
                if (result)
                {
                    _loggingService.LogInformation($"PLC连接成功: {_ipAddress}:{_port}");
                }
                else
                {
                    _loggingService.LogError($"PLC连接失败: {_ipAddress}:{_port}");
                }
                
                return result;
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"PLC连接异常: {_ipAddress}:{_port}");
                return false;
            }
        }

        public bool Disconnect()
        {
            try
            {
                if (_isConnected)
                {
                    bool result = Exit_ETH(_netId);
                    _isConnected = !result;
                    
                    if (result)
                    {
                        _loggingService.LogInformation($"PLC断开连接成功: {_ipAddress}:{_port}");
                    }
                    else
                    {
                        _loggingService.LogWarning($"PLC断开连接失败: {_ipAddress}:{_port}");
                    }
                    
                    return result;
                }
                return true;
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"PLC断开连接异常: {_ipAddress}:{_port}");
                return false;
            }
        }

        public bool IsConnected => _isConnected;

        // 添加缺失的方法
        public bool KeepAliveRequest()
        {
            try
            {
                // 使用超时保护，防止阻塞UI线程
                var task = Task.Run(() => Am600_Read_Soft_Elem(SoftElemType.ELEM_MW, 0, 1, new byte[2], 0));
                return task.Wait(1000) && task.Result > 0; // 1秒超时
            }
            catch
            {
                return false;
            }
        }

        public int WriteData(string name, object value)
        {
            try
            {
                // 简化实现，实际应该根据变量类型和地址进行写入
                if (value is short shortValue)
                {
                    return Am600_Write_Soft_Elem_Int16(SoftElemType.ELEM_MW, 0, 1, new short[] { shortValue }, _netId);
                }
                else if (value is int intValue)
                {
                    return Am600_Write_Soft_Elem_Int32(SoftElemType.ELEM_MW, 0, 1, new int[] { intValue }, _netId);
                }
                else if (value is float floatValue)
                {
                    return Am600_Write_Soft_Elem_Float(SoftElemType.ELEM_MW, 0, 1, new float[] { floatValue }, _netId);
                }
                return 0;
            }
            catch
            {
                return 0;
            }
        }

        public object? ReadDataAsync(string name, Type type)
        {
            try
            {
                // 简化实现，实际应该根据变量名和类型进行读取
                if (type == typeof(short))
                {
                    var buffer = new short[1];
                    var result = Am600_Read_Soft_Elem_Int16(SoftElemType.ELEM_MW, 0, 1, buffer, _netId);
                    return result > 0 ? buffer[0] : (short)0;
                }
                else if (type == typeof(int))
                {
                    var buffer = new int[1];
                    var result = Am600_Read_Soft_Elem_Int32(SoftElemType.ELEM_MW, 0, 1, buffer, _netId);
                    return result > 0 ? buffer[0] : 0;
                }
                else if (type == typeof(float))
                {
                    var buffer = new float[1];
                    var result = Am600_Read_Soft_Elem_Float(SoftElemType.ELEM_MW, 0, 1, buffer, _netId);
                    return result > 0 ? buffer[0] : 0.0f;
                }
                return null;
            }
            catch
            {
                return null;
            }
        }

        public uint RegisterMonitorVariable(string name, Type type)
        {
            // 简化实现，返回一个假的句柄
            // 实际实现应该注册变量监控并返回真实的句柄
            return (uint)name.GetHashCode();
        }

        // 事件定义
        public EventHandler<InvoanceVariableChangedEventArgs> HCNotificationChanged = (s, e) => { };

        public void Dispose()
        {
            Disconnect();
        }
    }

    // PLCClient别名，保持兼容性
    public class PLCClient : InvoancePlcClient
    {
        public PLCClient(ILoggingService loggingService) : base(loggingService)
        {
        }

        public PLCClient(string ipAddress, int port = 502) : base(null)
        {
            Connect(ipAddress, port);
        }
    }
}
