// using AlignerUI; // 已迁移到WaferAligner.Models
using WaferAligner.Infrastructure.Common;
using WaferAligner.Services.Abstractions;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using WaferAligner.Communication.Inovance;
using WaferAligner.Communication.Inovance.Abstractions;
using Sunny.UI.Win32;
using System.ComponentModel;
using System.DirectoryServices.ActiveDirectory;
using System.Runtime.CompilerServices;
using System.Security.Cryptography;
using System.Text.RegularExpressions;
using System.Xml.Linq;
using WaferAligner;
using WaferAligner.CustomClass;
using WaferAligner.Services.Extensions;
using WaferAligner.Common;
using WaferAligner.EventIds;
using WaferAligner.Infrastructure.Common;
using WaferAligner.Services;
using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using Microsoft.Extensions.Configuration;
using System.IO;
using System.Text.Json;
using System.Collections.ObjectModel;
using WaferAligner.Communication.Inovance;
using WaferAligner.Interfaces;
using AlignerUI;

namespace Sunny.UI.Demo
{
    public partial class FTitlePage1 : BasePage, INotifyPropertyChanged
    {
        private CancellationTokenSource _cts = new CancellationTokenSource();  // 添加取消令牌源
        private volatile bool _isClosing = false;  // 添加关闭标志，使用volatile确保线程安全
        private IAxisEventService _axisEventService;
        private IUIUpdateService _uiUpdateService;
        private IPlcVariableService _plcVariableService;
        private IRecipeService _recipeService;
        private IAxisViewModelFactory _axisFactory; // 添加轴ViewModel工厂
        private IMainWindowViewModel _mainWindowViewModel; // 添加主窗口ViewModel
        private IPlcConnectionManager _plcConnectionManager; // 添加PLC连接管理器
        private readonly IStatusUpdateService _statusUpdateService;

        #region Bottom
        private string autobiography = null;
        private const int MaxLogLines = 1000; // 最大保留日志行数
        public Dictionary<Microsoft.Extensions.Logging.LogLevel, bool> LogLevels { get; set; } = new()
        {
            [Microsoft.Extensions.Logging.LogLevel.Trace] = false,
            [Microsoft.Extensions.Logging.LogLevel.Debug] = false,

            [Microsoft.Extensions.Logging.LogLevel.Information] = true,
            [Microsoft.Extensions.Logging.LogLevel.Warning] = true,
            [Microsoft.Extensions.Logging.LogLevel.Error] = true,
        };
        private readonly LogObserver<string> logObserver = new LogObserver<string>();
        #endregion Bottom
        
        // 判断是否在设计模式
        private static bool IsDesignMode
        {
            get
            {
                return (LicenseManager.UsageMode == LicenseUsageMode.Designtime);
            }
        }
        
        public FTitlePage1(IStatusUpdateService statusUpdateService /* 其他参数 */)
        {
            _statusUpdateService = statusUpdateService ?? throw new ArgumentNullException(nameof(statusUpdateService));
            InitializeComponent();
            
            // 在设计模式下不初始化服务，以便设计器可以正常加载
            if (IsDesignMode)
            {
                return;
            }
            
                          #region GetService
              _axisEventService = GetRequiredService<IAxisEventService>();
            _uiUpdateService = GetRequiredService<IUIUpdateService>();
            _plcVariableService = GetRequiredService<IPlcVariableService>();
            _recipeService = GetRequiredService<IRecipeService>();
            _axisFactory = GetRequiredService<IAxisViewModelFactory>(); // 添加轴ViewModel工厂
            _mainWindowViewModel = GetRequiredService<IMainWindowViewModel>(); // 添加主窗口ViewModel
            _plcConnectionManager = GetRequiredService<IPlcConnectionManager>(); // 添加PLC连接管理器
            #endregion GetService
        }

        protected override async Task OnInitializeAsync()
        {
            // 检查用户是否已登录且有权限访问键合对准页面
            if (!UserContext.CanAccessAlignmentPage())
            {
                Logger?.LogWarning($"未登录用户尝试访问键合对准页面",
                    EventIds.Unauthorized_Alignment_Access);

                // 显示权限不足的消息
                ShowWarning("请先登录：您需要登录才能访问键合对准页面。");

                // 禁用当前页面
                this.Enabled = false;
                return;
            }

            Logger?.LogInformation($"用户 {UserContext.CurrentUser?.Username ?? "未知"} ({UserContext.GetRoleDisplayName()}) 成功访问键合对准页面",
                EventIds.Alignment_Page_Accessed);

            // 注册资源到资源管理器
            RegisterBackgroundWorkers();
            RegisterTimers();

            BTNAutoManage(false);

            #region Bottom
            logObserver.Subscribe(Logger.LogFeed, OnLogReceived, OnLogError);
            #endregion Bottom

            // 注册Z轴位置变化事件
            await _axisEventService.RegisterAxisEventAsync("Z", $"{AxisConstants.AXIS_GVL}.ZRealDistance", (obj) =>
            {
                _uiUpdateService.SafeUpdateUI(this, () => { TxtCurZ.Text = string.Format("{0:0.000}", Convert.ToDouble(obj) / AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION); });
            });

            // 注册LZ轴位置变化事件
            await _axisEventService.RegisterAxisEventAsync("LZ", $"{AxisConstants.AXIS_GVL}.LZRealDistance", (obj) =>
            {
                _uiUpdateService.SafeUpdateUI(this, () => { TxtCurLZ.Text = string.Format("{0:0.000}", Convert.ToDouble(obj) / AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION); });
            });

            // 注册RZ轴位置变化事件
            await _axisEventService.RegisterAxisEventAsync("RZ", $"{AxisConstants.AXIS_GVL}.RZRealDistance", (obj) =>
            {
                _uiUpdateService.SafeUpdateUI(this, () => { TxtCurRZ.Text = string.Format("{0:0.000}", Convert.ToDouble(obj) / AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION); });
            });
        }

        [Obsolete("BackgroundWorker已全部迁移至Task-based异步模式，此方法将在v4.0中移除", false)]
        private void RegisterBackgroundWorkers()
        {
            // 不再使用BackgroundWorker，所有异步操作都使用Task-based异步模式
            // 旧BackgroundWorker已全部移除并替换为async/await模式
            Logger?.LogWarning("FTitlePage1调用了已过期的RegisterBackgroundWorkers方法", 
                WaferAligner.EventIds.EventIds.Deprecated_Api_Usage);
        }

        // 使用TimerWrapper替代System.Timers.Timer
        private TimerWrapper _alignMoreTimer;
        private TimerWrapper _alignOnceTimer;
        private int AlignMore_state = -1;
        private int AlignOnce_state = -1;
        
        // 不需要添加UI控件，使用IRecipeService服务管理参数

        private void RegisterTimers()
        {
            // 创建并配置TimerWrapper实例
            _alignMoreTimer = new TimerWrapper(500, AlignMore_Tick, Logger);
            _alignOnceTimer = new TimerWrapper(500, AlignOnce_Tick, Logger);
            
            // 注册到资源管理器
            RegisterResource("AlignMoreTimer", _alignMoreTimer);
            RegisterResource("AlignOnceTimer", _alignOnceTimer);
        }
        public event PropertyChangedEventHandler? PropertyChanged;
        #region Bottom
        private void OnLogReceived(LogEntry<string> log)
        {
            if (LogLevels[log.LogLevel])
            {
                string logMessage = $"{log.TimeStamp.ToString("yyyy-MM-dd T HH:mm:ss")} -- {Enum.GetName(log.LogLevel).PadRight(32, ' ')} : {log.LogMessage}" + "\r\n";
                autobiography += logMessage;

                SafeInvoke(() =>
                {
                    // 使用AppendText方法追加文本，这样新内容会显示在最后
                    RichTextBoxLog.AppendText(logMessage);

                    // 限制日志行数，避免内存占用过多
                    LimitLogLines();

                    // 将光标移动到文本末尾并滚动到底部
                    RichTextBoxLog.SelectionStart = RichTextBoxLog.Text.Length;
                    RichTextBoxLog.ScrollToCaret();

                    UpdateStatus($"{log.TimeStamp.ToString("yyyy-MM-dd T HH:mm:ss")} -- {Enum.GetName(log.LogLevel).PadRight(32, ' ')} : {log.LogMessage}");
                });
            }
        }

        /// <summary>
        /// 限制日志显示行数，当超过最大行数时删除最早的日志
        /// </summary>
        private void LimitLogLines()
        {
            try
            {
                if (RichTextBoxLog.Lines.Length > MaxLogLines)
                {
                    // 计算需要删除的行数
                    int linesToRemove = RichTextBoxLog.Lines.Length - MaxLogLines + 100; // 多删除100行，避免频繁操作

                    // 找到要删除的字符数（从开头到第linesToRemove行的结尾）
                    int charToRemove = 0;
                    string[] lines = RichTextBoxLog.Lines;
                    for (int i = 0; i < linesToRemove && i < lines.Length; i++)
                    {
                        charToRemove += lines[i].Length + Environment.NewLine.Length;
                    }

                    // 删除最早的日志行
                    if (charToRemove > 0 && charToRemove < RichTextBoxLog.Text.Length)
                    {
                        RichTextBoxLog.Select(0, charToRemove);
                        RichTextBoxLog.SelectedText = "";

                        // 同步更新autobiography字符串
                        if (charToRemove < autobiography.Length)
                        {
                            autobiography = autobiography.Substring(charToRemove);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // 如果限制行数失败，记录错误但不影响主要功能
                Logger?.LogWarning($"限制日志行数时发生错误: {ex.Message}", EventIds.Timer_Error);
            }
        }

        // SafeInvoke方法已由BasePage提供，移除重复实现
        private void OnLogError(Exception e)
        {
            string errorMessage = "日志记录系统发生错误!\r\n";
            autobiography += errorMessage;

            SafeInvoke(() =>
            {
                // 使用AppendText方法追加错误信息
                RichTextBoxLog.AppendText(errorMessage);

                // 限制日志行数，避免内存占用过多
                LimitLogLines();

                // 将光标移动到文本末尾并滚动到底部
                RichTextBoxLog.SelectionStart = RichTextBoxLog.Text.Length;
                RichTextBoxLog.ScrollToCaret();
            });
        }
        #endregion Bottom

        #region  0打开参数文件并显示  全部轴停止
        bool OpenParaFile = false;
        private async void BtnOpen_Click(object sender, EventArgs e)
        {
            if (_isClosing) return;

            try
            {
                // 禁用按钮防止重复点击
                btnOpen.Enabled = false;
                
                string path;
                OpenFileDialog openfile = new()
                {
                    Filter = "对准参数文件(*.json)|*.json",
                    InitialDirectory = Environment.CurrentDirectory + @"\ProductPara\"
                };

                if (openfile.ShowDialog() == DialogResult.OK)
                {
                    // 移除 Application.DoEvents() 调用
                    txtFileName.Text = path = openfile.FileName;
                    
                    // 使用异步方法加载参数，并传递取消令牌
                    _cts?.Cancel(); // 取消之前的操作
                    _cts?.Dispose();
                    _cts = new CancellationTokenSource();
                    RegisterCancellationTokenSource("OpenFile", _cts); // 注册到资源管理器
                    
                    bool Res = await ReadFromJSONAsync(path, _cts.Token);
                    if (Res)
                    {
                        UpdateStatus("打开对准参数文件成功");
                        Logger.LogInformation($"加载对准参数文件成功", EventIds.Load_Alignment_Config_Success);
                        OpenParaFile = true;
                        BTNAutoManage(true);
                    }
                    else
                    {
                        UpdateStatus("打开对准参数文件失败");
                        Logger.LogError($"加载对准参数文件发生错误", EventIds.Load_Alignment_Config_Failed);
                        OpenParaFile = false;
                        BTNAutoManage(false);
                    }
                }
            }
            catch (OperationCanceledException)
            {
                // 操作被取消，这是预期行为
                Logger.LogInformation("文件加载操作被用户取消", EventIds.Operation_Cancelled);
                UpdateStatus("操作已取消");
            }
            catch (Exception ex)
            {
                // 处理其他异常
                Logger.LogError(ex, "打开文件时发生错误", EventIds.Load_Alignment_Config_Failed);
                UpdateStatus($"打开文件失败: {ex.Message}");
                OpenParaFile = false;
                BTNAutoManage(false);
            }
            finally
            {
                // 恢复UI状态
                if (!_isClosing && !this.IsDisposed)
                {
                    btnOpen.Enabled = true;
                }
            }
        }

        private async Task<bool> ReadFromJSONAsync(string fileName, CancellationToken cancellationToken = default)
        {
            try
            {
                // 异步读取文件内容
                return await Task.Run(() => 
                {
                    // 定期检查取消请求
                    cancellationToken.ThrowIfCancellationRequested();
                    
                    JsonOperator oper = new(fileName);
                    TxtName.Text = oper.Select("产品名称");
                    _recipeService.ProductName = TxtName.Text;
                    _recipeService.ProductSize = oper.Select("产品尺寸");
                    _recipeService.SpacerThick = Convert.ToDouble(oper.Select("隔片厚度"));
                    TxtSpacerThick.Text = _recipeService.SpacerThick.ToString();
                    _recipeService.MarkDistance = Convert.ToDouble(oper.Select("Mark距离"));
                    _recipeService.Material = (Convert.ToString(oper.Select("产品材质")) == "硅") ? WaferSeries.Normal : WaferSeries.Special;
                    _recipeService.VisualNumber = Convert.ToInt32(oper.Select("视觉作业"));

                    // 检查取消
                    cancellationToken.ThrowIfCancellationRequested();
                    
                    _recipeService.TopWaferPhotoLX = Convert.ToDouble(oper.Select("上晶圆相机拍照位LX"));
                    _recipeService.TopWaferPhotoLY = Convert.ToDouble(oper.Select("上晶圆相机拍照位LY"));
                    _recipeService.TopWaferPhotoLZ = Convert.ToDouble(oper.Select("上晶圆相机拍照位LZ"));
                    _recipeService.TopWaferPhotoRX = Convert.ToDouble(oper.Select("上晶圆相机拍照位RX"));
                    _recipeService.TopWaferPhotoRY = Convert.ToDouble(oper.Select("上晶圆相机拍照位RY"));
                    _recipeService.TopWaferPhotoRZ = Convert.ToDouble(oper.Select("上晶圆相机拍照位RZ"));
                    _recipeService.TopWaferPhotoZ = Convert.ToDouble(oper.Select("上晶圆平台拍照位Z"));
                    _recipeService.TopWaferPhotoX = Convert.ToDouble(oper.Select("上晶圆平台拍照位X"));
                    _recipeService.TopWaferPhotoY = Convert.ToDouble(oper.Select("上晶圆平台拍照位Y"));
                    _recipeService.TopWaferPhotoR = Convert.ToDouble(oper.Select("上晶圆平台拍照位R"));
                    _recipeService.IsTopHorizontalAdjust = Convert.ToInt32(oper.Select("上晶圆调平")) == 1 ? true : false;
                    _recipeService.IsTopHorizontalPhoto = Convert.ToInt32(oper.Select("调平后去拍照位")) == 1 ? true : false;
                    _recipeService.TopWaferThick = Convert.ToDouble(oper.Select("上晶圆厚度"));
                    TxtTopThick.Text = _recipeService.TopWaferThick.ToString();

                    // 检查取消
                    cancellationToken.ThrowIfCancellationRequested();
                    
                    _recipeService.BottomWaferPhotoLX = Convert.ToDouble(oper.Select("下晶圆相机拍照位LX"));
                    _recipeService.BottomWaferPhotoLY = Convert.ToDouble(oper.Select("下晶圆相机拍照位LY"));
                    _recipeService.BottomWaferPhotoLZ = Convert.ToDouble(oper.Select("下晶圆相机拍照位LZ"));
                    _recipeService.BottomWaferPhotoRX = Convert.ToDouble(oper.Select("下晶圆相机拍照位RX"));
                    _recipeService.BottomWaferPhotoRY = Convert.ToDouble(oper.Select("下晶圆相机拍照位RY"));
                    _recipeService.BottomWaferPhotoRZ = Convert.ToDouble(oper.Select("下晶圆相机拍照位RZ"));
                    _recipeService.BottomWaferPhotoZ = Convert.ToDouble(oper.Select("下晶圆平台拍照位Z"));
                    _recipeService.BottomWaferPhotoX = Convert.ToDouble(oper.Select("下晶圆平台拍照位X"));
                    _recipeService.BottomWaferPhotoY = Convert.ToDouble(oper.Select("下晶圆平台拍照位Y"));
                    _recipeService.BottomWaferPhotoR = Convert.ToDouble(oper.Select("下晶圆平台拍照位R"));
                    _recipeService.BottomWaferThick = Convert.ToDouble(oper.Select("下晶圆厚度"));
                    TxtBottomThick.Text = _recipeService.BottomWaferThick.ToString();
                    
                    // 检查取消
                    cancellationToken.ThrowIfCancellationRequested();
                    
                    // 读取后更新PLC参数，不传递取消令牌参数，因为接口不支持
                    _recipeService.UpdatePlcParametersAsync().GetAwaiter().GetResult();
                    
                    return true;
                }, cancellationToken);
            }
            catch (OperationCanceledException)
            {
                // 重新抛出取消异常，让调用方处理
                throw;
            }
            catch (Exception ex)
            {
                await SafeInvokeAsync(() => 
                {
                    MessageBox.Show(ex.Message.ToString(), "提示");
                });
                Logger?.LogError($"加载对准参数文件失败: {ex.Message}", EventIds.Load_Alignment_Config_Failed);
                return false;
            }
        }

        // 旧的同步方法已移除，改为直接使用异步版本

        private async void BtnAll停止_Click(object sender, EventArgs e)
        {
            ALLBTNManage(false, -1);
            await _plcVariableService.WriteVariableSafelyAsync($"{AxisConstants.AXIS_GVL}.hb_U_轴停止", true);
            await _plcVariableService.WriteVariableSafelyAsync($"{AxisConstants.AXIS_GVL}.hb_V_轴停止", true);
            await _plcVariableService.WriteVariableSafelyAsync($"{AxisConstants.AXIS_GVL}.hb_W_轴停止", true);
            await _plcVariableService.WriteVariableSafelyAsync($"{AxisConstants.AXIS_GVL}.hb_Z_轴停止", true);
            await _plcVariableService.WriteVariableSafelyAsync($"{AxisConstants.AXIS_GVL}.hb_R_轴停止", true);
            await _plcVariableService.WriteVariableSafelyAsync($"{AxisConstants.AXIS_GVL}.hb_LX_轴停止", true);
            await _plcVariableService.WriteVariableSafelyAsync($"{AxisConstants.AXIS_GVL}.hb_LZ_轴停止", true);
            await _plcVariableService.WriteVariableSafelyAsync($"{AxisConstants.AXIS_GVL}.hb_RX_轴停止", true);
            await _plcVariableService.WriteVariableSafelyAsync($"{AxisConstants.AXIS_GVL}.hb_RZ_轴停止", true);
            ALLBTNManage(true, -1);

        }
        #endregion  0打开参数文件并显示 全部轴停止

        #region  1固定卡盘       
        private async void BtnChuckLock_Click(object sender, EventArgs e)
        {
            if (_isClosing) return;

            try
            {
                // 禁用所有按钮，防止重复操作
                ALLBTNManage(false, -1);
                UpdateStatus("卡盘锁关中...");
                
                // 重置和注册取消令牌
                _cts?.Cancel();
                _cts?.Dispose();
                _cts = new CancellationTokenSource();
                RegisterCancellationTokenSource("ChuckLock", _cts);
                
                // 使用直接注入的MainWindowViewModel
                if (_mainWindowViewModel.ChuckLockState == (int)CylinderStatus.Close)
                {
                    _mainWindowViewModel.ChuckLockState = (int)CylinderStatus.Open;
                    await _mainWindowViewModel.ChuckLockExecute();
                }

                // 使用直接注入的PLC连接管理器
                var plc = _plcConnectionManager.GetPlcInstance("Main");
                
                PLCVarReadInfo ReadInPutChuckLeft = new() { Name = $"{AxisConstants.AXIS_GVL}.UpperChuckLeftLockSensor", Type = typeof(bool) };
                object retChuckLeft;
                retChuckLeft = await plc.ReadVariableAsync(ReadInPutChuckLeft, _cts.Token);

                PLCVarReadInfo ReadInPutChuckRight = new() { Name = $"{AxisConstants.AXIS_GVL}.UpperChuckRightLockSensor", Type = typeof(bool) };
                object retInPutChuckRight;
                retInPutChuckRight = await plc.ReadVariableAsync(ReadInPutChuckRight, _cts.Token);

                if (_mainWindowViewModel.ChuckLockState == (int)CylinderStatus.Open)
                {
                    bool success = (bool)retChuckLeft && (bool)retInPutChuckRight;
                    UpdateStatus(success ? "卡盘锁关成功!" : "卡盘锁关失败");
                    
                    if (!success)
                    {
                        Logger.LogError($"卡盘锁关失败", EventIds.Chuck_Lock_Closed_Failed);
                        ShowError("卡盘锁关失败");
                    }
                    else
                    {
                        Logger.LogInformation("卡盘锁关成功", EventIds.Cylinder_Operation_Completed);
                    }
                }
            }
            catch (OperationCanceledException)
            {
                // 操作被取消，这是预期行为
                Logger.LogInformation("卡盘锁关操作被取消", EventIds.Operation_Cancelled);
                UpdateStatus("卡盘锁操作已取消");
            }
            catch (Exception ex)
            {
                // 处理其他异常
                Logger.LogError(ex, "卡盘锁关操作失败", EventIds.Cylinder_Operation_Failed);
                UpdateStatus($"卡盘锁关操作失败: {ex.Message}");
            }
            finally
            {
                // 恢复UI状态
                if (!_isClosing && !this.IsDisposed)
                {
                    ALLBTNManage(true, -1);
                }
            }
        }

        #endregion  1固定卡盘 

        #region  2固定上晶圆
        private async void BtnTrayWaferOuter_Click(object sender, EventArgs e)
        {
            ALLBTNManage(false, -1);
            UpdateStatus("顶晶圆托盘弹出中...");
            
            // 使用直接注入的MainWindowViewModel
            if (_mainWindowViewModel.TrayWaferOuterState == (int)CylinderStatus.Close)
            {
                _mainWindowViewModel.TrayWaferOuterState = (int)CylinderStatus.Open;
                await _mainWindowViewModel.TrayWaferOuterExecute();
            }

            // 使用直接注入的PLC连接管理器
            var plc = _plcConnectionManager.GetPlcInstance("Main");
            
            PLCVarReadInfo ReadInPutWaferTray = new() { Name = $"{AxisConstants.AXIS_GVL}.UpperWaferOutBoardSensor", Type = typeof(bool) };
            object retWaferTray;
            retWaferTray = await plc.ReadVariableAsync(ReadInPutWaferTray, CancellationToken.None);

            if (_mainWindowViewModel.TrayWaferOuterState == (int)CylinderStatus.Open)
            {
                UpdateStatus((bool)retWaferTray ? "顶晶圆托盘弹出成功!" : "顶晶圆托盘弹出失败");
                if (!(bool)retWaferTray)
                    Logger.LogError($"顶晶圆托盘弹出失败", EventIds.Top_Wafer_Tray_Out_Error);
            }
            ALLBTNManage(true, -1);
        }
        private async void BtnTopWaferFixed_Click(object sender, EventArgs e)
        {
            if (_isClosing) return;

            try
            {
                // 禁用所有按钮，防止重复操作
                ALLBTNManage(false, -1);
                UpdateStatus("顶部晶圆固定中...");
                
                // 重置和注册取消令牌
                _cts?.Cancel();
                _cts?.Dispose();
                _cts = new CancellationTokenSource();
                RegisterCancellationTokenSource("TopWaferFixed", _cts);
                
                // 记录开始操作日志
                Logger.LogInformation("开始顶部晶圆固定操作", EventIds.Top_Wafer_Photo_Started);
                
                // 使用直接注入的MainWindowViewModel
                if (_mainWindowViewModel.TopWaferState == (int)CylinderStatus.Close)
                {
                    _mainWindowViewModel.TopWaferState = (int)CylinderStatus.Open;
                    await _mainWindowViewModel.TopWaferExecute();
                }

                // 使用直接注入的PLC连接管理器
                var plc = _plcConnectionManager.GetPlcInstance("Main");
                
                PLCVarReadInfo ReadInPutWaferVacc = new() { Name = $"{AxisConstants.AXIS_GVL}.UpperWaferVaccumSensor", Type = typeof(bool) };
                object retWaferVacc;
                retWaferVacc = await plc.ReadVariableAsync(ReadInPutWaferVacc, _cts.Token);

                if (_mainWindowViewModel.TopWaferState == (int)CylinderStatus.Open)
                {
                    if ((bool)retWaferVacc)
                    {
                        UpdateStatus("顶部晶圆固定成功!");
                        Logger.LogInformation("顶部晶圆固定操作成功完成", EventIds.Operation_Complete);
                    }
                    else
                    {
                        UpdateStatus("顶部晶圆固定失败");
                        Logger.LogError("顶部晶圆固定失败", EventIds.Top_Wafer_Fix_Error);
                        ShowError("顶部晶圆固定失败");
                    }
                }
            }
            catch (OperationCanceledException)
            {
                // 操作被取消，这是预期行为
                Logger.LogInformation("顶部晶圆固定操作被取消", EventIds.Operation_Cancelled);
                UpdateStatus("顶部晶圆固定操作已取消");
            }
            catch (Exception ex)
            {
                // 处理其他异常
                Logger.LogError(ex, "顶部晶圆固定操作失败", EventIds.Top_Wafer_Fix_Error);
                UpdateStatus($"顶部晶圆固定操作失败: {ex.Message}");
            }
            finally
            {
                // 恢复UI状态
                if (!_isClosing && !this.IsDisposed)
                {
                    ALLBTNManage(true, -1);
                }
            }
        }
        private async void BtnTopWaferTakeUp_Click(object sender, EventArgs e)
        {
            if (_isClosing) return;

            try
            {
                // 禁用所有按钮，防止重复操作
                ALLBTNManage(false, -1);
                UpdateStatus("上部晶圆取起中...");
                
                // 重置和注册取消令牌
                _cts?.Cancel();
                _cts?.Dispose();
                _cts = new CancellationTokenSource();
                RegisterCancellationTokenSource("TopWaferTakeUp", _cts);
                
                // 记录开始操作日志
                Logger.LogInformation("开始上部晶圆取起操作", EventIds.Top_Wafer_Take_Up_Started);
                
                // 使用直接注入的MainWindowViewModel
                bool res = await _mainWindowViewModel.TopWaferTakeUp();
                if (res)
                {
                    UpdateStatus("上部晶圆取起完成！");
                    Logger.LogInformation("上部晶圆取起成功完成", EventIds.Operation_Complete);
                }
                else
                {
                    UpdateStatus("上部晶圆取起失败");
                    Logger.LogError("上部晶圆取起失败", EventIds.Top_Wafer_Pickup_Error);
                    ShowError("上部晶圆取起失败");
                }
            }
            catch (OperationCanceledException)
            {
                // 操作被取消，这是预期行为
                Logger.LogInformation("上部晶圆取起操作被取消", EventIds.Operation_Cancelled);
                UpdateStatus("上部晶圆取起操作已取消");
            }
            catch (Exception ex)
            {
                // 处理其他异常
                Logger.LogError(ex, "上部晶圆取起操作失败", EventIds.Top_Wafer_Pickup_Error);
                UpdateStatus($"上部晶圆取起操作失败: {ex.Message}");
            }
            finally
            {
                // 恢复UI状态
                if (!_isClosing && !this.IsDisposed)
                {
                    ALLBTNManage(true, -1);
                }
            }
        }
        private async void BtnTopWaferPhotoPos_Click(object sender, EventArgs e)
        {
            if (_isClosing) return;

            try
            {
                // 禁用所有按钮，防止重复操作
                ALLBTNManage(false, -1);
                UpdateStatus("上部晶圆拍照中...");
                
                // 重置和注册取消令牌
                _cts?.Cancel();
                _cts?.Dispose();
                _cts = new CancellationTokenSource();
                RegisterCancellationTokenSource("TopWaferPhotoPos", _cts);
                
                // 记录开始操作日志
                Logger.LogInformation("开始上部晶圆拍照操作", EventIds.Top_Wafer_Photo_Started);
                
                // 使用直接注入的MainWindowViewModel
                bool res = await _mainWindowViewModel.TopWaferUp(true, true);
                if (res)
                {
                    UpdateStatus("上部晶圆拍照运动完成！");
                    Logger.LogInformation("上部晶圆拍照运动成功完成", EventIds.Operation_Complete);
                }
                else
                {
                    UpdateStatus("上部晶圆拍照运动失败");
                    Logger.LogError("上部晶圆拍照运动失败", EventIds.Top_Wafer_Photo_Move_Error);
                    ShowError("上部晶圆拍照运动失败");
                }
            }
            catch (OperationCanceledException)
            {
                // 操作被取消，这是预期行为
                Logger.LogInformation("上部晶圆拍照操作被取消", EventIds.Operation_Cancelled);
                UpdateStatus("上部晶圆拍照操作已取消");
            }
            catch (Exception ex)
            {
                // 处理其他异常
                Logger.LogError(ex, "上部晶圆拍照操作失败", EventIds.Top_Wafer_Photo_Move_Error);
                UpdateStatus($"上部晶圆拍照操作失败: {ex.Message}");
            }
            finally
            {
                // 恢复UI状态
                if (!_isClosing && !this.IsDisposed)
                {
                    ALLBTNManage(true, -1);
                }
            }
        }
        private async void BtnTopWaferPhoto_Click(object sender, EventArgs e)
        {
            if (_isClosing) return;

            try
            {
                // 禁用所有按钮，防止重复操作
                ALLBTNManage(false, -1);
                UpdateStatus("拍照中...");
                
                // 重置和注册取消令牌
                _cts?.Cancel();
                _cts?.Dispose();
                _cts = new CancellationTokenSource();
                RegisterCancellationTokenSource("TopWaferPhoto", _cts);
                
                // 记录开始操作日志
                Logger.LogInformation("开始拍照操作", EventIds.Target_Take_Photo_Started);
                
                // 使用直接注入的MainWindowViewModel
                await _mainWindowViewModel.TargetTakePhoto();
                
                UpdateStatus("拍照完成！");
                Logger.LogInformation("拍照操作成功完成", EventIds.Operation_Complete);
            }
            catch (OperationCanceledException)
            {
                // 操作被取消，这是预期行为
                Logger.LogInformation("拍照操作被取消", EventIds.Operation_Cancelled);
                UpdateStatus("拍照操作已取消");
            }
            catch (Exception ex)
            {
                // 处理其他异常
                Logger.LogError(ex, "拍照操作失败", EventIds.Target_Take_Photo_Failed);
                UpdateStatus($"拍照操作失败: {ex.Message}");
            }
            finally
            {
                // 恢复UI状态
                if (!_isClosing && !this.IsDisposed)
                {
                    ALLBTNManage(true, -1);
                }
            }
        }
        #endregion 2固定上晶圆

        #region  3上晶圆识别

        private async void BtnTopWaferPhotoPositioning_Click(object sender, EventArgs e)
        {
            try
            {
                // 禁用按钮防止重复操作
                BTNManualManage(false, 0);
                
                // 创建取消令牌，支持超时取消
                using var localCts = CancellationTokenSource.CreateLinkedTokenSource(_cts.Token);
                localCts.CancelAfter(TimeSpan.FromSeconds(30)); // 30秒超时
                
                Logger?.LogInformation("开始上晶圆拍照定位", EventIds.Top_Wafer_Photo_Started);
                UpdateStatus("上晶圆拍照定位...");
                
                // 异步执行操作，而不是使用BackgroundWorker
                await Task.Run(async () => 
                {
                    try
                    {
                        // 控制Z轴到拍照位置
                        double zPos = _recipeService.TopWaferPhotoZ;
                        
                        // 检查取消状态
                        localCts.Token.ThrowIfCancellationRequested();
                        
                        await _axisEventService.MoveToPositionAsync("Z", zPos);
                        
                        // 控制相机轴到拍照位置
                        await _axisEventService.MoveCameraAxesToPositionAsync(
                            _recipeService.TopWaferPhotoLX,
                            _recipeService.TopWaferPhotoLY,
                            _recipeService.TopWaferPhotoLZ,
                            _recipeService.TopWaferPhotoRX,
                            _recipeService.TopWaferPhotoRY,
                            _recipeService.TopWaferPhotoRZ);
                            
                        // 等待轴运动完成
                        while (!_axisEventService.CheckCameraAxesArrived())
                        {
                            // 定期检查取消请求
                            localCts.Token.ThrowIfCancellationRequested();
                            await Task.Delay(100, localCts.Token);
                        }
                        
                        // 完成后更新状态
                        await SafeInvokeAsync(() => 
                        {
                            UpdateStatus("上晶圆拍照定位完成");
                            BTNManualManage(true, 1);
                        });
                        
                        Logger?.LogInformation("上晶圆拍照定位完成", EventIds.Top_Wafer_Photo_Completed);
                    }
                    catch (OperationCanceledException)
                    {
                        // 处理取消操作
                        Logger?.LogWarning("上晶圆拍照定位被取消", EventIds.Top_Wafer_Photo_Cancelled);
                        await SafeInvokeAsync(() => 
                        {
                            UpdateStatus("上晶圆拍照定位被取消");
                            BTNManualManage(true, 1);
                        });
                    }
                    catch (Exception ex)
                    {
                        // 处理异常
                        Logger?.LogError(ex, "上晶圆拍照定位出错", EventIds.Top_Wafer_Photo_Error);
                        await SafeInvokeAsync(() => 
                        {
                            UpdateStatus($"上晶圆拍照定位出错: {ex.Message}");
                            BTNManualManage(true, 1);
                        });
                    }
                }, localCts.Token);
            }
            catch (Exception ex)
            {
                // 处理UI线程异常
                Logger?.LogError(ex, "启动上晶圆拍照定位任务出错", EventIds.Top_Wafer_Photo_Error);
                UpdateStatus($"启动上晶圆拍照定位任务出错: {ex.Message}");
                BTNManualManage(true, 1);
            }
        }
        
        // BackgroundWorker已完全移除，使用Task-based异步模式替代
        private async void BtnTopWaferMarkRecognition_Click(object sender, EventArgs e)
        {
            if (_isClosing) return;

            try
            {
                // 禁用所有按钮，防止重复操作
                ALLBTNManage(false, -1);
                UpdateStatus("上晶圆Mark识别中...");
                
                // 重置和注册取消令牌
                _cts?.Cancel();
                _cts?.Dispose();
                _cts = new CancellationTokenSource();
                RegisterCancellationTokenSource("TopWaferMarkRecognition", _cts);
                
                // 记录开始操作日志
                Logger.LogInformation("开始上晶圆Mark识别操作", EventIds.Top_Wafer_Mark_Recognition_Started);
                
                // 使用直接注入的MainWindowViewModel
                await _mainWindowViewModel.TargetTakePhoto();
                await Task.Delay(100, _cts.Token);
                string State = await _mainWindowViewModel.ReceiveMsg();
                string[] States = State.Split(',');
                
                if (Convert.ToInt32(States[0]) == 1 && Convert.ToInt32(States[1]) == 5 && Convert.ToInt32(States[2]) == 1)
                {
                    UpdateStatus("上晶圆Mark识别成功!");
                    Logger.LogInformation("上晶圆Mark识别成功完成", EventIds.Operation_Complete);
                }
                else if (Convert.ToInt32(States[0]) == 1 && Convert.ToInt32(States[1]) == 5 && Convert.ToInt32(States[2]) == 2)
                {
                    UpdateStatus("上晶圆Mark识别失败!");
                    Logger.LogError("上晶圆Mark识别失败", EventIds.Top_Wafer_Mark_Identified_Failed_Specific);
                    ShowError("上晶圆Mark识别失败");
                }
            }
            catch (OperationCanceledException)
            {
                // 操作被取消，这是预期行为
                Logger.LogInformation("上晶圆Mark识别操作被取消", EventIds.Operation_Cancelled);
                UpdateStatus("上晶圆Mark识别操作已取消");
            }
            catch (Exception ex)
            {
                // 处理其他异常
                Logger.LogError(ex, "上晶圆Mark识别操作失败", EventIds.Top_Wafer_Mark_Identified_Failed_Specific);
                UpdateStatus($"上晶圆Mark识别操作失败: {ex.Message}");
            }
            finally
            {
                // 恢复UI状态
                if (!_isClosing && !this.IsDisposed)
                {
                    ALLBTNManage(true, -1);
                }
            }
        }


        #endregion  3上晶圆识别

        #region  4固定下晶圆 
        private async void BtnBottomWaferDown_Click(object sender, EventArgs e)
        {
            if (_isClosing) return;

            try
            {
                // 禁用所有按钮，防止重复操作
                ALLBTNManage(false, -1);
                UpdateStatus("底部晶圆下降中...");
                
                // 重置和注册取消令牌
                _cts?.Cancel();
                _cts?.Dispose();
                _cts = new CancellationTokenSource();
                RegisterCancellationTokenSource("BottomWaferDown", _cts);
                
                // 记录开始操作日志
                Logger.LogInformation("开始底部晶圆下降操作", EventIds.Bottom_Wafer_Down_Started);
                
                // 使用直接注入的MainWindowViewModel
                bool res = await _mainWindowViewModel.BottomWaferDown(1);
                if (res)
                {
                    UpdateStatus("底部晶圆下降完成！");
                    Logger.LogInformation("底部晶圆下降成功完成", EventIds.Operation_Complete);
                }
                else
                {
                    UpdateStatus("底部晶圆下降失败");
                    Logger.LogError("底部晶圆下降失败", EventIds.Bottom_Wafer_Down_Error);
                    ShowError("底部晶圆下降失败");
                }
            }
            catch (OperationCanceledException)
            {
                // 操作被取消，这是预期行为
                Logger.LogInformation("底部晶圆下降操作被取消", EventIds.Operation_Cancelled);
                UpdateStatus("底部晶圆下降操作已取消");
            }
            catch (Exception ex)
            {
                // 处理其他异常
                Logger.LogError(ex, "底部晶圆下降操作失败", EventIds.Bottom_Wafer_Down_Error);
                UpdateStatus($"底部晶圆下降操作失败: {ex.Message}");
            }
            finally
            {
                // 恢复UI状态
                if (!_isClosing && !this.IsDisposed)
                {
                    ALLBTNManage(true, -1);
                }
            }
        }

        private async void BtnBottomWaferTakeDown_Click(object sender, EventArgs e)
        {
            if (_isClosing) return;

            try
            {
                // 禁用所有按钮，防止重复操作
                ALLBTNManage(false, -1);
                UpdateStatus("底部晶圆放下中...");
                
                // 重置和注册取消令牌
                _cts?.Cancel();
                _cts?.Dispose();
                _cts = new CancellationTokenSource();
                RegisterCancellationTokenSource("BottomWaferTakeDown", _cts);
                
                // 记录开始操作日志
                Logger.LogInformation("开始底部晶圆放下操作", EventIds.Bottom_Wafer_Take_Down_Started);
                
                // 使用直接注入的MainWindowViewModel
                bool res = await _mainWindowViewModel.BottomWaferTakeDown(1);
                if (res)
                {
                    UpdateStatus("底部晶圆放下完成！");
                    Logger.LogInformation("底部晶圆放下成功完成", EventIds.Operation_Complete);
                }
                else
                {
                    UpdateStatus("底部晶圆放下失败");
                    Logger.LogError("底部晶圆放下失败", EventIds.Bottom_Wafer_Place_Error);
                    ShowError("底部晶圆放下失败");
                }
            }
            catch (OperationCanceledException)
            {
                // 操作被取消，这是预期行为
                Logger.LogInformation("底部晶圆放下操作被取消", EventIds.Operation_Cancelled);
                UpdateStatus("底部晶圆放下操作已取消");
            }
            catch (Exception ex)
            {
                // 处理其他异常
                Logger.LogError(ex, "底部晶圆放下操作失败", EventIds.Bottom_Wafer_Place_Error);
                UpdateStatus($"底部晶圆放下操作失败: {ex.Message}");
            }
            finally
            {
                // 恢复UI状态
                if (!_isClosing && !this.IsDisposed)
                {
                    ALLBTNManage(true, -1);
                }
            }
        }

        private async void BtnBottomWaferPhotoPos_Click(object sender, EventArgs e)
        {
            if (_isClosing) return;

            try
            {
                // 禁用所有按钮，防止重复操作
                ALLBTNManage(false, -1);
                UpdateStatus("下部晶圆拍照中...");
                
                // 重置和注册取消令牌
                _cts?.Cancel();
                _cts?.Dispose();
                _cts = new CancellationTokenSource();
                RegisterCancellationTokenSource("BottomWaferPhotoPos", _cts);
                
                // 记录开始操作日志
                Logger.LogInformation("开始下部晶圆拍照操作", EventIds.Bottom_Wafer_Photo_Started);
                
                // 使用直接注入的MainWindowViewModel
                bool res = await _mainWindowViewModel.BottomWaferDown(2);
                if (res)
                {
                    UpdateStatus("下部晶圆拍照运动完成！");
                    Logger.LogInformation("下部晶圆拍照运动成功完成", EventIds.Operation_Complete);
                }
                else
                {
                    UpdateStatus("下部晶圆拍照运动失败");
                    Logger.LogError("下部晶圆拍照运动失败", EventIds.Bottom_Wafer_Photo_Move_Error);
                    ShowError("下部晶圆拍照运动失败");
                }
            }
            catch (OperationCanceledException)
            {
                // 操作被取消，这是预期行为
                Logger.LogInformation("下部晶圆拍照操作被取消", EventIds.Operation_Cancelled);
                UpdateStatus("下部晶圆拍照操作已取消");
            }
            catch (Exception ex)
            {
                // 处理其他异常
                Logger.LogError(ex, "下部晶圆拍照操作失败", EventIds.Bottom_Wafer_Photo_Move_Error);
                UpdateStatus($"下部晶圆拍照操作失败: {ex.Message}");
            }
            finally
            {
                // 恢复UI状态
                if (!_isClosing && !this.IsDisposed)
                {
                    ALLBTNManage(true, -1);
                }
            }
        }

        #endregion   4固定下晶圆 

        #region 5键合对准
        private async void BtnBottomWaferPhotoPositioning_Click(object sender, EventArgs e)
        {
            if (_isClosing) return;
            
            try
            {
                // 禁用按钮防止重复操作
                BTNManualManage(false, 0);
                
                // 重置主取消令牌
                _cts?.Cancel();
                _cts?.Dispose();
                _cts = new CancellationTokenSource();
                RegisterCancellationTokenSource("BottomWaferPhotoPositioning", _cts);
                
                // 创建取消令牌，支持超时取消
                using var localCts = CancellationTokenSource.CreateLinkedTokenSource(_cts.Token);
                localCts.CancelAfter(TimeSpan.FromSeconds(30)); // 30秒超时
                
                Logger.LogInformation("开始下晶圆拍照定位", EventIds.Bottom_Wafer_Photo_Started);
                UpdateStatus("下晶圆拍照定位...");
                
                // 异步执行操作，而不是使用BackgroundWorker
                await Task.Run(async () => 
                {
                    try
                    {
                        // 控制Z轴到拍照位置
                        double zPos = _recipeService.BottomWaferPhotoZ;
                        
                        // 检查取消状态
                        localCts.Token.ThrowIfCancellationRequested();
                        
                        await _axisEventService.MoveToPositionAsync("Z", zPos);
                        
                        // 控制相机轴到拍照位置
                        await _axisEventService.MoveCameraAxesToPositionAsync(
                            _recipeService.BottomWaferPhotoLX,
                            _recipeService.BottomWaferPhotoLY,
                            _recipeService.BottomWaferPhotoLZ,
                            _recipeService.BottomWaferPhotoRX,
                            _recipeService.BottomWaferPhotoRY,
                            _recipeService.BottomWaferPhotoRZ);
                            
                        // 等待轴运动完成
                        while (!_axisEventService.CheckCameraAxesArrived())
                        {
                            // 定期检查取消请求
                            localCts.Token.ThrowIfCancellationRequested();
                            await Task.Delay(100, localCts.Token);
                        }
                        
                        // 完成后更新状态
                        await SafeInvokeAsync(() => 
                        {
                            UpdateStatus("下晶圆拍照定位完成");
                            BTNManualManage(true, 1);
                        });
                        
                        Logger.LogInformation("下晶圆拍照定位完成", EventIds.Operation_Complete);
                    }
                    catch (OperationCanceledException)
                    {
                        // 处理取消操作
                        Logger.LogWarning("下晶圆拍照定位被取消", EventIds.Operation_Cancelled);
                        await SafeInvokeAsync(() => 
                        {
                            UpdateStatus("下晶圆拍照定位被取消");
                            BTNManualManage(true, 1);
                        });
                    }
                    catch (Exception ex)
                    {
                        // 处理异常
                        Logger.LogError(ex, "下晶圆拍照定位出错", EventIds.Bottom_Wafer_Photo_Error);
                        await SafeInvokeAsync(() => 
                        {
                            UpdateStatus($"下晶圆拍照定位出错: {ex.Message}");
                            BTNManualManage(true, 1);
                        });
                    }
                }, localCts.Token);
            }
            catch (Exception ex)
            {
                // 处理UI线程异常
                Logger.LogError(ex, "启动下晶圆拍照定位任务出错", EventIds.Bottom_Wafer_Photo_Error);
                UpdateStatus($"启动下晶圆拍照定位任务出错: {ex.Message}");
                BTNManualManage(true, 1);
            }
        }
        
        // BackgroundWorker已完全移除，使用Task-based异步模式替代

        // 注意：_AlignMoreTimer和AlignMore_state已经在类级别定义
        private async void BtnAignMore_Click(object sender, EventArgs e)
        {
            if (_isClosing) return;

            try
            {
                if (AlignMore_state < 0)
                {
                    BTNAutoManage(true);
                    AlignMore_state = 0;
                    Logger.LogInformation("开始连续对准操作", EventIds.Alignment_Started);
                    UpdateStatus("开始连续对准...");
                    _alignMoreTimer.Interval = 500;
                    _alignMoreTimer.Start();
                }
                else
                {
                    if (_alignMoreTimer.Enabled)
                    {
                        _alignMoreTimer.Stop();
                        BTNAutoManage(false);
                        AlignMore_state = -1;
                        Logger.LogInformation("停止连续对准操作", EventIds.Alignment_Stopped);
                        UpdateStatus("连续对准已停止");
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "连续对准操作控制失败", EventIds.Alignment_Error);
                UpdateStatus($"连续对准操作控制失败: {ex.Message}");
                // 确保在异常情况下重置状态
                if (_alignMoreTimer.Enabled)
                {
                    _alignMoreTimer.Stop();
                }
                BTNAutoManage(false);
                AlignMore_state = -1;
            }
        }

        // 将AlignMore_Tick方法更新为使用取消令牌和异步操作
        private async void AlignMore_Tick(object sender, System.Timers.ElapsedEventArgs e)
        {
            // 停止计时器，防止重入
            _alignMoreTimer.Stop();
            
            try
            {
                // 如果正在关闭，则退出
                if (_isClosing || _cts.IsCancellationRequested)
                {
                    return;
                }
                
                // 创建取消令牌，支持超时取消
                using var localCts = CancellationTokenSource.CreateLinkedTokenSource(_cts.Token);
                localCts.CancelAfter(TimeSpan.FromSeconds(30)); // 30秒超时
                
                switch (AlignMore_state)
                {
                    case 0:
                        AlignMore_state = 1;
                        await _axisEventService.SendMsgAsync(4, 2, 0, 0, 0);
                        await SafeInvokeAsync(() => 
                        {
                            UpdateStatus("键合对准:检查信号");
                        });
                        break;
                    case 1:
                        var cameraAligned = await _axisEventService.ReceiveMsgAsync();
                        if (cameraAligned != null && cameraAligned.Contains("1"))
                        {
                            AlignMore_state = 0;
                            var xyrPos = await _axisEventService.GetXYRPosAsync();
                            if (xyrPos != null)
                            {
                                string[] Poses = xyrPos.Replace("|", "").Split(' ');
                                if (Poses.Length >= 3)
                                {
                                    await SafeInvokeAsync(() =>
                                    {
                                        UpdateStatus("键合对准:同步坐标");
                                        // 使用原有方式处理坐标值，不使用TargetX、TargetY和TargetR
                                        float x = Convert.ToSingle(Poses[0]);
                                        float y = Convert.ToSingle(Poses[1]);
                                        float r = Convert.ToSingle(Poses[2]);
                                    });
                                    await _axisEventService.MoveXYRAxesToPositionAsync(
                                        Convert.ToSingle(Poses[0]),
                                        Convert.ToSingle(Poses[1]),
                                        Convert.ToSingle(Poses[2]));
                                }
                            }
                        }
                        break;
                }
            }
            catch (OperationCanceledException)
            {
                // 操作被取消，记录日志
                Logger.LogWarning("连续对准操作被取消", EventIds.Alignment_Cancelled);
            }
            catch (Exception ex)
            {
                // 记录错误
                Logger.LogError(ex, "连续对准过程中发生错误", EventIds.Alignment_Error);
            }
            finally
            {
                // 如果没有被取消且页面仍在运行，重新启动计时器
                if (!_isClosing && !_cts.IsCancellationRequested)
                {
                    _alignMoreTimer.Start();
                }
            }
        }

        // 注意：_AlignOnceTimer和AlignOnce_state已经在TimerWrapper _alignOnceTimer和RegisterTimers方法中定义
        // 这里不需要重复定义

        #endregion 5键合对准

        #region  6释放卡盘

        private async void BtnReset_Click(object sender, EventArgs e)
        {
            if (_isClosing) return;

            try
            {
                // 禁用所有按钮，防止重复操作
                ALLBTNManage(false, -1);
                UpdateStatus("平台运动至初始位中...");
                
                // 重置和注册取消令牌
                _cts?.Cancel();
                _cts?.Dispose();
                _cts = new CancellationTokenSource();
                RegisterCancellationTokenSource("Reset", _cts);
                
                // 使用直接注入的MainWindowViewModel
                UpdateStatus("获取TakeConfirm变量中...");
                bool res = await _mainWindowViewModel.WaferInitialZ();
                if (!res)
                {
                    UpdateStatus("获取TakeConfirm变量失败");
                    Logger.LogError($"获取TakeConfirm变量失败", EventIds.Get_Take_Confirm_Variables_Failed);
                    ShowError("获取TakeConfirm变量失败");
                    return;
                }
                
                UpdateStatus("XYR运动至初始位中...");
                res = await _mainWindowViewModel.XYRMotion(0, 0, 0);
                if (!res)
                {
                    UpdateStatus("XYR轴运动至初始位失败");
                    Logger.LogError($"XYR轴运动至初始位失败", EventIds.Xyr_Motion_To_Initial_Location_Failed);
                    ShowError("XYR轴运动至初始位失败");
                    return;
                }
                
                res = await _mainWindowViewModel.TakeXYRConfirmed();
                if (!res)
                {
                    Logger.LogWarning("XYR轴确认变量校验失败，但继续执行", EventIds.Variable_Confirm_Failed);
                }
                else
                {
                    Logger.LogInformation("XYR轴运动至初始位成功", EventIds.Axis_Move_Completed);
                }
                
                // 等待Z轴运动完成
                res = await _mainWindowViewModel.VariableConfirm("TakeZIniatialFinished");
                if (!res)
                {
                    Logger.LogWarning("Z轴确认变量校验失败，但继续执行", EventIds.Variable_Confirm_Failed);
                }
                
                UpdateStatus("平台至初始位成功");
                Logger.LogInformation("平台复位成功完成", EventIds.Operation_Complete);
            }
            catch (OperationCanceledException)
            {
                // 操作被取消，这是预期行为
                Logger.LogInformation("平台复位操作被取消", EventIds.Operation_Cancelled);
                UpdateStatus("平台复位操作已取消");
            }
            catch (Exception ex)
            {
                // 处理其他异常
                Logger.LogError(ex, "平台复位操作失败", EventIds.Location_Failed_Specific);
                UpdateStatus($"平台复位操作失败: {ex.Message}");
            }
            finally
            {
                // 恢复UI状态
                if (!_isClosing && !this.IsDisposed)
                {
                    ALLBTNManage(true, -1);
                }
            }
        }
        // BWReset_DoWork和WReset_RunWorkerCompleted已移除，使用Task-based异步模式替代
        private async void BtnChuckUnLock_Click(object sender, EventArgs e)
        {
            if (_isClosing) return;

            try
            {
                // 禁用所有按钮，防止重复操作
                ALLBTNManage(false, -1);
                UpdateStatus("卡盘锁开中...");
                
                // 重置和注册取消令牌
                _cts?.Cancel();
                _cts?.Dispose();
                _cts = new CancellationTokenSource();
                RegisterCancellationTokenSource("ChuckUnlock", _cts);
                
                // 使用直接注入的MainWindowViewModel
                if (_mainWindowViewModel.ChuckLockState == (int)CylinderStatus.Open)
                {
                    _mainWindowViewModel.ChuckLockState = (int)CylinderStatus.Close;
                    await _mainWindowViewModel.ChuckLockExecute();
                }

                // 使用直接注入的PLC连接管理器
                var plc = _plcConnectionManager.GetPlcInstance("Main");
                
                PLCVarReadInfo ReadInPutChuckLeft = new() { Name = $"{AxisConstants.AXIS_GVL}.UpperChuckLeftUnLockSensor", Type = typeof(bool) };
                object retChuckLeft;
                retChuckLeft = await plc.ReadVariableAsync(ReadInPutChuckLeft, _cts.Token);

                PLCVarReadInfo ReadInPutChuckRight = new() { Name = $"{AxisConstants.AXIS_GVL}.UpperChuckRightUnLockSensor", Type = typeof(bool) };
                object retInPutChuckRight;
                retInPutChuckRight = await plc.ReadVariableAsync(ReadInPutChuckRight, _cts.Token);

                if (_mainWindowViewModel.ChuckLockState == (int)CylinderStatus.Close)
                {
                    bool success = (bool)retChuckLeft && (bool)retInPutChuckRight;
                    UpdateStatus(success ? "卡盘锁开成功!" : "卡盘锁开失败");
                    
                    if (!success)
                    {
                        Logger.LogError($"卡盘锁开失败", EventIds.Chuck_Lock_Open_Failed_Specific);
                        ShowError("卡盘锁开失败");
                    }
                    else
                    {
                        Logger.LogInformation("卡盘锁开成功", EventIds.Cylinder_Operation_Completed);
                    }
                }
            }
            catch (OperationCanceledException)
            {
                // 操作被取消，这是预期行为
                Logger.LogInformation("卡盘锁开操作被取消", EventIds.Operation_Cancelled);
                UpdateStatus("卡盘锁开操作已取消");
            }
            catch (Exception ex)
            {
                // 处理其他异常
                Logger.LogError(ex, "卡盘锁开操作失败", EventIds.Cylinder_Operation_Failed);
                UpdateStatus($"卡盘锁开操作失败: {ex.Message}");
            }
            finally
            {
                // 恢复UI状态
                if (!_isClosing && !this.IsDisposed)
                {
                    ALLBTNManage(true, -1);
                }
            }
        }

        #endregion  6释放卡盘


        #region 微调

        private async void BtnPos_Click(object sender, EventArgs e)
        {
            if (_isClosing) return;
            
            Button temp = (Button)sender;
            int AXISID = NameToId(temp.Name);
            
            // 对应位置的停止按钮可用
            ALLBTNManage(false, AXISID);
            // 使用AxisEventService存储当前轴ID
            _axisEventService.SetCurrentAxisId(AXISID);
            
            if (AXISID == -1)
            {
                Logger.LogError("轴序号是错误的", EventIds.Axis_Id_Error);
                MessageBox.Show("轴序号是错误的！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                ALLBTNManage(true, -1);
                return;
            }
            
            try
            {
                // 重置主取消令牌
                _cts?.Cancel();
                _cts?.Dispose();
                _cts = new CancellationTokenSource();
                RegisterCancellationTokenSource($"AxisPosition_{AXISID}", _cts);
                
                // 创建取消令牌，支持超时取消
                using var localCts = CancellationTokenSource.CreateLinkedTokenSource(_cts.Token);
                localCts.CancelAfter(TimeSpan.FromSeconds(30)); // 30秒超时
                
                UpdateStatus($"轴{AXISID}定位中...");
                Logger.LogInformation($"开始轴{AXISID}定位操作", EventIds.Axis_Positioning_Started);
                
                // 异步执行定位操作
                await Task.Run(async () => 
                {
                    // 开始轴运动
                    await GOPosition(AXISID, localCts.Token);
                    
                    // 等待轴到达指定位置
                    await WaitForAxisArrival(AXISID, localCts.Token);
                    
                }, localCts.Token);
                
                // 操作成功完成
                UpdateStatus($"轴{AXISID}定位完成!");
                Logger.LogInformation($"轴{AXISID}定位完成", EventIds.Operation_Complete);
            }
            catch (OperationCanceledException)
            {
                // 操作被取消
                Logger.LogWarning($"轴{AXISID}定位操作被取消", EventIds.Operation_Cancelled);
                UpdateStatus($"轴{AXISID}定位取消!");
                MessageBox.Show("您取消了操作!");
            }
            catch (Exception ex)
            {
                // 处理错误
                Logger.LogError(ex, $"轴{AXISID}定位过程中产生错误", EventIds.Location_Failed_Specific);
                UpdateStatus($"轴{AXISID}定位过程中产生错误: {ex.Message}");
                MessageBox.Show($"定位过程中产生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // 恢复按钮状态
                if (!_isClosing && !this.IsDisposed)
                {
                    ALLBTNManage(true, -1);
                }
            }
        }
        
        // 等待轴到达指定位置
        private async Task WaitForAxisArrival(int axisId, CancellationToken cancellationToken)
        {
            try
            {
                // 使用_axisFactory直接获取轴ViewModel，替代_constValueCompatibilityService
                switch (axisId)
                {
                    case 0: // X
                        {
                            var xAxis = _axisFactory.GetXAxisViewModel();
                            while (xAxis.GetRunState() != 0)
                            {
                                cancellationToken.ThrowIfCancellationRequested();
                                await Task.Delay(100, cancellationToken);
                            }
                        }
                        break;
                    case 1: // Y
                        {
                            var yAxis = _axisFactory.GetYAxisViewModel();
                            while (yAxis.GetRunState() != 0)
                            {
                                cancellationToken.ThrowIfCancellationRequested();
                                await Task.Delay(100, cancellationToken);
                            }
                        }
                        break;
                    case 2: // R
                        {
                            var rAxis = _axisFactory.GetRAxisViewModel();
                            while (rAxis.GetRunState() != 0)
                            {
                                cancellationToken.ThrowIfCancellationRequested();
                                await Task.Delay(100, cancellationToken);
                            }
                        }
                        break;
                    case 3: // Z
                        {
                            var zAxis = _axisFactory.GetZAxisViewModel();
                            while (!zAxis.Arrive_position)
                            {
                                cancellationToken.ThrowIfCancellationRequested();
                                await Task.Delay(100, cancellationToken);
                            }
                        }
                        break;
                    case 4: // LX
                        {
                            var lxAxis = _axisFactory.GetLXAxisViewModel();
                            while (!lxAxis.Arrive_position)
                            {
                                cancellationToken.ThrowIfCancellationRequested();
                                await Task.Delay(100, cancellationToken);
                            }
                        }
                        break;
                    case 5: // LY
                        {
                            var lyAxis = _axisFactory.GetLYAxisViewModel();
                            while (!lyAxis.Arrive_position)
                            {
                                cancellationToken.ThrowIfCancellationRequested();
                                await Task.Delay(100, cancellationToken);
                            }
                        }
                        break;
                    case 6: // LZ
                        {
                            var lzAxis = _axisFactory.GetLZAxisViewModel();
                            while (!lzAxis.Arrive_position)
                            {
                                cancellationToken.ThrowIfCancellationRequested();
                                await Task.Delay(100, cancellationToken);
                            }
                        }
                        break;
                    case 7: // RX
                        {
                            var rxAxis = _axisFactory.GetRXAxisViewModel();
                            while (!rxAxis.Arrive_position)
                            {
                                cancellationToken.ThrowIfCancellationRequested();
                                await Task.Delay(100, cancellationToken);
                            }
                        }
                        break;
                    case 8: // RY
                        {
                            var ryAxis = _axisFactory.GetRYAxisViewModel();
                            while (!ryAxis.Arrive_position)
                            {
                                cancellationToken.ThrowIfCancellationRequested();
                                await Task.Delay(100, cancellationToken);
                            }
                        }
                        break;
                    case 9: // RZ
                        {
                            var rzAxis = _axisFactory.GetRZAxisViewModel();
                            while (!rzAxis.Arrive_position)
                            {
                                cancellationToken.ThrowIfCancellationRequested();
                                await Task.Delay(100, cancellationToken);
                            }
                        }
                        break;
                }
            }
            catch (OperationCanceledException)
            {
                // 操作被取消（软件关闭或用户停止），这是预期行为，不需要记录错误
                Logger?.LogDebug($"轴{axisId}等待到位操作被取消", EventIds.Axis_Move_Error);
                // 重新抛出异常，让调用者知道操作被取消
                throw;
            }
            catch (Exception ex)
            {
                // 处理其他类型的异常
                Logger?.LogError(ex, $"等待轴{axisId}到位时发生错误", EventIds.Axis_Move_Error);
                throw;
            }
        }
        // BWPos_DoWork和BWPos_RunWorkerCompleted已移除，使用Task-based异步模式替代

        // 修改为实例方法，以便使用兼容层服务
        private async Task GOPosition(int id, CancellationToken cancellationToken = default)
        {
            try
            {
                // 使用_axisFactory直接获取轴ViewModel
                switch (id)
                {
                    case 0://X
                        { 
                            var xAxis = _axisFactory.GetXAxisViewModel();
                            // 先获取位置，然后再移动，避免.Result阻塞
                            cancellationToken.ThrowIfCancellationRequested();
                            var position = await xAxis.GetCurrentPositionAsync();
                            cancellationToken.ThrowIfCancellationRequested();
                            await xAxis.MoveToPositionAsync(position);
                        }
                        break;
                    case 1://Y
                        { 
                            var yAxis = _axisFactory.GetYAxisViewModel();
                            // 先获取位置，然后再移动，避免.Result阻塞
                            cancellationToken.ThrowIfCancellationRequested();
                            var position = await yAxis.GetCurrentPositionAsync();
                            cancellationToken.ThrowIfCancellationRequested();
                            await yAxis.MoveToPositionAsync(position);
                        }
                        break;
                    case 2://R
                        { 
                            var rAxis = _axisFactory.GetRAxisViewModel();
                            // 先获取位置，然后再移动，避免.Result阻塞
                            cancellationToken.ThrowIfCancellationRequested();
                            var position = await rAxis.GetCurrentPositionAsync();
                            cancellationToken.ThrowIfCancellationRequested();
                            await rAxis.MoveToPositionAsync(position);
                        }
                        break;
                    case 3://Z
                        { 
                            var zAxis = _axisFactory.GetZAxisViewModel();
                            // 先获取位置，然后再移动，避免.Result阻塞
                            cancellationToken.ThrowIfCancellationRequested();
                            var position = await zAxis.GetCurrentPositionAsync();
                            cancellationToken.ThrowIfCancellationRequested();
                            await zAxis.MoveToPositionAsync(position);
                        }
                        break;
                    case 4://LX
                        { 
                            var lxAxis = _axisFactory.GetLXAxisViewModel();
                            // 先获取位置，然后再移动，避免.Result阻塞
                            cancellationToken.ThrowIfCancellationRequested();
                            var position = await lxAxis.GetCurrentPositionAsync();
                            cancellationToken.ThrowIfCancellationRequested();
                            await lxAxis.MoveToPositionAsync(position);
                        }
                        break;
                    case 5://LY
                        { 
                            var lyAxis = _axisFactory.GetLYAxisViewModel();
                            // 先获取位置，然后再移动，避免.Result阻塞
                            cancellationToken.ThrowIfCancellationRequested();
                            var position = await lyAxis.GetCurrentPositionAsync();
                            cancellationToken.ThrowIfCancellationRequested();
                            await lyAxis.MoveToPositionAsync(position);
                        }
                        break;
                    case 6://LZ
                        { 
                            var lzAxis = _axisFactory.GetLZAxisViewModel();
                            // 先获取位置，然后再移动，避免.Result阻塞
                            cancellationToken.ThrowIfCancellationRequested();
                            var position = await lzAxis.GetCurrentPositionAsync();
                            cancellationToken.ThrowIfCancellationRequested();
                            await lzAxis.MoveToPositionAsync(position);
                        }
                        break;
                    case 7://RX 
                        { 
                            var rxAxis = _axisFactory.GetRXAxisViewModel();
                            // 先获取位置，然后再移动，避免.Result阻塞
                            cancellationToken.ThrowIfCancellationRequested();
                            var position = await rxAxis.GetCurrentPositionAsync();
                            cancellationToken.ThrowIfCancellationRequested();
                            await rxAxis.MoveToPositionAsync(position);
                        }
                        break;
                    case 8://RY  
                        { 
                            var ryAxis = _axisFactory.GetRYAxisViewModel();
                            // 先获取位置，然后再移动，避免.Result阻塞
                            cancellationToken.ThrowIfCancellationRequested();
                            var position = await ryAxis.GetCurrentPositionAsync();
                            cancellationToken.ThrowIfCancellationRequested();
                            await ryAxis.MoveToPositionAsync(position);
                        }
                        break;
                    case 9://RZ  
                        { 
                            var rzAxis = _axisFactory.GetRZAxisViewModel();
                            // 先获取位置，然后再移动，避免.Result阻塞
                            cancellationToken.ThrowIfCancellationRequested();
                            var position = await rzAxis.GetCurrentPositionAsync();
                            cancellationToken.ThrowIfCancellationRequested();
                            await rzAxis.MoveToPositionAsync(position);
                        }
                        break;
                }
            }
            catch (OperationCanceledException)
            {
                // 操作被取消，重新抛出异常以便上层处理
                Logger.LogWarning($"轴{id}移动操作被取消", EventIds.Operation_Cancelled);
                throw;
            }
            catch (Exception ex)
            {
                // 使用已注入的日志服务
                Logger.LogError(ex, $"轴{id}移动到位置时发生错误", EventIds.Axis_Move_Error);
                throw; // 重新抛出异常以便上层处理
            }
        }


        private async void BtnStop_Click(object sender, EventArgs e)
        {
            if (_isClosing) return;
            
            try
            {
                // 禁用按钮防止重复操作
                ALLBTNManage(false, -1);
                
                Button temp = (Button)sender;
                
                int AXISID = NameToId(temp.Name);
                if (AXISID == -1)
                {
                    Logger.LogError($"轴ID错误: {temp.Name}", EventIds.Axis_Id_Error);
                    ShowError("轴序号是错误的！");
                    return;
                }
                
                // 重置和注册取消令牌
                _cts?.Cancel();
                _cts?.Dispose();
                _cts = new CancellationTokenSource();
                RegisterCancellationTokenSource($"Stop_{AXISID}", _cts);
                
                // 使用新的异步方法
                await StopAsync(AXISID);
            }
            catch (Exception ex)
            {
                // 处理异常
                Logger.LogError(ex, "停止轴操作失败", EventIds.Axis_Stop_Error);
                ShowError($"停止轴操作失败: {ex.Message}");
            }
            finally
            {
                // 恢复UI状态
                if (!_isClosing && !this.IsDisposed)
                {
                    ALLBTNManage(true, -1);
                }
            }
        }
        /// <summary>
/// 停止指定轴的异步方法，支持异常处理
/// </summary>
/// <param name="axisId">要停止的轴ID</param>
/// <returns>Task表示异步操作</returns>
private async Task StopAsync(int axisId)
{
    if (_isClosing) return;
    
    try
    {
        // 检查ID有效性
        if (axisId < 0 || axisId > 9)
        {
            throw new ArgumentOutOfRangeException(nameof(axisId), $"无效的轴ID: {axisId}");
        }
        
        Logger.LogInformation($"开始停止轴{axisId}", EventIds.Axis_Jog_Stop_Started);
        
        switch (axisId)
        {
            case 0://X
                {
                    var xAxis = _axisFactory.GetXAxisViewModel();
                    await xAxis.StopAsync(); // 使用接口定义的方法
                    Logger.LogDebug($"X轴已停止", EventIds.Axis_Stop_Completed);
                }
                break;
            case 1://Y
                {
                    var yAxis = _axisFactory.GetYAxisViewModel();
                    await yAxis.StopAsync(); // 使用接口定义的方法
                    Logger.LogDebug($"Y轴已停止", EventIds.Axis_Stop_Completed);
                }
                break;
            case 2://R
                {
                    var rAxis = _axisFactory.GetRAxisViewModel();
                    await rAxis.StopAsync(); // 使用接口定义的方法
                    Logger.LogDebug($"R轴已停止", EventIds.Axis_Stop_Completed);
                }
                break;
            case 3://Z
                { 
                    var zAxis = _axisFactory.GetZAxisViewModel();
                    await zAxis.StopAsync(); // 使用接口定义的方法
                    Logger.LogDebug($"Z轴已停止", EventIds.Axis_Stop_Completed);
                }
                break;
            case 4://LX
                { 
                    var lxAxis = _axisFactory.GetLXAxisViewModel();
                    await lxAxis.StopAsync(); // 使用接口定义的方法
                    Logger.LogDebug($"LX轴已停止", EventIds.Axis_Stop_Completed);
                }
                break;
            case 5://LY
                { 
                    var lyAxis = _axisFactory.GetLYAxisViewModel();
                    await lyAxis.StopAsync(); // 使用接口定义的方法
                    Logger.LogDebug($"LY轴已停止", EventIds.Axis_Stop_Completed);
                }
                break;
            case 6://LZ
                { 
                    var lzAxis = _axisFactory.GetLZAxisViewModel();
                    await lzAxis.StopAsync(); // 使用接口定义的方法
                    Logger.LogDebug($"LZ轴已停止", EventIds.Axis_Stop_Completed);
                }
                break;
            case 7://RX 
                { 
                    var rxAxis = _axisFactory.GetRXAxisViewModel();
                    await rxAxis.StopAsync(); // 使用接口定义的方法
                    Logger.LogDebug($"RX轴已停止", EventIds.Axis_Stop_Completed);
                }
                break;
            case 8://RY  
                { 
                    var ryAxis = _axisFactory.GetRYAxisViewModel();
                    await ryAxis.StopAsync(); // 使用接口定义的方法
                    Logger.LogDebug($"RY轴已停止", EventIds.Axis_Stop_Completed);
                }
                break;
            case 9://RZ  
                { 
                    var rzAxis = _axisFactory.GetRZAxisViewModel();
                    await rzAxis.StopAsync(); // 使用接口定义的方法
                    Logger.LogDebug($"RZ轴已停止", EventIds.Axis_Stop_Completed);
                }
                break;
        }
    }
    catch (Exception ex)
    {
        Logger?.LogError(ex, $"轴{axisId}停止时发生错误", EventIds.Axis_Stop_Error);
        throw; // 重新抛出异常以便调用者处理
    }
}

        private async void BtnFront_MouseDown(object sender, MouseEventArgs e)
        {
            if (_isClosing) return;
            
            try
            {
                // 禁用按钮防止重复操作
                ALLBTNManage(false, -1);
                
                Button temp = (Button)sender;

                int AXISID = NameToId(temp.Name);
                if (AXISID == -1)
                {
                    Logger.LogError($"轴ID错误: {temp.Name}", EventIds.Axis_Id_Error);
                    ShowError("轴序号是错误的！");
                    return;
                }
                
                // 重置和注册取消令牌
                _cts?.Cancel();
                _cts?.Dispose();
                _cts = new CancellationTokenSource();
                RegisterCancellationTokenSource($"JogForward_{AXISID}", _cts);
                
                // 使用异步方法启动点动
                await JogForwardStartAsync(AXISID, _cts.Token);
            }
            catch (OperationCanceledException)
            {
                // 操作被取消，记录日志但不显示错误
                Logger.LogInformation("点动操作被取消", EventIds.Operation_Cancelled);
            }
            catch (Exception ex)
            {
                // 处理异常
                Logger.LogError(ex, "启动点动操作失败", EventIds.Axis_Jog_Error);
                ShowError($"启动点动失败: {ex.Message}");
            }
            finally
            {
                // 恢复UI状态
                if (!_isClosing && !this.IsDisposed)
                {
                    ALLBTNManage(true, -1);
                }
            }
        }
        /// <summary>
        /// 启动指定轴的正向点动
        /// </summary>
        /// <param name="axisId">轴ID</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>表示异步操作的Task</returns>
        private async Task JogForwardStartAsync(int axisId, CancellationToken cancellationToken = default)
        {
            if (_isClosing) 
            {
                throw new OperationCanceledException("页面正在关闭，无法执行轴操作");
            }
            
            try
            {
                Logger.LogInformation($"开始轴{axisId}正向点动", EventIds.Axis_Jog_Forward_Started);
                
                // 使用_axisFactory直接获取轴ViewModel
                switch (axisId)
                {
                    case 0://X
                        { 
                            var xAxis = _axisFactory.GetXAxisViewModel();
                            await xAxis.JogForwardAsync().ConfigureAwait(false); 
                        }
                        break;
                    case 1://Y
                        { 
                            var yAxis = _axisFactory.GetYAxisViewModel();
                            await yAxis.JogForwardAsync().ConfigureAwait(false); 
                        }
                        break;
                    case 2://R
                        { 
                            var rAxis = _axisFactory.GetRAxisViewModel();
                            await rAxis.JogForwardAsync().ConfigureAwait(false); 
                        }
                        break;
                    case 3://Z
                        { 
                            var zAxis = _axisFactory.GetZAxisViewModel();
                            await zAxis.JogForwardAsync().ConfigureAwait(false); 
                        }
                        break;
                    case 4://LX
                        { 
                            var lxAxis = _axisFactory.GetLXAxisViewModel();
                            await lxAxis.JogForwardAsync().ConfigureAwait(false); 
                        }
                        break;
                    case 5://LY
                        { 
                            var lyAxis = _axisFactory.GetLYAxisViewModel();
                            await lyAxis.JogForwardAsync().ConfigureAwait(false); 
                        }
                        break;
                    case 6://LZ
                        { 
                            var lzAxis = _axisFactory.GetLZAxisViewModel();
                            await lzAxis.JogForwardAsync().ConfigureAwait(false); 
                        }
                        break;
                    case 7://RX 
                        { 
                            var rxAxis = _axisFactory.GetRXAxisViewModel();
                            await rxAxis.JogForwardAsync().ConfigureAwait(false); 
                        }
                        break;
                    case 8://RY  
                        { 
                            var ryAxis = _axisFactory.GetRYAxisViewModel();
                            await ryAxis.JogForwardAsync().ConfigureAwait(false); 
                        }
                        break;
                    case 9://RZ  
                        { 
                            var rzAxis = _axisFactory.GetRZAxisViewModel();
                            await rzAxis.JogForwardAsync().ConfigureAwait(false); 
                        }
                        break;
                    default:
                        throw new ArgumentOutOfRangeException(nameof(axisId), $"不支持的轴ID: {axisId}");
                }
                
                Logger.LogInformation($"轴{axisId}正向点动启动成功", EventIds.Operation_Complete);
            }
            catch (OperationCanceledException)
            {
                Logger.LogWarning($"轴{axisId}正向点动被取消", EventIds.Operation_Cancelled);
                throw; // 重新抛出取消异常，允许调用者处理
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, $"轴{axisId}正向点动失败", EventIds.Axis_Jog_Error);
                UpdateStatus($"轴{axisId}正向点动失败: {ex.Message}");
                throw; // 重新抛出异常，确保调用者知道操作失败
            }
        }

        private async void BtnFront_MouseUp(object sender, MouseEventArgs e)
        {
            if (_isClosing) return;
            
            try
            {
                // 禁用按钮防止重复操作
                ALLBTNManage(false, -1);
                
                Button temp = (Button)sender;

                int AXISID = NameToId(temp.Name);
                if (AXISID == -1)
                {
                    Logger.LogError($"轴ID错误: {temp.Name}", EventIds.Axis_Id_Error);
                    ShowError("轴序号是错误的！");
                    return;
                }
                
                // 重置和注册取消令牌
                _cts?.Cancel();
                _cts?.Dispose();
                _cts = new CancellationTokenSource();
                RegisterCancellationTokenSource($"JogStop_{AXISID}", _cts);
                
                // 使用异步方法停止点动
                await JogStopAsync(AXISID, _cts.Token);
            }
            catch (Exception ex)
            {
                // 处理异常
                Logger.LogError(ex, "停止点动操作失败", EventIds.Axis_Jog_Error);
                ShowError($"停止点动失败: {ex.Message}");
            }
            finally
            {
                // 恢复UI状态
                if (!_isClosing && !this.IsDisposed)
                {
                    ALLBTNManage(true, -1);
                }
            }
        }

        private async void BtnBack_MouseDown(object sender, MouseEventArgs e)
        {
            if (_isClosing) return;
            
            try
            {
                // 禁用按钮防止重复操作
                ALLBTNManage(false, -1);
                
                Button temp = (Button)sender;

                int AXISID = NameToId(temp.Name);
                if (AXISID == -1)
                {
                    Logger.LogError($"轴ID错误: {temp.Name}", EventIds.Axis_Id_Error);
                    ShowError("轴序号是错误的！");
                    return;
                }
                
                // 重置和注册取消令牌
                _cts?.Cancel();
                _cts?.Dispose();
                _cts = new CancellationTokenSource();
                RegisterCancellationTokenSource($"JogBackward_{AXISID}", _cts);
                
                // 使用异步方法启动点动
                await JogBackwardStartAsync(AXISID, _cts.Token);
            }
            catch (OperationCanceledException)
            {
                // 操作被取消，记录日志但不显示错误
                Logger.LogInformation("点动操作被取消", EventIds.Operation_Cancelled);
            }
            catch (Exception ex)
            {
                // 处理异常
                Logger.LogError(ex, "启动点动操作失败", EventIds.Axis_Jog_Error);
                ShowError($"启动点动失败: {ex.Message}");
            }
            finally
            {
                // 恢复UI状态
                if (!_isClosing && !this.IsDisposed)
                {
                    ALLBTNManage(true, -1);
                }
            }
        }
        /// <summary>
        /// 启动指定轴的反向点动
        /// </summary>
        /// <param name="axisId">轴ID</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>表示异步操作的Task</returns>
        private async Task JogBackwardStartAsync(int axisId, CancellationToken cancellationToken = default)
        {
            if (_isClosing) 
            {
                throw new OperationCanceledException("页面正在关闭，无法执行轴操作");
            }
            
            try
            {
                Logger.LogInformation($"开始轴{axisId}反向点动", EventIds.Axis_Jog_Backward_Started);
                
                // 使用_axisFactory直接获取轴ViewModel
                switch (axisId)
                {
                    case 0://X
                        { 
                            var xAxis = _axisFactory.GetXAxisViewModel();
                            await xAxis.JogBackwardAsync().ConfigureAwait(false); 
                        }
                        break;
                    case 1://Y
                        { 
                            var yAxis = _axisFactory.GetYAxisViewModel();
                            await yAxis.JogBackwardAsync().ConfigureAwait(false); 
                        }
                        break;
                    case 2://R
                        { 
                            var rAxis = _axisFactory.GetRAxisViewModel();
                            await rAxis.JogBackwardAsync().ConfigureAwait(false); 
                        }
                        break;
                    case 3://Z
                        { 
                            var zAxis = _axisFactory.GetZAxisViewModel();
                            await zAxis.JogBackwardAsync().ConfigureAwait(false); 
                        }
                        break;
                    case 4://LX
                        { 
                            var lxAxis = _axisFactory.GetLXAxisViewModel();
                            await lxAxis.JogBackwardAsync().ConfigureAwait(false); 
                        }
                        break;
                    case 5://LY
                        { 
                            var lyAxis = _axisFactory.GetLYAxisViewModel();
                            await lyAxis.JogBackwardAsync().ConfigureAwait(false); 
                        }
                        break;
                    case 6://LZ
                        { 
                            var lzAxis = _axisFactory.GetLZAxisViewModel();
                            await lzAxis.JogBackwardAsync().ConfigureAwait(false); 
                        }
                        break;
                    case 7://RX 
                        { 
                            var rxAxis = _axisFactory.GetRXAxisViewModel();
                            await rxAxis.JogBackwardAsync().ConfigureAwait(false); 
                        }
                        break;
                    case 8://RY  
                        { 
                            var ryAxis = _axisFactory.GetRYAxisViewModel();
                            await ryAxis.JogBackwardAsync().ConfigureAwait(false); 
                        }
                        break;
                    case 9://RZ  
                        { 
                            var rzAxis = _axisFactory.GetRZAxisViewModel();
                            await rzAxis.JogBackwardAsync().ConfigureAwait(false); 
                        }
                        break;
                    default:
                        throw new ArgumentOutOfRangeException(nameof(axisId), $"不支持的轴ID: {axisId}");
                }
                
                Logger.LogInformation($"轴{axisId}反向点动启动成功", EventIds.Operation_Complete);
            }
            catch (OperationCanceledException)
            {
                Logger.LogWarning($"轴{axisId}反向点动被取消", EventIds.Operation_Cancelled);
                throw; // 重新抛出取消异常，允许调用者处理
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, $"轴{axisId}反向点动失败", EventIds.Axis_Jog_Error);
                UpdateStatus($"轴{axisId}反向点动失败: {ex.Message}");
                throw; // 重新抛出异常，确保调用者知道操作失败
            }
        }

        private async void BtnBack_MouseUp(object sender, MouseEventArgs e)
        {
            if (_isClosing) return;
            
            try
            {
                // 禁用按钮防止重复操作
                ALLBTNManage(false, -1);
                
                Button temp = (Button)sender;

                int AXISID = NameToId(temp.Name);
                if (AXISID == -1)
                {
                    Logger.LogError($"轴ID错误: {temp.Name}", EventIds.Axis_Id_Error);
                    ShowError("轴序号是错误的！");
                    return;
                }
                
                // 重置和注册取消令牌
                _cts?.Cancel();
                _cts?.Dispose();
                _cts = new CancellationTokenSource();
                RegisterCancellationTokenSource($"JogStop_{AXISID}", _cts);
                
                // 使用异步方法停止点动
                await JogStopAsync(AXISID, _cts.Token);
            }
            catch (Exception ex)
            {
                // 处理异常
                Logger.LogError(ex, "停止点动操作失败", EventIds.Axis_Jog_Error);
                ShowError($"停止点动失败: {ex.Message}");
            }
            finally
            {
                // 恢复UI状态
                if (!_isClosing && !this.IsDisposed)
                {
                    ALLBTNManage(true, -1);
                }
            }
        }
        /// <summary>
        /// 停止指定轴的点动操作
        /// </summary>
        /// <param name="axisId">轴ID</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>表示异步操作的Task</returns>
        private async Task JogStopAsync(int axisId, CancellationToken cancellationToken = default)
        {
            if (_isClosing) 
            {
                throw new OperationCanceledException("页面正在关闭，无法执行轴操作");
            }
            
            try
            {
                Logger.LogInformation($"停止轴{axisId}点动", EventIds.Axis_Jog_Stop_Started);
                
                // 使用_axisFactory直接获取轴ViewModel
                switch (axisId)
                {
                    case 0://X
                        { 
                            var xAxis = _axisFactory.GetXAxisViewModel();
                            await xAxis.JogStopAsync().ConfigureAwait(false); 
                        }
                        break;
                    case 1://Y
                        { 
                            var yAxis = _axisFactory.GetYAxisViewModel();
                            await yAxis.JogStopAsync().ConfigureAwait(false); 
                        }
                        break;
                    case 2://R
                        { 
                            var rAxis = _axisFactory.GetRAxisViewModel();
                            await rAxis.JogStopAsync().ConfigureAwait(false); 
                        }
                        break;
                    case 3://Z
                        { 
                            var zAxis = _axisFactory.GetZAxisViewModel();
                            await zAxis.JogStopAsync().ConfigureAwait(false); 
                        }
                        break;
                    case 4://LX
                        { 
                            var lxAxis = _axisFactory.GetLXAxisViewModel();
                            await lxAxis.JogStopAsync().ConfigureAwait(false); 
                        }
                        break;
                    case 5://LY
                        { 
                            var lyAxis = _axisFactory.GetLYAxisViewModel();
                            await lyAxis.JogStopAsync().ConfigureAwait(false); 
                        }
                        break;
                    case 6://LZ
                        { 
                            var lzAxis = _axisFactory.GetLZAxisViewModel();
                            await lzAxis.JogStopAsync().ConfigureAwait(false); 
                        }
                        break;
                    case 7://RX
                        { 
                            var rxAxis = _axisFactory.GetRXAxisViewModel();
                            await rxAxis.JogStopAsync().ConfigureAwait(false); 
                        }
                        break;
                    case 8://RY
                        { 
                            var ryAxis = _axisFactory.GetRYAxisViewModel();
                            await ryAxis.JogStopAsync().ConfigureAwait(false); 
                        }
                        break;
                    case 9://RZ
                        { 
                            var rzAxis = _axisFactory.GetRZAxisViewModel();
                            await rzAxis.JogStopAsync().ConfigureAwait(false); 
                        }
                        break;
                    default:
                        throw new ArgumentOutOfRangeException(nameof(axisId), $"不支持的轴ID: {axisId}");
                }
                
                Logger.LogInformation($"轴{axisId}点动停止成功", EventIds.Operation_Complete);
            }
            catch (OperationCanceledException)
            {
                Logger.LogWarning($"轴{axisId}点动停止操作被取消", EventIds.Operation_Cancelled);
                throw; // 重新抛出取消异常，允许调用者处理
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, $"轴{axisId}停止点动失败", EventIds.Axis_Jog_Error);
                UpdateStatus($"轴{axisId}停止点动失败: {ex.Message}");
                throw; // 重新抛出异常，确保调用者知道操作失败
            }
        }
        private async void Target_KeyUp(object sender, KeyEventArgs e)
{
    if (_isClosing) return;

    UITextBox temp = (UITextBox)sender;

    try
    {
        string TempText = temp.Text.Trim();
        if (TempText == "+" || TempText == "-") return;
        
        float TarPos = (float)Convert.ToDouble(TempText);
        int AXISID = NameToId(temp.Name);
        
        if (AXISID == -1)
        {
            Logger.LogError($"轴ID错误: {temp.Name}", EventIds.Axis_Id_Error);
            ShowError("轴序号是错误的！");
            return;
        }
        
        // 重置和注册取消令牌
        _cts?.Cancel();
        _cts?.Dispose();
        _cts = new CancellationTokenSource();
        RegisterCancellationTokenSource($"SetPosition_{AXISID}", _cts);
        
        Logger.LogInformation($"开始设置轴{AXISID}位置为{TarPos}", EventIds.Axis_Positioning_Started);
        
        // 使用新的异步方法，传递取消令牌
        await SetPositionAsync(TarPos, AXISID, _cts.Token);
        
        Logger.LogInformation($"轴{AXISID}位置设置完成", EventIds.Operation_Complete);
    }
    catch (OperationCanceledException)
    {
        // 操作被取消，这是预期行为
        Logger.LogInformation("轴位置设置操作被取消", EventIds.Operation_Cancelled);
    }
    catch (Exception ex)
    {
        // 处理其他异常
        Logger.LogError(ex, "设置轴位置失败", EventIds.Axis_Move_Error);
        ShowError($"设置轴位置失败: {ex.Message}");
    }
}
        private int NameToId(string Name)
        {
            int ID = -1;
            if (Name.IndexOf("X") != -1) //U
            {
                ID = 0;
            }
            if (Name.IndexOf("Y") != -1)//V
            {
                ID = 1;
            }
            if (Name.IndexOf("R") != -1)//W
            {
                ID = 2;
            }
            if (Name.IndexOf("Z") != -1)//Z
            {
                ID = 3;
            }
            if (Name.IndexOf("LX") != -1)//LZ
            {
                ID = 4;
            }
            if (Name.IndexOf("LY") != -1)//LZ
            {
                ID = 5;
            }
            if (Name.IndexOf("LZ") != -1)//LZ
            {
                ID = 6;
            }
            if (Name.IndexOf("RX") != -1)//RZ
            {
                ID = 7;
            }
            if (Name.IndexOf("RY") != -1)//RZ
            {
                ID = 8;
            }
            if (Name.IndexOf("RZ") != -1)//RZ
            {
                ID = 9;
            }
            return ID;
        }

        /// <summary>
/// 设置轴位置的异步方法，支持取消
/// </summary>
/// <param name="targetPosition">目标位置</param>
/// <param name="axisId">轴ID</param>
/// <param name="cancellationToken">取消令牌</param>
/// <returns>Task表示异步操作</returns>
private async Task SetPositionAsync(float targetPosition, int axisId, CancellationToken cancellationToken = default)
{
    if (_isClosing)
    {
        throw new OperationCanceledException("页面正在关闭，无法执行轴操作");
    }
    
    try
    {
        // 检查ID有效性
        if (axisId < 0 || axisId > 9)
        {
            throw new ArgumentOutOfRangeException(nameof(axisId), $"无效的轴ID: {axisId}");
        }
        
        // 使用AxisEventService或兼容层替代直接ConstValue调用
        switch (axisId)
        {
            case 0://X
            { 
                // 使用轴常量替代硬编码值
                var xAxis = _axisFactory.GetXAxisViewModel();
                
                // 检查取消请求
                cancellationToken.ThrowIfCancellationRequested();
                
                await xAxis.SetPositionAsync((int)(targetPosition * AxisConstants.AXIS_XY_MULTIPLE_CONVERTION)); // 使用接口定义的方法
                Logger.LogDebug($"X轴位置设置为: {targetPosition}", EventIds.Axis_Move_Started);
            }
            break;
            
            case 1://Y
            { 
                var yAxis = _axisFactory.GetYAxisViewModel();
                
                // 检查取消请求
                cancellationToken.ThrowIfCancellationRequested();
                
                await yAxis.SetPositionAsync((int)(targetPosition * AxisConstants.AXIS_XY_MULTIPLE_CONVERTION)); 
                Logger.LogDebug($"Y轴位置设置为: {targetPosition}", EventIds.Axis_Move_Started);
            }
            break;
            
            case 2://R
            { 
                var rAxis = _axisFactory.GetRAxisViewModel();
                
                // 检查取消请求
                cancellationToken.ThrowIfCancellationRequested();
                
                await rAxis.SetPositionAsync((int)(targetPosition * AxisConstants.AXIS_R_MULTIPLE_CONVERTION)); 
                Logger.LogDebug($"R轴位置设置为: {targetPosition}", EventIds.Axis_Move_Started);
            }
            break;
            
            case 3://Z
            { 
                var zAxis = _axisFactory.GetZAxisViewModel();
                
                // 检查取消请求
                cancellationToken.ThrowIfCancellationRequested();
                
                await zAxis.SetPositionAsync(targetPosition * AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION); 
                Logger.LogDebug($"Z轴位置设置为: {targetPosition}", EventIds.Axis_Move_Started);
            }
            break;
            
            case 4://LX
            { 
                var lxAxis = _axisFactory.GetLXAxisViewModel();
                
                // 检查取消请求
                cancellationToken.ThrowIfCancellationRequested();
                
                await lxAxis.SetPositionAsync(targetPosition * AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION); 
                Logger.LogDebug($"LX轴位置设置为: {targetPosition}", EventIds.Axis_Move_Started);
            }
            break;
            
            case 5://LY
            { 
                var lyAxis = _axisFactory.GetLYAxisViewModel();
                
                // 检查取消请求
                cancellationToken.ThrowIfCancellationRequested();
                
                await lyAxis.SetPositionAsync(targetPosition * AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION); 
                Logger.LogDebug($"LY轴位置设置为: {targetPosition}", EventIds.Axis_Move_Started);
            }
            break;
            
            case 6://LZ
            { 
                var lzAxis = _axisFactory.GetLZAxisViewModel();
                
                // 检查取消请求
                cancellationToken.ThrowIfCancellationRequested();
                
                await lzAxis.SetPositionAsync(targetPosition); 
                Logger.LogDebug($"LZ轴位置设置为: {targetPosition}", EventIds.Axis_Move_Started);
            }
            break;
            
            case 7://RX 
            { 
                var rxAxis = _axisFactory.GetRXAxisViewModel();
                
                // 检查取消请求
                cancellationToken.ThrowIfCancellationRequested();
                
                await rxAxis.SetPositionAsync(targetPosition * AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION); 
                Logger.LogDebug($"RX轴位置设置为: {targetPosition}", EventIds.Axis_Move_Started);
            }
            break;
            
            case 8://RY  
            { 
                var ryAxis = _axisFactory.GetRYAxisViewModel();
                
                // 检查取消请求
                cancellationToken.ThrowIfCancellationRequested();
                
                await ryAxis.SetPositionAsync(targetPosition * AxisConstants.AXIS_ZLXYRXYRPOS_MULTIPLE_CONVERTION); 
                Logger.LogDebug($"RY轴位置设置为: {targetPosition}", EventIds.Axis_Move_Started);
            }
            break;
            
            case 9://RZ  
            { 
                var rzAxis = _axisFactory.GetRZAxisViewModel();
                
                // 检查取消请求
                cancellationToken.ThrowIfCancellationRequested();
                
                await rzAxis.SetPositionAsync(targetPosition); 
                Logger.LogDebug($"RZ轴位置设置为: {targetPosition}", EventIds.Axis_Move_Started);
            }
            break;
        }
    }
    catch (OperationCanceledException)
    {
        Logger.LogInformation($"轴{axisId}位置设置操作被取消", EventIds.Operation_Cancelled);
        throw; // 重新抛出异常，以便调用者处理
    }
    catch (Exception ex)
    {
        Logger.LogError(ex, $"轴{axisId}位置设置失败: {ex.Message}", EventIds.Axis_Move_Error);
        throw; // 重新抛出异常，以便调用者处理
    }
}
        #endregion 微调


        #region 按钮管理
        void BTNAutoManage(bool EnableState)
        {
            BtnChuckLock.Enabled = EnableState;

            BtnTopTrayWaferOuter.Enabled = EnableState;
            BtnTopWaferPhotoPos.Enabled = EnableState;
            BtnTopWaferUp.Enabled = EnableState;
            BtnTopWaferPhoto.Enabled = EnableState;
            BtnTopWaferTakeUp.Enabled = EnableState;
            //BtnReset.Enabled = EnableState;
            //BtnTopMarkSearch.Enabled = EnableState;


            BtnBottomTrayWaferOuter.Enabled = EnableState;
            BtnBottomWaferDown.Enabled = EnableState;
            BtnAignMore.Enabled = EnableState;
            BtnBottomWaferTakeDown.Enabled = EnableState;

            BtnXYRZReset.Enabled = EnableState;
            BtnChuckUnLock.Enabled = EnableState;
        }
        void BTNManualManage(bool EnableState, int StopSign)
        {
            //Btn停止LZ.Enabled = EnableState;
            if (StopSign == 6 || StopSign == -1)
            {
                BtnPosLZ.Enabled = EnableState;
                BtnFrontLZ.Enabled = EnableState;
                BtnBackLZ.Enabled = EnableState;
            }


            //Btn停止RZ.Enabled = EnableState;
            if (StopSign == 9 || StopSign == -1)
            {
                BtnPosRZ.Enabled = EnableState;
                BtnFrontRZ.Enabled = EnableState;
                BtnBackRZ.Enabled = EnableState;
            }



            //Btn停止U.Enabled = EnableState;
            if (StopSign == 0 || StopSign == -1)
            {
                BtnPosX.Enabled = EnableState;
                BtnFrontY.Enabled = EnableState;
                BtnBackX.Enabled = EnableState;
            }



            //Btn停止V.Enabled = EnableState;
            if (StopSign == 1 || StopSign == -1)
            {
                BtnPosY.Enabled = EnableState;
                BtnFrontX.Enabled = EnableState;
                BtnBackY.Enabled = EnableState;
            }



            //Btn停止W.Enabled = EnableState;
            if (StopSign == 2 || StopSign == -1)
            {
                BtnPosR.Enabled = EnableState;
                BtnFrontR.Enabled = EnableState;
                BtnBackR.Enabled = EnableState;
            }


            //Btn停止Z.Enabled = EnableState;
            if (StopSign == 3 || StopSign == -1)
            {
                BtnPosZ.Enabled = EnableState;
                BtnFrontZ.Enabled = EnableState;
                BtnBackZ.Enabled = EnableState;
            }
        }

        void ALLBTNManage(bool EnableState, int StopSign)
        {
            BTNManualManage(EnableState, StopSign);
            //btnAll停止.Enabled = EnableState;
            if (OpenParaFile)
                BTNAutoManage(EnableState);
        }

        #endregion 按钮管理

        #region  一次对位
        // 注意：这里不需要重复定义，使用上面已定义的TimerWrapper
        private async void Btn键合对准_Click(object sender, EventArgs e)
        {
            if (_isClosing) return;

            try
            {
                BTNManualManage(false, 0);
                
                // 重置和注册取消令牌
                _cts?.Cancel();
                _cts?.Dispose();
                _cts = new CancellationTokenSource();
                RegisterCancellationTokenSource("SingleAlignment", _cts);
                
                AlignOnce_state = 0;
                Logger.LogInformation("开始单次对准", EventIds.Single_Alignment_Started);
                UpdateStatus("开始单次对准...");
                _alignOnceTimer.Interval = 500;
                _alignOnceTimer.Start();
            }
            catch (Exception ex)
            {
                // 处理异常
                Logger.LogError(ex, "启动单次对准操作失败", EventIds.Single_Alignment_Error);
                UpdateStatus($"启动单次对准操作失败: {ex.Message}");
                
                // 确保在异常情况下重置状态
                AlignOnce_state = -1;
                BTNManualManage(true, 0);
            }
        }

        // 更新AlignOnce_Tick方法使用取消令牌和异步操作
        private async void AlignOnce_Tick(object sender, System.Timers.ElapsedEventArgs e)
        {
            // 停止计时器，防止重入
            _alignOnceTimer.Stop();
            
            try
            {
                // 如果正在关闭，则退出
                if (_isClosing || _cts.IsCancellationRequested)
                {
                    return;
                }
                
                // 创建取消令牌，支持超时取消
                using var localCts = CancellationTokenSource.CreateLinkedTokenSource(_cts.Token);
                localCts.CancelAfter(TimeSpan.FromSeconds(30)); // 30秒超时
                
                switch (AlignOnce_state)
                {
                    case 0:
                        AlignOnce_state = 1;
                        await _axisEventService.SendMsgAsync(4, 2, 0, 0, 0);
                        await SafeInvokeAsync(() => 
                        {
                            UpdateStatus("键合对准:检查信号");
                        });
                        break;
                    case 1:
                        var cameraAligned = await _axisEventService.ReceiveMsgAsync();
                        if (cameraAligned != null && cameraAligned.Contains("1"))
                        {
                            var xyrPos = await _axisEventService.GetXYRPosAsync();
                            if (xyrPos != null)
                            {
                                string[] Poses = xyrPos.Replace("|", "").Split(' ');
                                if (Poses.Length >= 3)
                                {
                                    await SafeInvokeAsync(() =>
                                    {
                                        UpdateStatus("键合对准:同步坐标");
                                        // 使用原有方式处理坐标值，不使用TargetX、TargetY和TargetR
                                        float x = Convert.ToSingle(Poses[0]);
                                        float y = Convert.ToSingle(Poses[1]);
                                        float r = Convert.ToSingle(Poses[2]);
                                    });
                                    await _axisEventService.MoveXYRAxesToPositionAsync(
                                        Convert.ToSingle(Poses[0]),
                                        Convert.ToSingle(Poses[1]),
                                        Convert.ToSingle(Poses[2]));
                                    
                                    AlignOnce_state = 2;
                                }
                            }
                        }
                        break;
                    case 2:
                        var xyStates = _axisEventService.GetXYRRunStates();
                        if (xyStates.XState == 0 && xyStates.YState == 0 && xyStates.RState == 0)
                        {
                            AlignOnce_state = -1;
                            _alignOnceTimer.RemoveElapsedHandler(AlignOnce_Tick);
                            await SafeInvokeAsync(() => 
                            {
                                UpdateStatus("键合对准完成");
                                BTNManualManage(true, 1);
                            });
                            Logger.LogInformation("单次对准完成", EventIds.Operation_Complete);
                            return; // 对准完成，不再重启计时器
                        }
                        break;
                }
            }
            catch (OperationCanceledException)
            {
                // 操作被取消，记录日志
                Logger.LogWarning("单次对准操作被取消", EventIds.Operation_Cancelled);
                AlignOnce_state = -1;
                _alignOnceTimer.RemoveElapsedHandler(AlignOnce_Tick);
                await SafeInvokeAsync(() => BTNManualManage(true, 1));
            }
            catch (Exception ex)
            {
                // 记录错误
                Logger.LogError(ex, "单次对准过程中发生错误", EventIds.Single_Alignment_Error);
                AlignOnce_state = -1;
                _alignOnceTimer.RemoveElapsedHandler(AlignOnce_Tick);
                await SafeInvokeAsync(() => 
                {
                    UpdateStatus($"单次对准错误: {ex.Message}");
                    BTNManualManage(true, 1);
                });
            }
            finally
            {
                // 如果没有被取消且页面仍在运行，重新启动计时器
                if (!_isClosing && !_cts.IsCancellationRequested && AlignOnce_state >= 0)
                {
                    _alignOnceTimer.Start();
                }
            }
        }

        #endregion

        protected override async Task<bool> OnClosingAsync()
        {
            // 设置关闭标志
            _isClosing = true;
            
            try
            {
                // 取消所有正在进行的异步操作
                _cts?.Cancel();
                
                // 停止定时器
                _alignMoreTimer?.Stop();
                _alignOnceTimer?.Stop();
                
                // 给异步操作一点时间来响应取消请求
                await Task.Delay(200);
            }
            catch (OperationCanceledException)
            {
                // 忽略取消异常，这是预期行为
                Logger?.LogDebug("页面关闭时的任务取消异常被忽略", EventIds.Form_Closing_Error);
            }
            catch (Exception ex)
            {
                // 记录其他异常，但不阻止关闭
                Logger?.LogError(ex, "取消异步操作时发生错误", EventIds.Form_Closing_Error);
            }
            
            // 执行基类关闭逻辑
            return await base.OnClosingAsync();
        }

        public override void CleanUp()
        {
            try
            {
                // 设置关闭标志
                _isClosing = true;
                
                // 取消所有任务
                _cts?.Cancel();
                
                // 清理TimerWrapper实例 - 它们会自动管理事件处理器
                _alignMoreTimer?.Dispose();
                _alignOnceTimer?.Dispose();
                
                // 清理订阅
                logObserver?.UnSubscribe();
                
                // 资源释放
                _cts?.Dispose();
                _cts = null;
                
                // 调用基类清理
                base.CleanUp();
            }
            catch (OperationCanceledException)
            {
                // 忽略取消异常，这是预期行为
                Logger?.LogDebug("页面清理时的任务取消异常被忽略", EventIds.Resource_Cleanup_Error);
            }
            catch (Exception ex)
            {
                Logger?.LogError(ex, "页面资源清理时发生错误", EventIds.Resource_Cleanup_Error);
            }
        }

        // 添加异步清理方法
        public async Task CleanupAsync()
        {
            try
            {
                // 设置关闭标志
                _isClosing = true;
                
                // 取消所有任务
                _cts?.Cancel();
                
                // 等待任务取消
                await Task.Delay(100);
                
                // 清理TimerWrapper实例 - 它们会自动管理事件处理器
                _alignMoreTimer?.Dispose();
                _alignOnceTimer?.Dispose();
                
                // 清理订阅
                logObserver?.UnSubscribe();
                
                // 资源释放
                _cts?.Dispose();
                _cts = null;
            }
            catch (OperationCanceledException)
            {
                // 忽略取消异常，这是预期行为
                Logger?.LogDebug("页面异步清理时的任务取消异常被忽略", EventIds.Resource_Cleanup_Error);
            }
            catch (Exception ex)
            {
                Logger?.LogError(ex, "页面资源异步清理时发生错误", EventIds.Resource_Cleanup_Error);
            }
        }

        // 此方法已移除，因为所有BackgroundWorker已替换为Task-based异步模式

        // 移除自定义的SafeInvokeAsync实现，统一使用BasePage提供的方法
        private void UpdateStatus(string status)
        {
            _statusUpdateService?.UpdateStatus(status);
        }
    }
    
}