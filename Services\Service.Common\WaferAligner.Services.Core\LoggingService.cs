using WaferAligner.Infrastructure.Common;
using WaferAligner.Core.Business;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Reactive.Subjects;
using System.Reactive.Linq;
using System.Runtime.CompilerServices;
using System.Text.Json;
using System.Threading.Channels;
using WaferAligner.Services.Abstractions;
using Microsoft.Extensions.Logging;

namespace WaferAligner.Services.Core
{
    /// <summary>
    /// 增强的日志服务实现 - 重构版本
    /// 支持按模块日志级别控制、结构化日志、性能监控等新功能
    /// </summary>
    public class LoggingService : ILoggingService
    {
        #region 现有字段 - 保持兼容性

        public IObservable<ILogger> LogFeed { get; }
        IObservable<LogEntry<string>> ILoggingService.LogFeed => subject;

        private readonly Subject<LogEntry<string>> subject = new Subject<LogEntry<string>>();
        private LogLevel _minimumLogLevel = LogLevel.Information;
        private readonly List<ILogger> loggers = new List<ILogger>();

        #endregion

        #region 新增字段

        // 按模块的日志级别配置
        private readonly ConcurrentDictionary<string, LogLevel> _moduleLogLevels = new ConcurrentDictionary<string, LogLevel>();

        // 日志配置
        private LoggingConfiguration _configuration;

        #endregion

        #region 构造函数

        public LoggingService() : this(LoggingConfiguration.CreateDefaultConfiguration())
        {
        }

        public LoggingService(LoggingConfiguration configuration)
        {
            _configuration = configuration ?? LoggingConfiguration.CreateDefaultConfiguration();
            _minimumLogLevel = _configuration.GlobalMinimumLevel;

            // 初始化模块级别配置
            foreach (var kvp in _configuration.ModuleLevels)
            {
                _moduleLogLevels.TryAdd(kvp.Key, kvp.Value);
            }

            // 初始化日志记录器
            var fileLogger = new FileLogger(this);
            //var pglogger = new PgLogger(this);
            loggers.Add(fileLogger);
            //loggers.Add(pglogger);
        }

        #endregion

        #region 现有方法 - 保持兼容性

        public void SetMinimumLogLevel(LogLevel level)
        {
            _minimumLogLevel = level;
            _configuration.GlobalMinimumLevel = level;

            // 更新所有日志记录器的日志级别
            foreach (var logger in loggers)
            {
                if (logger is FileLogger fileLogger)
                {
                    fileLogger.SetMinimumLogLevel(level);
                }
            }
        }

        public LogLevel GetMinimumLogLevel()
        {
            return _minimumLogLevel;
        }

        public void Log(LogLevel logLevel, EventId eventId, [CallerMemberName] string message = null)
        {
            // 检查日志级别是否应该被记录
            if (!ShouldLog(null, logLevel))
                return;

            subject.OnNext(new LogEntry<string>
            {
                LogLevel = logLevel,
                Id = eventId,
                LogMessage = message,
                TimeStamp = DateTime.Now
            });

            foreach (var logger in loggers)
            {
                logger.Log(logLevel, eventId, message, null, (state, exception) => state?.ToString() ?? string.Empty);
            }
        }

        public void Log<TState>(LogLevel logLevel, EventId eventId, TState state, Exception exception, Func<TState, Exception, string> formatter)
        {
            // 检查日志级别是否应该被记录
            if (!ShouldLog(null, logLevel))
                return;

            var message = formatter(state, exception);
            subject.OnNext(new LogEntry<string>
            {
                LogLevel = logLevel,
                Id = eventId,
                LogMessage = message,
                TimeStamp = DateTime.Now
            });

            foreach (var logger in loggers)
            {
                logger.Log(logLevel, eventId, state, exception, formatter);
            }
        }

        public ChannelReader<LogEntry<string>> StreamLog()
        {
            var channel = Channel.CreateUnbounded<LogEntry<string>>();
            var writer = channel.Writer;

            subject.ObserveOn(System.Reactive.Concurrency.TaskPoolScheduler.Default)
                   .Subscribe(
                       onNext: entry => writer.TryWrite(entry),
                       onError: ex => writer.TryComplete(ex),
                       onCompleted: () => writer.TryComplete()
                   );

            return channel.Reader;
        }

        #endregion

        #region 新增方法

        public void SetModuleLogLevel(string moduleName, LogLevel level)
        {
            _moduleLogLevels.AddOrUpdate(moduleName, level, (key, oldValue) => level);
            _configuration.ModuleLevels[moduleName] = level;
        }

        public LogLevel GetModuleLogLevel(string moduleName)
        {
            return _moduleLogLevels.TryGetValue(moduleName, out var level) ? level : _minimumLogLevel;
        }

        public void LogStructured<T>(LogLevel logLevel, EventId eventId, string message, T data)
        {
            if (!ShouldLog(null, logLevel))
                return;

            var structuredMessage = $"{message} | Data: {JsonSerializer.Serialize(data)}";
            Log(logLevel, eventId, structuredMessage);
        }

        public IDisposable BeginPerformanceScope(string operationName, EventId eventId)
        {
            return new PerformanceScope(this, operationName, eventId);
        }

        public bool IsEnabled(string moduleName, LogLevel logLevel)
        {
            return ShouldLog(moduleName, logLevel);
        }

        #endregion

        #region 私有方法

        private bool ShouldLog(string moduleName, LogLevel logLevel)
        {
            var effectiveLevel = string.IsNullOrEmpty(moduleName) 
                ? _minimumLogLevel 
                : GetModuleLogLevel(moduleName);
            
            return logLevel >= effectiveLevel;
        }

        #endregion
    }

    /// <summary>
    /// 性能监控作用域
    /// </summary>
    internal class PerformanceScope : IDisposable
    {
        private readonly ILoggingService _loggingService;
        private readonly string _operationName;
        private readonly EventId _eventId;
        private readonly Stopwatch _stopwatch;

        public PerformanceScope(ILoggingService loggingService, string operationName, EventId eventId)
        {
            _loggingService = loggingService;
            _operationName = operationName;
            _eventId = eventId;
            _stopwatch = Stopwatch.StartNew();
        }

        public void Dispose()
        {
            _stopwatch.Stop();
            _loggingService.Log(LogLevel.Information, _eventId, 
                $"Performance: {_operationName} completed in {_stopwatch.ElapsedMilliseconds}ms");
        }
    }
}
