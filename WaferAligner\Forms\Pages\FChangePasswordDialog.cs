using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using Sunny.UI;
using WaferAligner.Services.UserManagement;
using WaferAligner.Services.Abstractions;
using Microsoft.Extensions.DependencyInjection;
using WaferAligner.Common;
using WaferAligner.EventIds;
using WaferAligner.Interfaces;
using WaferAligner.Services.Extensions;
namespace WaferAligner.Forms.Pages
{
    public partial class FChangePasswordDialog : UIForm
    {
        private readonly IUserManagement _userManagement;
        private readonly ILoggingService _loggingService;
        private readonly IUserContext _userContext;
        private readonly string _username;
        public bool PasswordChanged { get; private set; } = false;

        public FChangePasswordDialog(IUserManagement userManagement, ILoggingService loggingService, IUserContext userContext, string username)
        {
            InitializeComponent();
            _userManagement = userManagement ?? throw new ArgumentNullException(nameof(userManagement));
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
            _userContext = userContext ?? throw new ArgumentNullException(nameof(userContext));
            _username = username;
            InitializeForm();
        }

        // 添加新的构造函数重载，从UserContext获取IUserManagement
        public FChangePasswordDialog(ILoggingService loggingService, IUserContext userContext, string username)
        {
            InitializeComponent();
            // 从IUserContext获取IUserManagement
            _userManagement = userContext?.UserManagement ?? throw new ArgumentNullException(nameof(userContext.UserManagement));
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
            _userContext = userContext ?? throw new ArgumentNullException(nameof(userContext));
            _username = username;
            InitializeForm();
        }

        private void InitializeForm()
        {
            this.Text = "修改密码";
            btnOK.Text = "确定";
            btnCancel.Text = "取消";
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if (!ValidateInput())
                return;

            // 验证当前密码
            if (!_userManagement.Authenticate(_username, txtOldPassword.Text))
            {
                UIMessageBox.ShowWarning("当前密码不正确，请重新输入");
                txtOldPassword.Focus();
                txtOldPassword.SelectAll();
                return;
            }

            ChangePassword();
        }

        private bool ValidateInput()
        {
            // 验证旧密码
            if (string.IsNullOrWhiteSpace(txtOldPassword.Text))
            {
                UIMessageBox.ShowWarning("请输入当前密码");
                txtOldPassword.Focus();
                return false;
            }

            // 验证旧密码是否正确
            if (!_userManagement.Authenticate(_username, txtOldPassword.Text))
            {
                UIMessageBox.ShowWarning("当前密码不正确");
                txtOldPassword.Focus();
                txtOldPassword.SelectAll();
                return false;
            }

            // 验证新密码
            if (string.IsNullOrWhiteSpace(txtNewPassword.Text))
            {
                UIMessageBox.ShowWarning("请输入新密码");
                txtNewPassword.Focus();
                return false;
            }

            if (txtNewPassword.Text.Length < 6 || txtNewPassword.Text.Length > 50)
            {
                UIMessageBox.ShowWarning("新密码长度必须在6-50个字符之间");
                txtNewPassword.Focus();
                return false;
            }

            // 验证密码强度（至少包含一个字母和一个数字）
            if (!System.Text.RegularExpressions.Regex.IsMatch(txtNewPassword.Text, @"^(?=.*[A-Za-z])(?=.*\d).+$"))
            {
                UIMessageBox.ShowWarning("新密码必须包含至少一个字母和一个数字");
                txtNewPassword.Focus();
                return false;
            }

            // 验证确认密码
            if (txtNewPassword.Text != txtConfirmPassword.Text)
            {
                UIMessageBox.ShowWarning("两次输入的新密码不一致");
                txtConfirmPassword.Focus();
                return false;
            }

            // 检查新密码是否与旧密码相同
            if (txtOldPassword.Text == txtNewPassword.Text)
            {
                UIMessageBox.ShowWarning("新密码不能与当前密码相同");
                txtNewPassword.Focus();
                return false;
            }

            return true;
        }

        private void ChangePassword()
        {
            try
            {
                if (_userManagement.UpdateUserPassword(_username, txtNewPassword.Text))
                {
                    UIMessageBox.ShowSuccess("密码修改成功！");
                    PasswordChanged = true;
                    
                    // 记录日志
                    _loggingService?.LogInformation($"用户 {_username} 的密码已成功修改", WaferAligner.EventIds.EventIds.User_Self_Password_Changed);
                    
                    // 如果修改的是当前登录用户的密码，更新用户上下文
                    if (_userContext?.CurrentUser?.Username == _username)
                    {
                        var userInfo = _userManagement.GetUserInfo(_username);
                        if (userInfo != null)
                        {
                            _userContext.SetCurrentUser(userInfo);
                        }
                    }
                    
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                else
                {
                    UIMessageBox.ShowError("密码修改失败，请重试");
                    _loggingService?.LogWarning($"更新用户 {_username} 的密码失败", WaferAligner.EventIds.EventIds.Change_Password_Error);
                }
            }
            catch (Exception ex)
            {
                UIMessageBox.ShowError($"修改密码时发生错误: {ex.Message}");
                _loggingService?.LogError($"修改密码时发生异常: {ex.Message}", WaferAligner.EventIds.EventIds.Change_Password_Error);
            }
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private void txtPassword_KeyDown(object sender, KeyEventArgs e)
        {
            // 按Enter键自动跳到下一个输入框
            if (e.KeyCode == Keys.Enter)
            {
                if (sender == txtOldPassword)
                {
                    txtNewPassword.Focus();
                }
                else if (sender == txtNewPassword)
                {
                    txtConfirmPassword.Focus();
                }
                else if (sender == txtConfirmPassword)
                {
                    btnOK.PerformClick();
                }
                e.Handled = true;
            }
        }

        private void FChangePasswordDialog_Load(object sender, EventArgs e)
        {
            // 显示当前用户名
            lblCurrentUser.Text = $"用户：{_username}";
            txtOldPassword.Focus();
        }
    }
} 