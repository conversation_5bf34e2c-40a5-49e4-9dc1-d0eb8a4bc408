﻿using WaferAligner.Communication.Inovance.Abstractions;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using WaferAligner.Communication.Inovance;
using WaferAligner.Communication.Inovance.Client;
using System;
using System.Collections.Concurrent;
using System.Runtime.Serialization;
using System.Threading.Channels;
using System.Windows;
using WaferAligner;
using WaferAligner.Services.Logging.Abstractions;
using WaferAligner.Common;
using WaferAligner.Services.Logging.Extensions;
using Microsoft.Extensions.DependencyInjection;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using WaferAligner.EventIds;
using WaferAligner.Services;
using System.Threading;

namespace AlignerUI
{
    public abstract partial class GenericAxis : ObservableValidator, IDisposable, IAsyncDisposable
    {
        #region Button Setting       
        [ObservableProperty] private bool axis_ready = false;
        [ObservableProperty] private bool axis_enabled = false;
        [ObservableProperty] private bool axis_alarm = false;
        [ObservableProperty] private bool axis_homeSetDone = false;
        [ObservableProperty] private bool axis_absDone = false;
        [ObservableProperty] private bool axis_resetDone = false;
        [ObservableProperty] private bool axis_stopDone = false;

        protected Dictionary<string, Type> MonitorVariables = new();
        public Dictionary<uint, string> DataMap = new();
        public Dictionary<string, Action<object>> VariableChangeActions = new();
        protected ConcurrentDictionary<string, ConcurrentBag<Action<object>>> ExportChangeAction = new();
        private bool _disposed = false;

        public void RegistryAction(string name, Action<object> action)
        {
            if (ExportChangeAction.TryGetValue(name, out var actions))
            {
                actions?.Add(action);
            }
        }
        
        #endregion
        [ObservableProperty]
        private string axisName = string.Empty;
        protected readonly IPlcInstance plcInstance;
        private readonly ILoggingService _loggingService;
        [ObservableProperty]
        private bool isConnected = false;

        public GenericAxis(string name, string plcAddress, int port, IPlcInstance plcInstance, ILoggingService loggingService = null)
        {
            // 通过构造函数参数获取日志服务
            _loggingService = loggingService;

            AxisName = name;
            this.plcInstance = plcInstance;
            
            // 只有在服务可用时才进行连接
            if (_loggingService != null)
            {
                _ = ConnectAsync(plcAddress, port);
            }
            #region Add monitor variable         

            MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.{AxisName}IsReady", typeof(bool));
            MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.{AxisName}IsEnable", typeof(bool));
            MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.{AxisName}AxisError", typeof(bool));
            MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.{AxisName}HomeSetDone", typeof(bool));//回原点完成
            //MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.{AxisName}AbsDone", typeof(bool));//绝对定位完成
            //MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.{AxisName}ResetDone", typeof(bool));//复位完成
            //MonitorVariables.Add($"{AxisConstants.AXIS_GVL}.{AxisName}StopDone", typeof(bool));//???

            #endregion Add monitor variable

            #region Add monitor action           
            VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.{AxisName}IsReady", (obj) =>
            {
                Axis_ready = (bool)obj;
                #region

                //if (!Axis_ready)
                //{
                //    channelWriter.TryWrite(new ErrorInformation { Process = ErrorProcess.Add, ErrorCode = ErrorBase + 3, Message = $"Axis_{AxisName} 未准备好， 请复位后继续操作", RecoverySuggesstion = "工作界面点击使能" });
                //}
                //else
                //{
                //    channelWriter.TryWrite(new ErrorInformation { Process = ErrorProcess.Remove, ErrorCode = ErrorBase + 3, Message = $"Axis_{AxisName} 未准备好， 请复位后继续操作", RecoverySuggesstion = "工作界面点击使能" });
                //}
                #endregion

            });
            VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.{AxisName}IsEnable", (obj) =>
            {
                Axis_enabled = (bool)obj;
                #region
                if (!(AxisName.Equals("U") || AxisName.Equals("W") || AxisName.Equals("V") || AxisName.Equals("R")))
                {
                    //if (!Axis_enabled)
                    //{
                    //    channelWriter.TryWrite(new ErrorInformation { Process = ErrorProcess.Add, ErrorCode = ErrorBase + 1, Message = $"Axis_{AxisName} 使能未完成， 请复位后继续操作", RecoverySuggesstion = "工作界面点击使能" });
                    //}
                    //else
                    //{
                    //    channelWriter.TryWrite(new ErrorInformation { Process = ErrorProcess.Remove, ErrorCode = ErrorBase + 1, Message = $"Axis_{AxisName} 使能未完成， 请复位后继续操作", RecoverySuggesstion = "工作界面点击使能" });
                    //}
                }
                #endregion
            }
            );
            VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.{AxisName}AxisError", (obj) =>
            {
                Axis_alarm = (bool)obj;
                #region
                //if (Axis_alarm)
                //{
                //    channelWriter.TryWrite(new ErrorInformation { Process = ErrorProcess.Add, ErrorCode = ErrorBase + 2, Message = $"Axis_{AxisName} 报警， 请复位后继续操作", RecoverySuggesstion = string.Empty });
                //}
                //else
                //{
                //    channelWriter.TryWrite(new ErrorInformation { Process = ErrorProcess.Remove, ErrorCode = ErrorBase + 2, Message = $"Axis_{AxisName} 报警， 请复位后继续操作", RecoverySuggesstion = string.Empty });
                //}
                #endregion
            });
            VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.{AxisName}HomeSetDone", (obj) => axis_homeSetDone = (bool)obj);
            //VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.{AxisName}AbsDone", (obj) => axis_absDone = (bool)obj);
            //VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.{AxisName}ResetDone", (obj) => axis_resetDone = (bool)obj);
            //VariableChangeActions.Add($"{AxisConstants.AXIS_GVL}.{AxisName}StopDone", (obj) => axis_stopDone = (bool)obj);

            #endregion Add monitor action
        }

        protected virtual void AddRegistry()
        {
            // 检查PLC实例是否为null（适用于测试或模拟环境）
            if (plcInstance == null)
            {
                _loggingService?.LogWarning($"PLC实例为null，跳过变量注册 - 轴: {AxisName}", EventIds.Plc_Instance_Null_Skip_Registry);
                return;
            }
            
            var registerResult = plcInstance.RegisterMonitorVariables(MonitorVariables, new NotificationSettings(TransMode.Cyclic, 200, 0));
            if (registerResult != null)
            {
                foreach (var item in registerResult)
                {
                    DataMap.Add(item.Key, item.Value.Item1);
                }
            }
            else
            {
                _loggingService?.LogError($"PLC变量注册失败", EventIds.Plc_Variables_Registry_Failed);
            }
            plcInstance.AddNotification<InvoanceVariableChangedEventArgs>(PLCVariableChanged);
        }

        protected async void PLCVariableChanged(object? sender, InvoanceVariableChangedEventArgs e)
        {
            if (DataMap.TryGetValue(e.Handle, out var name))
            {

                try
                {
                    if (ExportChangeAction.TryGetValue(e.Name, out var action))
                    {
                        foreach (var item in action)
                        {
                            item?.Invoke(e.Value);
                        }
                    }
                }
                catch (Exception ex)
                {
                    _loggingService?.LogError($"PLC变量方法获取失败", EventIds.Plc_Export_Change_Action_Get_Failed);

                    MessageBox.Show(ex.ToString());
                }
            }
            await Task.CompletedTask;
        }


        protected async Task WritePLCVariable(string name, object value)
        {
            // 检查PLC实例是否为null（适用于测试或模拟环境）
            if (plcInstance == null)
            {
                _loggingService?.LogWarning($"PLC实例为null，跳过变量写入 - 轴: {AxisName}, 变量: {name}", EventIds.Plc_Instance_Null_Skip_Write);
                return;
            }
            
            var ret = await Task.Run(async () =>
            {
                return await plcInstance.WriteVariableAsync(new PLCVarWriteInfo { Name = name, Value = value }, cancle: CancellationToken.None);
            });

#if DEBUG
            if (!ret)
            {
                _loggingService?.LogError($"PLC变量:{name}:{value.ToString()}写入失败", EventIds.Plc_Variables_Write_Failed);

                //await errorSource.WriteAsync(new ErrorInformation { ErrorCode = 0, Message = $"写入{{{name}}} : {{{value}}} 时发生错误", RecoverySuggesstion = string.Empty });
            }
#endif
        }

        protected async Task ReadPLCVariable(PLCVarReadInfo ReadInfoJogVel)
        {
            // 检查PLC实例是否为null（适用于测试或模拟环境）
            if (plcInstance == null)
            {
                _loggingService?.LogWarning($"PLC实例为null，跳过变量读取 - 轴: {AxisName}, 变量: {ReadInfoJogVel?.Name}", EventIds.Plc_Instance_Null_Skip_Read);
                return;
            }
            
            var ret = await Task.Run(async () =>
            {
                return await plcInstance.ReadVariableAsync(ReadInfoJogVel, CancellationToken.None);
            });

        }

        public async Task<bool> ConnectAsync(string plcAddress, int port)
        {
            bool result = await Task.Run(() =>
            {
                try
                {
                    // 检查PLC实例是否为null（适用于测试或模拟环境）
                    if (plcInstance == null)
                    {
                        _loggingService?.LogWarning($"PLC实例为null，跳过连接 - 轴: {AxisName}", EventIds.Plc_Instance_Null);
                        return false;
                    }
                    
                    plcInstance.Connect(plcAddress, port);
                    return true;
                }
                catch (Exception ex)
                {
                    _loggingService?.LogError($"PLC连接失败: {ex.Message}", EventIds.Plc_Connection_Failed);
                    return false;
                }
            });
            IsConnected = result;
            return result;
        }

        /// <summary>
        /// 实现IDisposable接口，释放资源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }
        
        /// <summary>
        /// 释放资源的核心方法
        /// </summary>
        /// <param name="disposing">是否主动释放（true）或者由终结器释放（false）</param>
        protected virtual void Dispose(bool disposing)
        {
            if (_disposed) return;
            
            _disposed = true;
            
            if (disposing)
            {
                try
                {
                    // 清理受控资源
                    
                    // 清理所有变量监控
                    if (plcInstance != null)
                    {
                        // 移除通知的API在IPlcInstance中不存在，直接跳过
                        // plcInstance.RemoveNotification<InvoanceVariableChangedEventArgs>(PLCVariableChanged);
                        
                        // 如果plcInstance实现了IDisposable，则释放它
                        if (plcInstance is IDisposable disposablePlc)
                        {
                            disposablePlc.Dispose();
                        }
                    }
                    
                    // 清空集合
                    MonitorVariables.Clear();
                    DataMap.Clear();
                    VariableChangeActions.Clear();
                    ExportChangeAction.Clear();
                    
                    _loggingService?.LogInformation($"{AxisName}轴基类资源已释放", EventIds.Resource_Released);
                }
                catch (Exception ex)
                {
                    _loggingService?.LogError(ex, $"{AxisName}轴基类资源释放失败", EventIds.Resource_Released);
                }
            }
        }
        
        /// <summary>
        /// 实现IAsyncDisposable接口，异步释放资源
        /// </summary>
        public async ValueTask DisposeAsync()
        {
            await DisposeAsyncCore();
            
            // 调用同步Dispose以清理其他资源
            Dispose(false);
            
            GC.SuppressFinalize(this);
        }
        
        /// <summary>
        /// 异步释放资源的核心方法
        /// </summary>
        protected virtual async ValueTask DisposeAsyncCore()
        {
            if (_disposed) return;
            
            try
            {
                // 这里可以执行异步清理，比如等待任何进行中的操作完成
                await Task.CompletedTask;
                
                _loggingService?.LogInformation($"{AxisName}轴基类资源已异步释放", EventIds.Resource_Released);
            }
            catch (Exception ex)
            {
                _loggingService?.LogError(ex, $"{AxisName}轴基类资源异步释放失败", EventIds.Resource_Released);
            }
        }
    }
}
