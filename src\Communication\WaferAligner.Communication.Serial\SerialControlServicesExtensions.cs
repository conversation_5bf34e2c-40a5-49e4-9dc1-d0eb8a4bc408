using WaferAligner.Services.Logging.Abstractions;
using Microsoft.Extensions.DependencyInjection;
using System;
using WaferAligner.Infrastructure.Common;
using WaferAligner.Communication.Serial.Implementation;
using WaferAligner.Communication.Serial.Interfaces;

namespace WaferAligner.Communication.Serial
{
    /// <summary>
    /// 串口通信服务扩展
    /// </summary>
    public static class SerialCommunicationServicesExtensions
    {
        /// <summary>
        /// 注册串口通信相关服务
        /// </summary>
        /// <param name="services">服务集合</param>
        /// <returns>服务集合</returns>
        public static IServiceCollection AddSerialCommunicationServices(this IServiceCollection services)
        {
            if (services == null)
            {
                throw new ArgumentNullException(nameof(services));
            }
            
            // 注册SerialComWrapper为单例
            services.AddSingleton<ISerialComWrapper, SerialComWrapper>();
            
            // 注册串口连接管理器为单例
            services.AddSingleton<ISerialConnectionManager, SerialConnectionManager>();
            
            // 注册轴控制器工厂为单例
            services.AddSingleton<ISerialAxisControllerFactory, SerialAxisControllerFactory>();
            
            // 注册ResourceManager（如果尚未注册）
            if (services.BuildServiceProvider().GetService<WaferAligner.Infrastructure.Common.ResourceManager>() == null)
            {
                services.AddSingleton<WaferAligner.Infrastructure.Common.ResourceManager>();
            }
            
            return services;
        }
    }
} 