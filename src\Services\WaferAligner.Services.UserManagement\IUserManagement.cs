namespace WaferAligner.Services.UserManagement
{
    public interface IUserManagement
    {
        /// <summary>
        /// 验证用户登录凭证
        /// </summary>
        bool Authenticate(string username, string password);
        
        /// <summary>
        /// 获取用户信息
        /// </summary>
        UserInfo? GetUserInfo(string username);
        
        /// <summary>
        /// 检查用户权限
        /// </summary>
        bool CheckPermission(string username, string permission);
        
        /// <summary>
        /// 创建新用户
        /// </summary>
        bool CreateUser(UserInfo userInfo, string initialPassword);
        
        /// <summary>
        /// 更新用户信息
        /// </summary>
        bool UpdateUser(UserInfo userInfo);
        
        /// <summary>
        /// 更新用户密码
        /// </summary>
        bool UpdateUserPassword(string username, string newPassword);
        
        /// <summary>
        /// 删除用户
        /// </summary>
        bool DeleteUser(string username);
        
        /// <summary>
        /// 获取所有用户列表
        /// </summary>
        IEnumerable<UserInfo> GetAllUsers();
        
        /// <summary>
        /// 获取所有角色列表
        /// </summary>
        IEnumerable<Role> GetAllRoles();
        
        /// <summary>
        /// 获取所有权限列表
        /// </summary>
        IEnumerable<Permission> GetAllPermissions();
        
        /// <summary>
        /// 创建角色
        /// </summary>
        bool CreateRole(Role role);
        
        /// <summary>
        /// 更新角色
        /// </summary>
        bool UpdateRole(Role role);
        
        /// <summary>
        /// 删除角色
        /// </summary>
        bool DeleteRole(string roleName);
    }
}
