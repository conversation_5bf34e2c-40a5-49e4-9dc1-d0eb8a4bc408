using System.Text;
using System.Runtime.InteropServices;
using WaferAligner.Services.Logging.Abstractions;
using WaferAligner.Services.UserManagement;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Sunny.UI;
using Sunny.UI.Demo;
using WaferAligner.Common;
using WaferAligner.Forms.Pages;
using WaferAligner.Infrastructure.Common;

namespace WaferAligner
{
    internal static class Program
    {
        /// <summary>
        ///  The main entry point for the application.
        /// </summary>
        [STAThread]
        static void Main()
        {
            try
            {
                CommonFun.host = new HostBuilder().ConfigureServices((context, services) =>
                {
                    ConfigureServices(services);
                }).Build();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"依赖注入配置失败:\n{ex.Message}\n\n{ex.StackTrace}", "启动错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            try
            {
                CommonFun.host.Start();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Host服务启动失败:\n{ex.Message}\n\n{ex.StackTrace}", "启动错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            //设置应用程序未处理异常方式为ThreadException
            Application.SetUnhandledExceptionMode(UnhandledExceptionMode.CatchException);
            //设置UI线程异常
            Application.ThreadException += new System.Threading.ThreadExceptionEventHandler(Application_ThreadException);
            //设置非UI线程异常
            AppDomain.CurrentDomain.UnhandledException += new UnhandledExceptionEventHandler(CurrentDomain_UnhandledException);
            // To customize application configuration such as set high DPI settings or default font,
            // see https://aka.ms/applicationconfiguration.
            ApplicationConfiguration.Initialize();
            
            // 显示登录界面
            try
            {
                var loginServiceProvider = CommonFun.host?.Services;
                var userManagement = loginServiceProvider.GetRequiredService<WaferAligner.Services.UserManagement.IUserManagement>();
                var loggingService = loginServiceProvider.GetRequiredService<WaferAligner.Services.Logging.Abstractions.ILoggingService>();
                var userContext = loginServiceProvider.GetRequiredService<WaferAligner.Interfaces.IUserContext>();
                var statusUpdateService = loginServiceProvider.GetRequiredService<WaferAligner.Services.IStatusUpdateService>();
                using (var loginForm = new FLoginPage(userManagement, loggingService, userContext, statusUpdateService))
                {
                    var result = loginForm.ShowDialog();

                    if (result == DialogResult.OK)
                    {
                        // 登录成功，启动主窗体
                        try
                        {
                            // 获取FHeaderMainFooter所需的所有服务
                            var mainServiceProvider = CommonFun.host?.Services;
                            var mainLoggingService = mainServiceProvider.GetRequiredService<ILoggingService>();
                            var resourceManager = mainServiceProvider.GetRequiredService<ResourceManager>();
                            var plcConnectionManager = mainServiceProvider.GetRequiredService<WaferAligner.Communication.Inovance.Abstractions.IPlcConnectionManager>();
                            var mainWindowViewModel = mainServiceProvider.GetRequiredService<WaferAligner.Interfaces.IMainWindowViewModel>();
                            var axisFactory = mainServiceProvider.GetRequiredService<WaferAligner.Interfaces.IAxisViewModelFactory>();
                            var mainUserContext = mainServiceProvider.GetRequiredService<WaferAligner.Interfaces.IUserContext>();
                            var performanceMonitor = mainServiceProvider.GetService<PerformanceMonitor>(); // 可以为null
                            var mainStatusUpdateService = mainServiceProvider.GetRequiredService<WaferAligner.Services.IStatusUpdateService>();
                            
                            var mainForm = new FHeaderMainFooter(
                                mainLoggingService,
                                resourceManager,
                                plcConnectionManager,
                                mainWindowViewModel,
                                axisFactory,
                                mainUserContext,
                                performanceMonitor,
                                mainStatusUpdateService);
                            Application.Run(mainForm);
                        }
                        catch (Exception ex)
                        {
                            var errorMessage = $"启动主窗体时发生错误:\n\n{ex.Message}\n\n详细信息:\n{ex.StackTrace}";
                            MessageBox.Show(errorMessage, "启动错误", MessageBoxButtons.OK, MessageBoxIcon.Error);

                            // 尝试显示服务配置信息以帮助诊断
                            try
                            {
                                var serviceProvider = CommonFun.host?.Services;
                                if (serviceProvider != null)
                                {
                                    var diagnosticInfo = "已注册的服务:\n";

                                    // 检查关键服务是否注册
                                    var keyServices = new[]
                                    {
                                    typeof(WaferAligner.Services.Abstractions.ILoggingService),
                                    typeof(ResourceManager),
                                    typeof(WaferAligner.Communication.Inovance.Abstractions.IPlcConnectionManager),
                                    typeof(WaferAligner.Interfaces.IAxisViewModelFactory),
                                    // typeof(WaferAligner.Migration.Phase2MigrationHelper) // 已废弃
                                };

                                    foreach (var serviceType in keyServices)
                                    {
                                        try
                                        {
                                            var service = serviceProvider.GetService(serviceType);
                                            diagnosticInfo += $"✓ {serviceType.Name}: {(service != null ? "已注册" : "未注册")}\n";
                                        }
                                        catch (Exception serviceEx)
                                        {
                                            diagnosticInfo += $"✗ {serviceType.Name}: 获取失败 - {serviceEx.Message}\n";
                                        }
                                    }

                                    MessageBox.Show(diagnosticInfo, "服务诊断信息", MessageBoxButtons.OK, MessageBoxIcon.Information);
                                }
                            }
                            catch (Exception diagEx)
                            {
                                // 忽略诊断过程中的错误
                            }
                        }
                    }
                    else
                    {
                        // 登录失败或取消，退出程序
                        // 停止后台服务
                        try
                        {
                            CommonFun.host?.StopAsync().Wait(2000);
                            CommonFun.host?.Dispose();
                        }
                        catch (Exception ex)
                        {
                            // 忽略停止服务时的错误
                        }

                        Application.Exit();
                        Environment.Exit(0); // 强制退出
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"创建登录界面失败:\n{ex.Message}\n\n{ex.StackTrace}", "启动错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        

        static void Application_ThreadException(object sender, System.Threading.ThreadExceptionEventArgs e)
        {
            string str = GetExceptionMsg(e.Exception, e.ToString());
            MessageBox.Show(str, "系统错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            //LogManager.WriteLog(str);

        }
        static void CurrentDomain_UnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            string str = GetExceptionMsg(e.ExceptionObject as Exception, e.ToString());
            MessageBox.Show(str, "系统错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            //LogManager.WriteLog(str);
        }
        /// <summary>
        /// 生成自定义异常信息
        /// </summary>
        /// <param name="ex">异常对象</param>
        /// <param name="backStr">备用异常信息：当ex为null时有效</param>
        /// <returns>异常字符串文本</returns>
        static string GetExceptionMsg(Exception ex, string backStr)
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendLine("****************************异常文本****************************");
            sb.AppendLine("【出现时间】：" + DateTime.Now.ToString());
            if (ex != null)
            {
                sb.AppendLine("【异常类型】：" + ex.GetType().Name);
                sb.AppendLine("【异常信息】：" + ex.Message);
                sb.AppendLine("【堆栈调用】：" + ex.StackTrace);
            }
            else
            {
                sb.AppendLine("【未处理异常】：" + backStr);
            }
            sb.AppendLine("***************************************************************");
            return sb.ToString();
        }
        private static void ConfigureServices(IServiceCollection services)
        {
            // 使用统一的服务配置管理器
            services.ConfigureAllServices();
            
            #if DEBUG
            services.ConfigureDevelopmentServices();
            #else
            services.ConfigureProductionServices();
            #endif
            
            // 验证服务配置
            try
            {
                var serviceProvider = services.BuildServiceProvider();
                ServiceConfiguration.ValidateServiceConfiguration(serviceProvider);
            }
            catch (Exception ex)
            {
                throw;
            }
        }
    }
}