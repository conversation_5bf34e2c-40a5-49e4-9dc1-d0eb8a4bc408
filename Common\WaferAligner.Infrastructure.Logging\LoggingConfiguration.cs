using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;

namespace WaferAligner.Infrastructure.Logging
{
    /// <summary>
    /// 日志配置类
    /// </summary>
    public class LoggingConfiguration
    {
        /// <summary>
        /// 全局最低日志级别
        /// </summary>
        public LogLevel MinimumLevel { get; set; } = LogLevel.Information;

        /// <summary>
        /// 模块特定的日志级别
        /// </summary>
        public Dictionary<string, LogLevel> ModuleLevels { get; set; } = new Dictionary<string, LogLevel>();

        /// <summary>
        /// 是否启用文件日志
        /// </summary>
        public bool EnableFileLogging { get; set; } = true;

        /// <summary>
        /// 是否启用控制台日志
        /// </summary>
        public bool EnableConsoleLogging { get; set; } = true;

        /// <summary>
        /// 是否启用事件日志
        /// </summary>
        public bool EnableEventLogging { get; set; } = false;

        /// <summary>
        /// 日志文件路径
        /// </summary>
        public string LogFilePath { get; set; } = "logs/wafer-aligner.log";

        /// <summary>
        /// 日志文件最大大小（MB）
        /// </summary>
        public int MaxFileSizeMB { get; set; } = 100;

        /// <summary>
        /// 保留的日志文件数量
        /// </summary>
        public int RetainedFileCount { get; set; } = 10;

        /// <summary>
        /// 日志格式模板
        /// </summary>
        public string LogTemplate { get; set; } = "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff}] [{Level:u3}] [{Module}] {Message}{NewLine}{Exception}";

        /// <summary>
        /// 是否启用结构化日志
        /// </summary>
        public bool EnableStructuredLogging { get; set; } = true;

        /// <summary>
        /// 是否启用性能监控
        /// </summary>
        public bool EnablePerformanceMonitoring { get; set; } = true;

        /// <summary>
        /// 性能监控阈值（毫秒）
        /// </summary>
        public int PerformanceThresholdMs { get; set; } = 1000;

        /// <summary>
        /// 获取指定模块的日志级别
        /// </summary>
        /// <param name="moduleName">模块名称</param>
        /// <returns>日志级别</returns>
        public LogLevel GetModuleLevel(string moduleName)
        {
            if (string.IsNullOrEmpty(moduleName))
                return MinimumLevel;

            return ModuleLevels.TryGetValue(moduleName, out var level) ? level : MinimumLevel;
        }

        /// <summary>
        /// 设置模块日志级别
        /// </summary>
        /// <param name="moduleName">模块名称</param>
        /// <param name="level">日志级别</param>
        public void SetModuleLevel(string moduleName, LogLevel level)
        {
            if (string.IsNullOrEmpty(moduleName))
                return;

            ModuleLevels[moduleName] = level;
        }

        /// <summary>
        /// 检查指定模块和级别是否启用日志记录
        /// </summary>
        /// <param name="moduleName">模块名称</param>
        /// <param name="logLevel">日志级别</param>
        /// <returns>是否启用</returns>
        public bool IsEnabled(string moduleName, LogLevel logLevel)
        {
            var moduleLevel = GetModuleLevel(moduleName);
            return logLevel >= moduleLevel;
        }

        /// <summary>
        /// 创建默认配置
        /// </summary>
        /// <returns>默认日志配置</returns>
        public static LoggingConfiguration CreateDefault()
        {
            return new LoggingConfiguration
            {
                MinimumLevel = LogLevel.Information,
                EnableFileLogging = true,
                EnableConsoleLogging = true,
                EnableEventLogging = false,
                LogFilePath = "logs/wafer-aligner.log",
                MaxFileSizeMB = 100,
                RetainedFileCount = 10,
                EnableStructuredLogging = true,
                EnablePerformanceMonitoring = true,
                PerformanceThresholdMs = 1000
            };
        }
    }
}
