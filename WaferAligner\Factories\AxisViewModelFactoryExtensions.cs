using System;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using WaferAligner.Interfaces;
using WaferAligner.Models;
using WaferAligner.Services;
using WaferAligner.Communication.Inovance.Abstractions;
using WaferAligner.Services.Abstractions;
using WaferAligner.Services.Extensions;
using WaferAligner.EventIds;

namespace WaferAligner.Factories
{
    /// <summary>
    /// 轴视图模型工厂扩展类，提供创建新实现的PLC轴的方法
    /// </summary>
    public static class AxisViewModelFactoryExtensions
    {
        /// <summary>
        /// 创建基于PlcAxisViewModelBase的Z轴视图模型
        /// </summary>
        public static async Task<IZAxisViewModel> CreatePlcBasedZAxisAsync(
            this IAxisViewModelFactory factory,
            IServiceProvider serviceProvider)
        {
            if (factory == null)
                throw new ArgumentNullException(nameof(factory));
            
            if (serviceProvider == null)
                throw new ArgumentNullException(nameof(serviceProvider));
                
            try
            {
                // 获取必要的服务
                var loggingService = serviceProvider.GetRequiredService<ILoggingService>();
                var plcConnectionManager = serviceProvider.GetRequiredService<IPlcConnectionManager>();
                var axisEventService = serviceProvider.GetRequiredService<IAxisEventService>();
                
                // 创建PLC通信
                var plcCommunication = serviceProvider.GetService<IPlcCommunication>() 
                    ?? new PlcCommunication(plcConnectionManager, loggingService);
                
                // 创建新的Z轴视图模型
                var zAxisViewModel = new ZAxisViewModelNew(plcCommunication, loggingService, axisEventService);
                
                // 初始化
                await zAxisViewModel.InitializeAsync();
                
                return zAxisViewModel;
            }
            catch (Exception ex)
            {
                var loggingService = serviceProvider.GetService<ILoggingService>();
                loggingService?.LogError(ex, "创建基于PLC的Z轴视图模型失败", WaferAligner.EventIds.EventIds.Axis_Initialize_Failed);
                throw;
            }
        }
        
        /// <summary>
        /// 创建基于PlcAxisViewModelBase的相机轴视图模型
        /// </summary>
        public static async Task<ICameraAxisViewModel> CreatePlcBasedCameraAxisAsync(
            this IAxisViewModelFactory factory,
            string axisName,
            string cameraPosition,
            IServiceProvider serviceProvider)
        {
            if (factory == null)
                throw new ArgumentNullException(nameof(factory));
                
            if (string.IsNullOrEmpty(axisName))
                throw new ArgumentNullException(nameof(axisName));
                
            if (string.IsNullOrEmpty(cameraPosition))
                throw new ArgumentNullException(nameof(cameraPosition));
                
            if (serviceProvider == null)
                throw new ArgumentNullException(nameof(serviceProvider));
                
            try
            {
                // 获取必要的服务
                var loggingService = serviceProvider.GetRequiredService<ILoggingService>();
                var plcConnectionManager = serviceProvider.GetRequiredService<IPlcConnectionManager>();
                var axisEventService = serviceProvider.GetRequiredService<IAxisEventService>();
                
                // 创建PLC通信
                var plcCommunication = serviceProvider.GetService<IPlcCommunication>() 
                    ?? new PlcCommunication(plcConnectionManager, loggingService);
                
                // 创建新的相机轴视图模型
                var cameraAxisViewModel = new CameraAxisViewModelNew(
                    axisName,
                    cameraPosition,
                    plcCommunication,
                    loggingService, 
                    axisEventService);
                
                // 初始化
                await cameraAxisViewModel.InitializeAsync();
                
                return cameraAxisViewModel;
            }
            catch (Exception ex)
            {
                var loggingService = serviceProvider.GetService<ILoggingService>();
                loggingService?.LogError(ex, $"创建基于PLC的相机轴视图模型失败: {axisName} ({cameraPosition})", WaferAligner.EventIds.EventIds.Axis_Initialize_Failed);
                throw;
            }
        }
        
        /// <summary>
        /// 创建左相机X轴视图模型
        /// </summary>
        public static async Task<ICameraAxisViewModel> CreatePlcBasedLeftXAxisAsync(
            this IAxisViewModelFactory factory,
            IServiceProvider serviceProvider)
        {
            return await factory.CreatePlcBasedCameraAxisAsync("LX", "Left", serviceProvider);
        }
        
        /// <summary>
        /// 创建左相机Y轴视图模型
        /// </summary>
        public static async Task<ICameraAxisViewModel> CreatePlcBasedLeftYAxisAsync(
            this IAxisViewModelFactory factory,
            IServiceProvider serviceProvider)
        {
            return await factory.CreatePlcBasedCameraAxisAsync("LY", "Left", serviceProvider);
        }
        
        /// <summary>
        /// 创建左相机Z轴视图模型
        /// </summary>
        public static async Task<ICameraAxisViewModel> CreatePlcBasedLeftZAxisAsync(
            this IAxisViewModelFactory factory,
            IServiceProvider serviceProvider)
        {
            return await factory.CreatePlcBasedCameraAxisAsync("LZ", "Left", serviceProvider);
        }
        
        /// <summary>
        /// 创建右相机X轴视图模型
        /// </summary>
        public static async Task<ICameraAxisViewModel> CreatePlcBasedRightXAxisAsync(
            this IAxisViewModelFactory factory,
            IServiceProvider serviceProvider)
        {
            return await factory.CreatePlcBasedCameraAxisAsync("RX", "Right", serviceProvider);
        }
        
        /// <summary>
        /// 创建右相机Y轴视图模型
        /// </summary>
        public static async Task<ICameraAxisViewModel> CreatePlcBasedRightYAxisAsync(
            this IAxisViewModelFactory factory,
            IServiceProvider serviceProvider)
        {
            return await factory.CreatePlcBasedCameraAxisAsync("RY", "Right", serviceProvider);
        }
        
        /// <summary>
        /// 创建右相机Z轴视图模型
        /// </summary>
        public static async Task<ICameraAxisViewModel> CreatePlcBasedRightZAxisAsync(
            this IAxisViewModelFactory factory,
            IServiceProvider serviceProvider)
        {
            return await factory.CreatePlcBasedCameraAxisAsync("RZ", "Right", serviceProvider);
        }
    }
} 