using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace WaferAligner.Core.Business
{
    public class LogObserver<T> : IObserver<LogEntry<T>>
    {
        public IDisposable unsubscriber;
        private string instName;

        private Action<LogEntry<T>> _onNext;
        private Action<Exception> _onError;

        public virtual void Subscribe(
            IObservable<LogEntry<T>> provider,
            Action<LogEntry<T>> onNext, 
            Action<Exception> onError)
        {
            if (provider != null) 
                unsubscriber = provider.Subscribe(this);
         
            if (onNext != null) _onNext = onNext;
            if (onError != null) _onError = onError;

        }
        public void OnCompleted()
        {
            this.UnSubscribe();
        }

        public void OnError(Exception error)
        {
            _onError?.Invoke(error);
        }

        public void OnNext(LogEntry<T> value)
        {
            _onNext?.Invoke(value);
        }

        public virtual void UnSubscribe()
        {
            unsubscriber?.Dispose();
        }
    }
}
