using System;
using WaferAligner.Services.Logging.Abstractions;
using WaferAligner.Services.UserManagement;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using WaferAligner.Infrastructure.Common;
using WaferAligner.Interfaces;
using WaferAligner.Services;
using WaferAligner.Factories;
using WaferAligner.Models;
using WaferAligner.Services.Logging.Extensions;
using System.Windows.Forms;
using WaferAligner.EventIds;
using WaferAligner.Communication.Inovance;
using WaferAligner.Communication.Serial;
using WaferAligner.Communication.Inovance.Abstractions;
using WaferAligner.Communication.Inovance.Management;
using WaferAligner.PLCControl;
using WaferAligner.Services.Logging.Implementation;
using WaferAligner.Infrastructure.Common;

namespace WaferAligner.Common
{
    /// <summary>
    /// 服务配置管理器
    /// 负责集中配置和管理所有服务的依赖注入
    /// </summary>
    public static class ServiceConfiguration
    {
        /// <summary>
        /// 配置所有服务
        /// </summary>
        /// <param name="services">服务集合</param>
        public static IServiceCollection ConfigureAllServices(this IServiceCollection services)
        {
            // 配置核心服务
            services.ConfigureCoreServices();
            
            // 配置基础设施服务
            services.ConfigureInfrastructureServices();
            
            // 配置业务服务
            services.ConfigureBusinessServices();
            
            // 配置PLC服务
            services.ConfigurePLCServices();
            
            // 配置串口服务 - 新增，与PLC分开
            services.ConfigureSerialServices();
            
            // 配置轴控制服务
            services.ConfigureAxisServices();
            
            // 配置UI服务
            services.ConfigureUIServices();

            // 注册后立即测试GetService
            var testProvider = services.BuildServiceProvider();
            var testAxisFactory = testProvider.GetService<WaferAligner.Interfaces.IAxisViewModelFactory>();
            var testAxisEventService = testProvider.GetService<WaferAligner.Services.IAxisEventService>();
            var testPlcVariableService = testProvider.GetService<WaferAligner.Communication.Inovance.Abstractions.IPlcVariableService>();

            return services;
        }

        /// <summary>
        /// 配置核心服务
        /// </summary>
        private static IServiceCollection ConfigureCoreServices(this IServiceCollection services)
        {
            // 移除微软日志框架直接注册
            // services.AddLogging(builder =>
            // {
            //     builder.AddConsole();
            //     builder.AddDebug();
            //     builder.SetMinimumLevel(LogLevel.Information);
            // });
            
            // 仅保留自定义日志服务 - 支持配置注入
            services.AddSingleton<WaferAligner.Services.Logging.Configuration.LoggingConfiguration>(provider =>
            {
                // 根据环境创建不同的日志配置
                bool isDevelopment = WaferAligner.Common.DevelopmentModeHelper.IsDevelopmentMode();
                return isDevelopment
                    ? WaferAligner.Services.Logging.Configuration.LoggingConfiguration.CreateDevelopmentConfiguration()
                    : WaferAligner.Services.Logging.Configuration.LoggingConfiguration.CreateProductionConfiguration();
            });
            services.AddSingleton<ILoggingService, WaferAligner.Services.Logging.Implementation.LoggingService>();
            
            // 资源管理器
            services.AddSingleton<ResourceManager>();

            // 性能监控服务
            services.AddSingleton<PerformanceMonitor>();
            
            return services;
        }

        /// <summary>
        /// 配置基础设施服务
        /// </summary>
        private static IServiceCollection ConfigureInfrastructureServices(this IServiceCollection services)
        {
            // 配置服务
            services.AddSingleton<IConfig, WaferAligner.Services.Logging.Configuration.JsonFileConfiguration>();
            
            // 用户管理服务
            services.AddSingleton<IUserManagement, UserManagementService>();
            services.AddSingleton<JsonStorageService>();
            
            // 新增：用户上下文服务
            services.AddSingleton<IUserContext, UserContextService>();
            
            // 注意：IFileStorageService 属于 Service.Record 项目，在 WaferAligner 中未引用和使用
            
            return services;
        }

        /// <summary>
        /// 配置业务服务
        /// </summary>
        private static IServiceCollection ConfigureBusinessServices(this IServiceCollection services)
        {
            // 注意：旧的 IAccountService 和 IUserRepository 已被弃用
            // 现在使用 IUserManagement 统一管理用户相关功能
            
            // 命令处理服务：这些服务属于Service.Recipe项目，在WaferAligner中暂未使用
            // 如果需要，应在具体使用的项目中进行注册
            
            return services;
        }

        /// <summary>
        /// 配置PLC相关服务
        /// </summary>
        private static IServiceCollection ConfigurePLCServices(this IServiceCollection services)
        {
            // 使用专门的PLC服务扩展类
            services.AddPLCControlServices();

            return services;
        }

        /// <summary>
        /// 配置串口相关服务
        /// </summary>
        private static IServiceCollection ConfigureSerialServices(this IServiceCollection services)
        {
            // 注册串口通信服务
            services.AddSerialCommunicationServices();

            return services;
        }

        /// <summary>
        /// 配置轴控制服务
        /// </summary>
        private static IServiceCollection ConfigureAxisServices(this IServiceCollection services)
        {
            // 新增：轴ViewModel工厂
            services.AddSingleton<IAxisViewModelFactory, AxisViewModelFactory>();

            // 新增：主窗体ViewModel（通过工厂创建）
            services.AddSingleton<IMainWindowViewModel>(provider =>
            {
                var plcManager = provider.GetRequiredService<IPlcConnectionManager>();
                var loggingService = provider.GetRequiredService<ILoggingService>();
                return new MainWindowViewModelService(plcManager, loggingService, provider);
            });

            // 新增：轴视图模型实现类
            services.AddTransient<CameraAxisViewModelNew>();
            services.AddTransient<ZAxisViewModelNew>();

            // 新增：适配器类（作为Transient，每次创建新实例）
            // ZAxisViewModelAdapter已被移除，直接使用ZAxisViewModel
            // services.AddTransient<ZAxisViewModelAdapter>();
            // XyrAxisViewModelAdapter已被移除，直接使用SerialAxisViewModel
            // CameraAxisViewModelAdapter已被移除，直接使用CameralHoldAxisViewModel
            // services.AddTransient<CameraAxisViewModelAdapter>();

            // 兼容性服务已禁用（2025年5月完成兼容层迁移）
            // 保留注册代码以便于历史回溯
            // services.AddSingleton<ConstValueCompatibilityService>();

            // 新增：AlignerPara服务（替代ConstValue.ALIGNERPARA）
            services.AddSingleton<IAlignerParaService, AlignerParaService>();

            // 新增：配方服务（替代ConstValue.ALIGNERPARA）
            services.AddSingleton<IRecipeService, RecipeService>();

            // 新增：气缸控制服务（替代直接操作ConstValue中的气缸相关方法）
            services.AddSingleton<WaferAligner.Services.ICylinderService, WaferAligner.Services.CylinderService>();

            // 新增：迁移助手服务（Phase 2专用）
            // 删除Phase2MigrationHelper相关注册
            // services.AddScoped<Phase2MigrationHelper>();

            // 注册重构后的服务
            services.AddSingleton<IAxisEventService, AxisEventService>();
            services.AddSingleton<IPlcVariableService, PlcVariableService>();
            services.AddSingleton<IUIUpdateService, UIUpdateService>();

            // 新增：数据模型
            services.AddTransient<AxisViewModelCollection>();

            return services;
        }

        /// <summary>
        /// 配置UI相关服务
        /// </summary>
        private static IServiceCollection ConfigureUIServices(this IServiceCollection services)
        {
            // UIThreadManager是静态类，提供扩展方法，无需注册为服务
            
            // 注册状态更新服务
            services.AddSingleton<IStatusUpdateService, StatusUpdateService>();
            
            return services;
        }

        /// <summary>
        /// 配置开发环境特定服务
        /// </summary>
        public static IServiceCollection ConfigureDevelopmentServices(this IServiceCollection services)
        {
            // 移除对LogLevel的直接使用
            // services.AddLogging(builder =>
            // {
            //     builder.SetMinimumLevel(LogLevel.Debug);
            // });
            
            // 开发环境日志配置已通过LoggingConfiguration自动设置
            
            return services;
        }

        /// <summary>
        /// 配置生产环境特定服务
        /// </summary>
        public static IServiceCollection ConfigureProductionServices(this IServiceCollection services)
        {
            // 移除对LogLevel的直接使用
            // services.AddLogging(builder =>
            // {
            //     builder.SetMinimumLevel(LogLevel.Warning);
            // });
            
            // 生产环境日志配置已通过LoggingConfiguration自动设置
            
            return services;
        }

        /// <summary>
        /// 验证服务配置
        /// </summary>
        public static void ValidateServiceConfiguration(IServiceProvider serviceProvider)
        {
            try
            {
                // 临时添加：跳过验证的选项
                var skipValidation = Environment.GetEnvironmentVariable("SKIP_SERVICE_VALIDATION");
                if (!string.IsNullOrEmpty(skipValidation))
                {
                    Console.WriteLine("跳过服务配置验证（由环境变量控制）");
                    return;
                }

                // 验证关键服务是否正确注册
                var loggingService = serviceProvider.GetRequiredService<ILoggingService>();
                var userManagement = serviceProvider.GetRequiredService<IUserManagement>();
                var resourceManager = serviceProvider.GetRequiredService<ResourceManager>();
                
                // 验证Phase 2新增服务
                var plcManager = serviceProvider.GetRequiredService<IPlcConnectionManager>();
                var axisFactory = serviceProvider.GetRequiredService<IAxisViewModelFactory>();
                var mainWindowViewModel = serviceProvider.GetRequiredService<IMainWindowViewModel>();
                // 兼容层服务已禁用（2025年5月完成兼容层迁移）
                // var compatibilityService = serviceProvider.GetRequiredService<ConstValueCompatibilityService>();
                var recipeService = serviceProvider.GetRequiredService<IRecipeService>();
                
                // 验证Phase 3新增服务
                var cylinderService = serviceProvider.GetRequiredService<WaferAligner.Services.ICylinderService>();
                
                // 将RecipeService初始化（可能导入旧配置）
                try
                {
                    recipeService.UpdatePlcParametersAsync().Wait();
                    loggingService.LogInformation("成功初始化配方服务", WaferAligner.EventIds.EventIds.Configuration_Loaded);
                }
                catch (Exception ex)
                {
                    loggingService.LogWarning($"配方服务初始化时出现警告：{ex.Message}", WaferAligner.EventIds.EventIds.Configuration_Error);
                }
                
                // 使用ServiceLifetimeValidator验证服务生命周期
                ServiceLifetimeValidator.ValidateServiceLifetimes(serviceProvider);
                
                loggingService.LogInformation("服务配置验证成功（包含Phase 3服务）", WaferAligner.EventIds.EventIds.Configuration_Loaded);
            }
            catch (Exception ex)
            {
                // 临时修改：不抛出异常，而是显示警告并继续
                var errorMessage = $"服务配置验证失败: {ex.Message}";
                Console.WriteLine(errorMessage);
                MessageBox.Show($"{errorMessage}\n\n程序将尝试继续运行，但某些功能可能不可用。", 
                    "服务配置验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }
    }

    /// <summary>
    /// 服务配置选项
    /// </summary>
    public class ServiceConfigurationOptions
    {
        /// <summary>
        /// 是否启用开发模式
        /// 自动检测环境：环境变量 > App.config > 默认值
        /// </summary>
        public bool IsDevelopmentMode { get; set; } = GetDevelopmentMode();

        /// <summary>
        /// 自动检测开发模式
        /// </summary>
        private static bool GetDevelopmentMode()
        {
            // 1. 优先检查环境变量
            string envValue = Environment.GetEnvironmentVariable("WAFER_ALIGNER_DEV_MODE");
            if (!string.IsNullOrEmpty(envValue))
            {
                return envValue.Equals("true", StringComparison.OrdinalIgnoreCase);
            }

            // 2. 检查App.config配置
            string configValue = System.Configuration.ConfigurationManager.AppSettings["DevelopmentMode"];
            if (!string.IsNullOrEmpty(configValue))
            {
                return configValue.Equals("true", StringComparison.OrdinalIgnoreCase);
            }

            // 3. 默认为生产模式
            return false;
        }

        /// <summary>
        /// PLC连接配置
        /// </summary>
        public PLCConfiguration PLCConfig { get; set; } = new PLCConfiguration();

        /// <summary>
        /// 日志配置
        /// </summary>
        public WaferAligner.Services.Logging.Configuration.LoggingConfiguration LoggingConfig { get; set; } = new WaferAligner.Services.Logging.Configuration.LoggingConfiguration();
    }

    /// <summary>
    /// PLC配置选项
    /// </summary>
    public class PLCConfiguration
    {
        public string ConnectionString { get; set; } = "";
        public int Port { get; set; } = 851;
        public int NetId { get; set; } = 0;
        public int TimeoutMs { get; set; } = 5000;
    }

}