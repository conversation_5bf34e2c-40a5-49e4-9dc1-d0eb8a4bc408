using System.Threading.Tasks;

namespace WaferAligner.Communication.Inovance.Abstractions
{
    public interface IPlcVariableService
    {
        Task<T> ReadVariableSafelyAsync<T>(string variableName, T defaultValue = default);
        Task<bool> WriteVariableSafelyAsync(string variableName, object value);
        
        /// <summary>
        /// 检查指定连接名称的PLC是否已连接
        /// </summary>
        /// <param name="connectionName">连接名称，如"Main"、"ZAxis"等</param>
        /// <returns>如果已连接则返回true，否则返回false</returns>
        bool IsConnectionOpen(string connectionName);
    }
} 