using WaferAligner.Services.UserManagement;
using WaferAligner.Core.Business;

namespace WaferAligner.Interfaces
{
    /// <summary>
    /// 用户上下文接口，提供当前登录用户的信息和权限管理
    /// </summary>
    public interface IUserContext
    {
        /// <summary>
        /// 当前登录用户
        /// </summary>
        UserInfo? CurrentUser { get; }

        /// <summary>
        /// 获取用户管理服务
        /// </summary>
        IUserManagement UserManagement { get; }

        /// <summary>
        /// 设置当前用户
        /// </summary>
        /// <param name="user">用户信息</param>
        void SetCurrentUser(UserInfo? user);

        /// <summary>
        /// 是否已经登录
        /// </summary>
        bool IsAuthenticated { get; }

        /// <summary>
        /// 检查当前用户是否有指定权限
        /// </summary>
        /// <param name="permission">权限名称</param>
        /// <returns>是否有权限</returns>
        bool HasPermission(string permission);

        /// <summary>
        /// 检查当前用户是否有指定角色
        /// </summary>
        /// <param name="roleName">角色名称</param>
        /// <returns>是否有角色</returns>
        bool HasRole(string roleName);

        /// <summary>
        /// 检查当前用户是否是管理员
        /// </summary>
        /// <returns>是否是管理员</returns>
        bool IsAdmin();

        /// <summary>
        /// 检查当前用户是否可以管理用户
        /// </summary>
        /// <returns>是否可以管理用户</returns>
        bool CanManageUsers();

        /// <summary>
        /// 检查当前用户是否是操作员
        /// </summary>
        /// <returns>是否是操作员</returns>
        bool IsOperator();

        /// <summary>
        /// 检查当前用户是否可以配置对准参数
        /// </summary>
        /// <returns>是否可以配置对准参数</returns>
        bool CanConfigParameters();

        /// <summary>
        /// 检查当前用户是否可以配置运动参数
        /// </summary>
        /// <returns>是否可以配置运动参数</returns>
        bool CanConfigMotion();

        /// <summary>
        /// 获取当前用户名
        /// </summary>
        /// <returns>用户名，如果未登录则返回空字符串</returns>
        string GetUsername();

        /// <summary>
        /// 检查当前用户是否可以访问键合对准页面
        /// </summary>
        /// <returns>是否可以访问</returns>
        bool CanAccessAlignmentPage();

        /// <summary>
        /// 检查当前用户是否可以访问对准参数页面
        /// </summary>
        /// <returns>是否可以访问</returns>
        bool CanAccessParameterPage();

        /// <summary>
        /// 检查当前用户是否可以访问运动参数页面
        /// </summary>
        /// <returns>是否可以访问</returns>
        bool CanAccessMotionPage();

        /// <summary>
        /// 检查当前用户是否可以访问用户管理页面
        /// </summary>
        /// <returns>是否可以访问</returns>
        bool CanAccessUserManagementPage();

        /// <summary>
        /// 获取用户角色显示名称
        /// </summary>
        /// <returns>角色显示名称</returns>
        string GetRoleDisplayName();

        /// <summary>
        /// 注销当前用户
        /// </summary>
        void Logout();
    }
} 