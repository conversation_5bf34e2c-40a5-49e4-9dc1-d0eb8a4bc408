using WaferAligner.Core.Business;
using WaferAligner.Services.Abstractions;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;
using WaferAligner.Services.Extensions;
using System.Security.Cryptography;
using System.Text;
using WaferAligner.EventIds;

namespace WaferAligner.Services.UserManagement
{
    public class UserManagementService : IUserManagement
    {
        private readonly JsonStorageService _storageService;
        private readonly ILoggingService _loggingService;
        private readonly ConcurrentDictionary<string, UserInfo> _userCache;
        private readonly ConcurrentDictionary<string, Role> _roleCache;
        private readonly ConcurrentDictionary<string, Permission> _permissionCache;

        public UserManagementService(ILoggingService loggingService)
        {
            _loggingService = loggingService;
            _storageService = new JsonStorageService();
            _userCache = new ConcurrentDictionary<string, UserInfo>();
            _roleCache = new ConcurrentDictionary<string, Role>();
            _permissionCache = new ConcurrentDictionary<string, Permission>();

            LoadData();
        }

        private void LoadData()
        {
            try
            {
                var users = _storageService.LoadUsers();
                var roles = _storageService.LoadRoles();
                var permissions = _storageService.LoadPermissions();

                _userCache.Clear();
                _roleCache.Clear();
                _permissionCache.Clear();

                foreach (var user in users)
                {
                    _userCache.TryAdd(user.Username, user);
                }

                foreach (var role in roles)
                {
                    _roleCache.TryAdd(role.Name, role);
                }

                foreach (var permission in permissions)
                {
                    _permissionCache.TryAdd(permission.Name, permission);
                }

                _loggingService.LogInformation("用户管理数据加载成功", WaferAligner.EventIds.EventIds.User_Data_Load_Success);
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"加载用户管理数据失败: {ex.Message}", WaferAligner.EventIds.EventIds.User_Data_Load_Failed);
            }
        }

        public bool Authenticate(string username, string password)
        {
            var users = _storageService.LoadUsers();
            var user = users.FirstOrDefault(u => u.Username.Equals(username, StringComparison.OrdinalIgnoreCase));
            
            if (user == null) return false;
            
            var hashedPassword = HashPassword(password);
            return user.PasswordHash.Equals(hashedPassword);
        }

        public UserInfo? GetUserInfo(string username)
        {
            var users = _storageService.LoadUsers();
            return users.FirstOrDefault(u => u.Username.Equals(username, StringComparison.OrdinalIgnoreCase));
        }

        public bool CheckPermission(string username, string permission)
        {
            var user = GetUserInfo(username);
            if (user == null) return false;

            var roles = _storageService.LoadRoles();
            
            foreach (var roleName in user.Roles)
            {
                var role = roles.FirstOrDefault(r => r.Name.Equals(roleName, StringComparison.OrdinalIgnoreCase));
                if (role != null)
                {
                    // 管理员拥有所有权限
                    if (role.Permissions.Contains("*")) return true;
                    
                    // 检查具体权限
                    if (role.Permissions.Contains(permission)) return true;
                }
            }
            
            return false;
        }

        public bool CreateUser(UserInfo userInfo, string initialPassword)
        {
            try
            {
                var users = _storageService.LoadUsers();
                
                // 检查用户名是否已存在
                if (users.Any(u => u.Username.Equals(userInfo.Username, StringComparison.OrdinalIgnoreCase)))
                {
                    _loggingService.LogWarning($"创建用户失败：用户 {userInfo.Username} 已存在", WaferAligner.EventIds.EventIds.User_Create_Failed);
                    return false;
                }
                
                // 设置密码哈希
                userInfo.PasswordHash = HashPassword(initialPassword);
                userInfo.CreatedDate = DateTime.Now;
                
                users.Add(userInfo);
                _storageService.SaveUsers(users);
                
                _loggingService.LogInformation($"用户 {userInfo.Username} 创建成功", WaferAligner.EventIds.EventIds.User_Create_Success);
                return true;
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"创建用户时发生错误: {ex.Message}", WaferAligner.EventIds.EventIds.User_Create_Error);
                return false;
            }
        }

        public bool UpdateUser(UserInfo userInfo)
        {
            try
            {
                var users = _storageService.LoadUsers();
                var existingUser = users.FirstOrDefault(u => u.Username.Equals(userInfo.Username, StringComparison.OrdinalIgnoreCase));
                
                if (existingUser == null)
                {
                    _loggingService.LogWarning($"更新用户失败：用户 {userInfo.Username} 不存在", WaferAligner.EventIds.EventIds.User_Update_Failed);
                    return false;
                }
                
                // 保留密码哈希
                userInfo.PasswordHash = existingUser.PasswordHash;
                userInfo.CreatedDate = existingUser.CreatedDate;
                
                var index = users.IndexOf(existingUser);
                users[index] = userInfo;
                
                _storageService.SaveUsers(users);
                
                _loggingService.LogInformation($"用户 {userInfo.Username} 更新成功", WaferAligner.EventIds.EventIds.User_Update_Success);
                return true;
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"更新用户时发生错误: {ex.Message}", WaferAligner.EventIds.EventIds.User_Update_Error);
                return false;
            }
        }

        public bool UpdateUserPassword(string username, string newPassword)
        {
            try
            {
                var users = _storageService.LoadUsers();
                var user = users.FirstOrDefault(u => u.Username.Equals(username, StringComparison.OrdinalIgnoreCase));
                
                if (user == null)
                {
                    _loggingService.LogWarning($"更新密码失败：用户 {username} 不存在", WaferAligner.EventIds.EventIds.User_Password_Update_Failed);
                    return false;
                }
                
                user.PasswordHash = HashPassword(newPassword);
                _storageService.SaveUsers(users);
                
                _loggingService.LogInformation($"用户 {username} 密码更新成功", WaferAligner.EventIds.EventIds.User_Password_Update_Success);
                return true;
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"更新用户密码时发生错误: {ex.Message}", WaferAligner.EventIds.EventIds.User_Password_Update_Error);
                return false;
            }
        }

        public bool DeleteUser(string username)
        {
            try
            {
                var users = _storageService.LoadUsers();
                var user = users.FirstOrDefault(u => u.Username.Equals(username, StringComparison.OrdinalIgnoreCase));
                
                if (user == null)
                {
                    _loggingService.LogWarning($"删除用户失败：用户 {username} 不存在", WaferAligner.EventIds.EventIds.User_Delete_Failed);
                    return false;
                }
                
                users.Remove(user);
                _storageService.SaveUsers(users);
                
                _loggingService.LogInformation($"用户 {username} 删除成功", WaferAligner.EventIds.EventIds.User_Delete_Success);
                return true;
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"删除用户时发生错误: {ex.Message}", WaferAligner.EventIds.EventIds.User_Delete_Error);
                return false;
            }
        }

        public IEnumerable<UserInfo> GetAllUsers()
        {
            return _storageService.LoadUsers();
        }

        public IEnumerable<Role> GetAllRoles()
        {
            return _storageService.LoadRoles();
        }

        public IEnumerable<Permission> GetAllPermissions()
        {
            return _storageService.LoadPermissions();
        }

        public bool CreateRole(Role role)
        {
            try
            {
                var roles = _storageService.LoadRoles();
                
                if (roles.Any(r => r.Name.Equals(role.Name, StringComparison.OrdinalIgnoreCase)))
                {
                    _loggingService.LogWarning($"创建角色失败：角色 {role.Name} 已存在", WaferAligner.EventIds.EventIds.Role_Create_Failed);
                    return false;
                }
                
                roles.Add(role);
                _storageService.SaveRoles(roles);
                
                _loggingService.LogInformation($"角色 {role.Name} 创建成功", WaferAligner.EventIds.EventIds.Role_Create_Success);
                return true;
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"创建角色时发生错误: {ex.Message}", WaferAligner.EventIds.EventIds.Role_Create_Error);
                return false;
            }
        }

        public bool UpdateRole(Role role)
        {
            try
            {
                var roles = _storageService.LoadRoles();
                var existingRole = roles.FirstOrDefault(r => r.Name.Equals(role.Name, StringComparison.OrdinalIgnoreCase));
                
                if (existingRole == null)
                {
                    _loggingService.LogWarning($"更新角色失败：角色 {role.Name} 不存在", WaferAligner.EventIds.EventIds.Role_Update_Failed);
                    return false;
                }
                
                var index = roles.IndexOf(existingRole);
                roles[index] = role;
                
                _storageService.SaveRoles(roles);
                
                _loggingService.LogInformation($"角色 {role.Name} 更新成功", WaferAligner.EventIds.EventIds.Role_Update_Success);
                return true;
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"更新角色时发生错误: {ex.Message}", WaferAligner.EventIds.EventIds.Role_Update_Error);
                return false;
            }
        }

        public bool DeleteRole(string roleName)
        {
            try
            {
                var roles = _storageService.LoadRoles();
                var role = roles.FirstOrDefault(r => r.Name.Equals(roleName, StringComparison.OrdinalIgnoreCase));
                
                if (role == null)
                {
                    _loggingService.LogWarning($"删除角色失败：角色 {roleName} 不存在", WaferAligner.EventIds.EventIds.Role_Delete_Failed);
                    return false;
                }
                
                roles.Remove(role);
                _storageService.SaveRoles(roles);
                
                _loggingService.LogInformation($"角色 {roleName} 删除成功", WaferAligner.EventIds.EventIds.Role_Delete_Success);
                return true;
            }
            catch (Exception ex)
            {
                _loggingService.LogError($"删除角色时发生错误: {ex.Message}", WaferAligner.EventIds.EventIds.Role_Delete_Error);
                return false;
            }
        }

        private string HashPassword(string password)
        {
            using (var sha256 = SHA256.Create())
            {
                var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(password));
                return Convert.ToBase64String(hashedBytes);
            }
        }
    }
}
