﻿using System;
using Microsoft.Extensions.DependencyInjection;
using WaferAligner.Communication.Inovance.Management;
using WaferAligner.Communication.Inovance.Abstractions;
using WaferAligner.Models;
using WaferAligner.Interfaces;
using WaferAligner.Factories;

namespace WaferAligner.Services
{
    /// <summary>
    /// 轴服务注册扩展方法
    /// </summary>
    public static class AxisServiceExtensions
    {
        /// <summary>
        /// 添加轴服务
        /// </summary>
        public static IServiceCollection AddAxisServices(this IServiceCollection services)
        {
            if (services == null)
                throw new ArgumentNullException(nameof(services));
                
            // 注册PLC通信服务
            services.AddSingleton<IPlcCommunication, PlcCommunication>();
            
            // 注册轴视图模型工厂
            services.AddSingleton<IAxisViewModelFactory, AxisViewModelFactory>();
            
            return services;
        }
        
        /// <summary>
        /// 添加基于PLC的轴视图模型
        /// </summary>
        /// <param name="services">服务集合</param>
        /// <param name="usePlcBasedZAxis">是否使用基于PLC的Z轴</param>
        /// <param name="usePlcBasedCameraAxis">是否使用基于PLC的相机轴</param>
        public static IServiceCollection AddPlcBasedAxisViewModels(
            this IServiceCollection services, 
            bool usePlcBasedZAxis = true, 
            bool usePlcBasedCameraAxis = true)
        {
            if (services == null)
                throw new ArgumentNullException(nameof(services));
                
            // 添加基础服务
            services.AddAxisServices();
            
            // 注册轴视图模型的具体类型
            if (usePlcBasedZAxis)
            {
                // 注册ZAxisViewModelNew类型
                services.AddTransient<ZAxisViewModelNew>();
                
                // 注册接口映射 - 通过工厂方法创建实例
                services.AddTransient<IZAxisViewModel>(provider => 
                    provider.GetRequiredService<IAxisViewModelFactory>().GetZAxisViewModel());
            }
            
            if (usePlcBasedCameraAxis)
            {
                // 注册CameraAxisViewModelNew类型
                services.AddTransient<CameraAxisViewModelNew>();
                
                // 相机轴实例通过AxisViewModelFactory创建
                // 不需要在此处注册接口映射，因为会通过工厂方法获取
            }
            
            return services;
        }
    }
}
