﻿using System;
using System.Threading.Tasks;
using WaferAligner.Services.Abstractions;
using WaferAligner.Interfaces;
using WaferAligner.Services.Extensions;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Linq;
using System.Threading;

namespace WaferAligner.Models
{
    /// <summary>
    /// 轴视图模型的统一基类，提供通用功能
    /// </summary>
    public abstract class AxisViewModelBase : IAxisViewModel, IDisposable, IAsyncDisposable
    {
        protected readonly ILoggingService _loggingService;
        
        // 注册的事件处理字典，用于资源清理
        protected readonly Dictionary<string, Action<object>> _registeredActions = new Dictionary<string, Action<object>>();
        
        // 清理状态标志
        protected volatile bool _isDisposed = false;
        
        // 资源集合
        protected readonly List<IDisposable> _resources = new List<IDisposable>();
        protected readonly List<CancellationTokenSource> _cancellationTokenSources = new List<CancellationTokenSource>();
        
        // 通用状态属性
        public virtual string AxisName { get; protected set; }
        public bool IsConnected { get; protected set; }
        public bool IsReady { get; protected set; }
        public bool IsEnabled { get; protected set; }
        public bool HasError { get; protected set; }
        public bool Arrive_position { get; protected set; }
        
        // 构造函数
        protected AxisViewModelBase(string axisName, ILoggingService loggingService)
        {
            AxisName = axisName ?? throw new ArgumentNullException(nameof(axisName));
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
            
            // 初始化默认状态
            IsConnected = false;
            IsReady = false;
            IsEnabled = false;
            HasError = false;
            Arrive_position = true; // 默认状态为已到位
        }
        
        // 通用方法实现
        public virtual Task InitializeAsync() => Task.CompletedTask;
        
        // 位置相关，子类需要实现
        public abstract Task<bool> MoveToPositionAsync(double position);
        public abstract Task<bool> SetPositionAsync(double position);
        public abstract Task<double> GetCurrentPositionAsync();
        
        // 运动控制，子类需要实现
        public abstract Task<bool> HomeAsync();
        public abstract Task<bool> StopAsync();
        public abstract Task<bool> ResetAsync();
        public abstract Task<bool> JogForwardAsync();
        public abstract Task<bool> JogBackwardAsync();
        public abstract Task<bool> JogStopAsync();
        
        // 速度控制，子类需要实现
        public abstract Task<bool> SetJogSpeedAsync(double speed);
        public abstract Task<bool> SetRunSpeedAsync(double speed);
        
        // 事件注册，增加内部资源跟踪
        public virtual void RegisterAction(string variableName, Action<object> action)
        {
            if (_isDisposed) return;
            
            if (action == null || string.IsNullOrEmpty(variableName))
                return;
                
            lock (_registeredActions)
            {
                _registeredActions[variableName] = action;
            }
            
            LogDebug($"{AxisName}: 注册了变量 {variableName} 的事件处理");
        }
        
        // 事件注销，增加内部资源清理
        public virtual void UnregisterAction(string variableName)
        {
            if (string.IsNullOrEmpty(variableName))
                return;
                
            lock (_registeredActions)
            {
                if (_registeredActions.ContainsKey(variableName))
                {
                    _registeredActions.Remove(variableName);
                    LogDebug($"{AxisName}: 注销了变量 {variableName} 的事件处理");
                }
            }
        }
        
        // 连接管理，子类需要实现
        public abstract Task<bool> ConnectAsync(string address, int port);
        public abstract Task DisconnectAsync();
        
        // 辅助方法，供子类调用
        protected virtual void LogInformation(string message, Microsoft.Extensions.Logging.EventId? eventId = null)
        {
            _loggingService?.LogInformation(message, eventId);
        }
        
        protected virtual void LogWarning(string message, Microsoft.Extensions.Logging.EventId? eventId = null)
        {
            _loggingService?.LogWarning(message, eventId);
        }
        
        protected virtual void LogError(Exception ex, string message, Microsoft.Extensions.Logging.EventId? eventId = null)
        {
            _loggingService?.LogError(ex, message, eventId);
        }
        
        protected virtual void LogDebug(string message, Microsoft.Extensions.Logging.EventId? eventId = null)
        {
            _loggingService?.LogDebug(message, eventId);
        }
        
        // 注册资源方法，用于跟踪需要清理的资源
        protected virtual void RegisterResource(IDisposable resource)
        {
            if (_isDisposed || resource == null) return;
            
            lock (_resources)
            {
                _resources.Add(resource);
            }
            
            LogDebug($"{AxisName}: 注册了资源 {resource.GetType().Name}");
        }
        
        // 注册取消令牌源
        protected virtual void RegisterCancellationTokenSource(CancellationTokenSource cts)
        {
            if (_isDisposed || cts == null) return;
            
            lock (_cancellationTokenSources)
            {
                _cancellationTokenSources.Add(cts);
            }
            
            LogDebug($"{AxisName}: 注册了取消令牌源");
        }
        
        // 资源清理方法 - 实现IDisposable
        public void Dispose()
        {
            // 如果已释放，直接返回
            if (_isDisposed) return;
            
            // 设置释放标志
            _isDisposed = true;
            
            try
            {
                // 调用异步清理方法，但同步等待完成
                DisposeAsyncCore().GetAwaiter().GetResult();
                
                // 额外的同步清理逻辑
                DisposeManagedResources();
                
                LogInformation($"{AxisName}: 轴视图模型已释放", WaferAligner.EventIds.EventIds.Resource_Released);
            }
            catch (Exception ex)
            {
                LogError(ex, $"{AxisName}: 释放资源时发生错误", WaferAligner.EventIds.EventIds.Resource_Released);
            }
            
            GC.SuppressFinalize(this);
        }
        
        // 资源清理方法 - 实现IAsyncDisposable
        public async ValueTask DisposeAsync()
        {
            // 如果已释放，直接返回
            if (_isDisposed) return;
            
            // 设置释放标志
            _isDisposed = true;
            
            try
            {
                // 执行异步清理
                await DisposeAsyncCore();
                
                // 额外的同步清理逻辑
                DisposeManagedResources();
                
                LogInformation($"{AxisName}: 轴视图模型已异步释放", WaferAligner.EventIds.EventIds.Resource_Released);
            }
            catch (Exception ex)
            {
                LogError(ex, $"{AxisName}: 异步释放资源时发生错误", WaferAligner.EventIds.EventIds.Resource_Released);
            }
            
            GC.SuppressFinalize(this);
        }
        
        // 核心异步清理逻辑
        protected virtual async Task DisposeAsyncCore()
        {
            try
            {
                // 1. 停止所有运动
                await StopAsync();
                
                // 2. 断开连接
                await DisconnectAsync();
                
                // 3. 取消所有取消令牌源
                CancelAllTokenSources();
            }
            catch (Exception ex)
            {
                LogWarning($"{AxisName}: 执行异步清理核心逻辑时出现错误: {ex.Message}", WaferAligner.EventIds.EventIds.Resource_Released);
            }
        }
        
        // 同步资源清理
        protected virtual void DisposeManagedResources()
        {
            try
            {
                // 1. 清理所有注册的事件处理
                lock (_registeredActions)
                {
                    foreach (var key in _registeredActions.Keys.ToList())
                    {
                        try
                        {
                            UnregisterAction(key);
                        }
                        catch (Exception ex)
                        {
                            LogWarning($"{AxisName}: 注销事件 {key} 时出现错误: {ex.Message}", WaferAligner.EventIds.EventIds.Resource_Released);
                        }
                    }
                    _registeredActions.Clear();
                }
                
                // 2. 释放所有资源
                lock (_resources)
                {
                    foreach (var resource in _resources)
                    {
                        try
                        {
                            resource?.Dispose();
                        }
                        catch (Exception ex)
                        {
                            LogWarning($"{AxisName}: 释放资源时出现错误: {ex.Message}", WaferAligner.EventIds.EventIds.Resource_Released);
                        }
                    }
                    _resources.Clear();
                }
            }
            catch (Exception ex)
            {
                LogWarning($"{AxisName}: 释放托管资源时出现错误: {ex.Message}", WaferAligner.EventIds.EventIds.Resource_Released);
            }
        }
        
        // 取消所有令牌源
        protected virtual void CancelAllTokenSources()
        {
            lock (_cancellationTokenSources)
            {
                foreach (var cts in _cancellationTokenSources)
                {
                    try
                    {
                        if (cts != null && !cts.IsCancellationRequested)
                        {
                            cts.Cancel();
                            cts.Dispose();
                        }
                    }
                    catch (Exception ex)
                    {
                        LogWarning($"{AxisName}: 取消令牌时出现错误: {ex.Message}", WaferAligner.EventIds.EventIds.Resource_Released);
                    }
                }
                _cancellationTokenSources.Clear();
            }
        }
    }
    
    /// <summary>
    /// 轴位置变化事件参数
    /// </summary>
    public class AxisPositionChangedEventArgs : EventArgs
    {
        public string AxisName { get; }
        public int Position { get; }
        
        public AxisPositionChangedEventArgs(string axisName, int position)
        {
            AxisName = axisName;
            Position = position;
        }
    }
    
    /// <summary>
    /// 轴状态变化事件参数
    /// </summary>
    public class AxisStateChangedEventArgs : EventArgs
    {
        public string AxisName { get; }
        public bool IsMoving { get; }
        public bool IsHomed { get; }
        public bool IsConnected { get; }
        
        public AxisStateChangedEventArgs(string axisName, bool isMoving, bool isHomed, bool isConnected)
        {
            AxisName = axisName;
            IsMoving = isMoving;
            IsHomed = isHomed;
            IsConnected = isConnected;
        }
    }
}
