using System;
using System.Threading.Tasks;
using System.Threading;
using WaferAligner.Communication.Inovance.Abstractions;
using WaferAligner.Services.Logging.Abstractions;
using WaferAligner.Services.Logging.Extensions;


namespace WaferAligner.Communication.Inovance.Management
{
    public class PlcVariableService : IPlcVariableService
    {
        private readonly ILoggingService _loggingService;
        private readonly IPlcInstance _plcInstance;
        private readonly IPlcConnectionManager _plcConnectionManager;

        public PlcVariableService(ILoggingService loggingService, IPlcInstance plcInstance, IPlcConnectionManager plcConnectionManager)
        {
            _loggingService = loggingService;
            _plcInstance = plcInstance;
            _plcConnectionManager = plcConnectionManager ?? throw new ArgumentNullException(nameof(plcConnectionManager));
        }

        public async Task<T> ReadVariableSafelyAsync<T>(string variableName, T defaultValue = default)
        {
            try
            {
                var readInfo = new PLCVarReadInfo { Name = variableName, Type = typeof(T) };
                var value = await _plcInstance.ReadVariableAsync(readInfo, CancellationToken.None);
                return value is T t ? t : defaultValue;
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"读取PLC变量失败: {variableName}");
                return defaultValue;
            }
        }

        public async Task<bool> WriteVariableSafelyAsync(string variableName, object value)
        {
            try
            {
                var writeInfo = new PLCVarWriteInfo { Name = variableName, Value = value };
                return await _plcInstance.WriteVariableAsync(writeInfo, CancellationToken.None);
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"写入PLC变量失败: {variableName}");
                return false;
            }
        }
        
        /// <summary>
        /// 检查指定连接名称的PLC是否已连接
        /// </summary>
        /// <param name="connectionName">连接名称，如"Main"、"ZAxis"等</param>
        /// <returns>如果已连接则返回true，否则返回false</returns>
        public bool IsConnectionOpen(string connectionName)
        {
            try
            {
                // 使用连接管理器检查连接状态
                return _plcConnectionManager?.IsConnected(connectionName) ?? false;
            }
            catch (Exception ex)
            {
                _loggingService?.LogError(ex, $"检查PLC连接状态失败: {connectionName}", WaferAligner.EventIds.EventIds.Plc_Connection_Failed);
                return false;
            }
        }
    }
} 