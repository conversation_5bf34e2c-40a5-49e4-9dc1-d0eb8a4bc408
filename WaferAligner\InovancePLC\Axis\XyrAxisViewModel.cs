﻿using WaferAligner.Communication.Inovance.Abstractions;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using WaferAligner.Services.Abstractions;
using Microsoft.Extensions.DependencyInjection;
using Sunny.UI.Win32;
using System.Collections.Concurrent;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Channels;
using System.Windows;
using WaferAligner;
using WaferAligner.Common;
using WaferAligner.Services.Extensions;
using System;
using System.Collections.Generic;
using System.Linq;
using WaferAligner.EventIds;
using WaferAligner.Interfaces;

namespace AlignerUI
{
    /// <summary>
    /// 轴控制视图模型 - 通过串口控制X、Y、R轴运动
    /// </summary>
    /// <remarks>
    /// 【废弃】此类已被SerialAxisViewModel替代，仅保留用于历史参考
    /// 新代码应使用WaferAligner.SerialControl.Models.SerialAxisViewModel
    /// 参见文档：Development Logs/V2/XyrAxisViewModel迁移说明.md
    /// </remarks>
    [Obsolete("此类已被SerialAxisViewModel替代，请使用WaferAligner.SerialControl.Models.SerialAxisViewModel", true)]
    public partial class XyrAxisViewModel : ObservableObject
    {
        private UInt32 axisIndex = 0;
        private int targetPos = 0;
        private int offsetPos = 0;
        public double OffsetPos { get; set; }
        private readonly ILoggingService _loggingService;
        private readonly IPlcConnectionManager _plcConnectionManager;

        // 新增：连接状态属性
        private bool _isConnected;
        public bool IsConnected
        {
            get => _isConnected;
            private set => SetProperty(ref _isConnected, value);
        }

        // 添加连接状态缓存，避免重复日志
        private static bool _isDevelopmentModeLogged = false;
        private static bool _isProductionModeConnected = false;

        // 添加ConnectAsync的连接状态缓存，避免重复日志
        private static bool _connectAsyncLogged = false;
        private static DateTime _lastConnectLogTime = DateTime.MinValue;

        // 添加静态变量用于跟踪各轴的实例创建
        private static readonly HashSet<UInt32> _createdAxisInstances = new HashSet<UInt32>();
        private static readonly object _instanceLock = new object();

        /// <summary>
        /// 【废弃】构造函数
        /// </summary>
        /// <param name="lorr">轴名称：X、Y或R</param>
        [Obsolete("此类已被SerialAxisViewModel替代，请使用WaferAligner.SerialControl.Models.SerialAxisViewModel", true)]
        public XyrAxisViewModel(string lorr, ILoggingService loggingService = null, IPlcConnectionManager plcConnectionManager = null)
        {
            // 通过构造函数参数获取服务
            _loggingService = loggingService;
            _plcConnectionManager = plcConnectionManager;

            axisIndex = lorr switch
            {
                "Y" => 1,
                "X" => 2,
                "R" => 3,
                _ => 1
            };
            
            // 检查是否已经创建了该轴的实例
            bool isNewInstance = false;
            lock (_instanceLock)
            {
                if (!_createdAxisInstances.Contains(axisIndex))
                {
                    _createdAxisInstances.Add(axisIndex);
                    isNewInstance = true;
                }
            }
            
            // 只有在是新实例且服务可用时才进行异步连接
            if (isNewInstance && _loggingService != null && _plcConnectionManager != null)
            {
                _loggingService?.LogDebug($"创建{lorr}轴实例", EventIds.Axis_Instance_Created_General);
                _ = ConnectAsync();
            }
        }

        // 异步连接方法，连接结果会通知UI
        /// <summary>
        /// 【废弃】异步连接控制器
        /// </summary>
        /// <returns>连接成功返回true，否则返回false</returns>
        [Obsolete("此方法已被SerialAxisViewModel.ConnectAsync替代", true)]
        public async Task<bool> ConnectAsync()
        {
            try
            {
                // 开发模式检测
                bool isDevelopmentMode = System.Configuration.ConfigurationManager.AppSettings["DevelopmentMode"] == "true" ||
                                       Environment.GetEnvironmentVariable("WAFER_ALIGNER_DEV_MODE") == "true" ||
                                       System.Diagnostics.Debugger.IsAttached;
                
                // 在开发模式下，如果已经记录过连接日志且在30秒内，则静默处理
                if (isDevelopmentMode && _connectAsyncLogged && 
                    (DateTime.Now - _lastConnectLogTime).TotalSeconds < 30)
                {
                    // 静默返回连接状态，不记录日志
                    return _plcConnectionManager?.IsConnected("Main") ?? false;
                }
                
                // 使用CancellationTokenSource来处理超时
                using (var cts = new CancellationTokenSource(8000)) // 8秒超时
                {
                    try
                    {
                        // 在后台线程中执行连接操作
                        var result = await Task.Run(() => 
                        {
                            try
                            {
                                // 🔇 减少频繁的连接开始日志
                                if (!isDevelopmentMode || !_connectAsyncLogged)
                                {
                                    _loggingService?.LogInformation($"开始连接控制器", EventIds.Connect_Start_General);
                                }
                                
                                // 检查是否已连接
                                if (_plcConnectionManager?.IsConnected("Main") ?? false)
                                {
                                    // 🔇 减少频繁的已连接日志
                                    if (!isDevelopmentMode || !_connectAsyncLogged)
                                    {
                                        string modeDesc = isDevelopmentMode ? "（开发模式模拟）" : "（实际硬件）";
                                        _loggingService?.LogInformation($"控制器已连接，跳过连接过程 {modeDesc}", EventIds.Already_Connected_General2);
                                    }
                                    return true;
                                }
                                
                                int ret = Connect();
                                return ret == 1;
                            }
                            catch (Exception ex)
                            {
                                _loggingService?.LogError($"连接过程中发生异常: {ex.Message}", EventIds.Connect_Process_Exception_General);
                                return false;
                            }
                        }, cts.Token);
                        
                        IsConnected = result;
                        
                        if (result)
                        {
                            // 🔇 减少频繁的连接成功日志
                            if (!_connectAsyncLogged || (DateTime.Now - _lastConnectLogTime).TotalSeconds > 30)
                            {
                                string modeDesc = isDevelopmentMode ? "（开发模式模拟）" : "（实际硬件）";
                                _loggingService?.LogInformation($"控制器连接成功 {modeDesc}", EventIds.Connect_Success_General2);
                                
                                // 标记已记录日志，无论是否为开发模式
                                _connectAsyncLogged = true;
                                _lastConnectLogTime = DateTime.Now;
                            }
                        }
                        else
                        {
                            // 🚨 保持重要的失败日志
                            _loggingService?.LogWarning($"控制器连接失败", EventIds.Connect_Failed_General2);
                        }
                        
                        return result;
                    }
                    catch (OperationCanceledException)
                    {
                        // 🚨 保持重要的超时日志
                        _loggingService?.LogError($"⏰ 连接控制器超时（8秒）", EventIds.Connect_Timeout_General);
                        IsConnected = false;
                        return false;
                    }
                }
            }
            catch (Exception ex)
            {
                // 🚨 保持重要的异常日志
                _loggingService?.LogError($"连接控制器异常: {ex.Message}", EventIds.Connect_Exception_General);
                IsConnected = false;
                return false;
            }
        }

        /// <summary>
        /// 【废弃】使能轴
        /// </summary>
        /// <returns>成功返回1，失败返回其他值</returns>
        [Obsolete("此方法已被SerialAxisViewModel.EnableAxisAsync替代", true)]
        public int EnableAxis()
        {
            int i = 40, res = -1;
            if (DLL_GetAxisStatus((int)axisIndex, 1) == 1)//查询是否使能，1是已经使能
            {

            }
            else//查询到非使能状态就开启使能
            {
                res = DLL_AxisEnable(axisIndex, 'K');
                while (i > 0)
                {
                    i--;
                    if (DLL_GetAxisStatus((int)axisIndex, 1) == 0)
                        Thread.Sleep(10);
                    else
                    {
                        return 1;

                    }
                }
                res = DLL_AxisEnable(axisIndex, 'F');
                _loggingService?.LogError($"失败，未连接对应轴{axisIndex.ToString()}", EventIds.Axis_Enable_Failed_Not_Connected);

                //MessageBox.Show("失败，未连接对应轴");
            }
            return res;
        }

        public int Connect()
        {
            int Ret = -1;
            
            // 开发模式检测：如果配置文件或环境变量指示为开发模式，直接返回成功
            bool isDevelopmentMode = System.Configuration.ConfigurationManager.AppSettings["DevelopmentMode"] == "true" ||
                                   Environment.GetEnvironmentVariable("WAFER_ALIGNER_DEV_MODE") == "true" ||
                                   System.Diagnostics.Debugger.IsAttached; // 检测是否在调试器中运行
            
            if (isDevelopmentMode)
            {
                // 只在首次连接时记录开发模式日志，避免定时器导致的重复日志
                if (!_isDevelopmentModeLogged)
                {
                    _loggingService?.LogInformation($"开发模式启用：模拟所有轴连接成功（无实际硬件连接）", EventIds.Development_Mode_Connect_General);
                    _isDevelopmentModeLogged = true;
                }
                
                // 使用PlcConnectionManager替代直接设置ConstValue.MyCom.ComOpened
                if (_plcConnectionManager != null)
                {
                    // 开发模式下，确保"XYRAxis"连接被标记为已连接
                    _ = _plcConnectionManager.ConnectAsync("XYRAxis", "127.0.0.1", 502);
                }
                return 1;
            }
            
            // 检查连接状态
            bool isConnected = _plcConnectionManager?.IsConnected("Main") ?? false;
            
            if (!isConnected)
            {
                try
                {
                    _loggingService?.LogInformation($"⚡ 生产模式：开始连接实际硬件控制器，轴索引: {axisIndex}", EventIds.Connect_Process_Start_General);
                    
                    //设置控制轴数量
                    _loggingService?.LogDebug($"设置控制轴数量: 0x01", EventIds.Set_Control_Axis_Start_General);
                    
                    Ret = SetControlAxis(0x01);
                    if (Ret != 1)
                    {
                        int errorCode = DLL_GetError();
                        _loggingService?.LogError($"设置控制轴数量失败，错误码: {errorCode}", EventIds.Set_Control_Axis_Failed_General);
                        return Ret;
                    }
                    
                    _loggingService?.LogDebug($"设置控制轴数量成功，开始打开设备", EventIds.Open_Device_Start_General);
                    
                    //打开设备
                    Ret = DLL_OpenQK(2);
                    if (Ret == 1)
                    {
                        // 使用PlcConnectionManager替代直接设置ConstValue.MyCom.ComOpened
                        if (_plcConnectionManager != null)
                        {
                            // 确保"XYRAxis"连接被标记为已连接
                            _ = _plcConnectionManager.ConnectAsync("XYRAxis", "192.168.1.87", 502);
                        }
                        _isProductionModeConnected = true;
                        
                        _loggingService?.LogDebug($"设备打开成功，设置多轴操作", EventIds.Multi_Axis_Start_General);
                        
                        int multiAxisRet = SetControlAxis(0x04 + 0x02 + 0x01);//设置2个轴操作
                        
                        if (multiAxisRet != 1)
                        {
                            _loggingService?.LogWarning($"多轴设置可能有问题，错误码: {DLL_GetError()}", EventIds.Multi_Axis_Warning);
                        }
                        
                        _loggingService?.LogInformation($"硬件控制器连接成功，轴索引: {axisIndex}", EventIds.Connect_Success_General);
                    }
                    else
                    {
                        int errorCode = DLL_GetError();
                        // 使用PlcConnectionManager替代直接设置ConstValue.MyCom.ComOpened
                        if (_plcConnectionManager != null)
                        {
                            // 确保"XYRAxis"连接被标记为未连接
                            _ = _plcConnectionManager.DisconnectAsync("XYRAxis");
                        }
                        _loggingService?.LogError($"打开硬件设备失败，错误号：{errorCode}，请检查com号，控制器通信线等", EventIds.Com_Connect_Failed);
                    }
                }
                catch (Exception ex)
                {
                    _loggingService?.LogError($"连接控制器失败: {ex.Message}，堆栈: {ex.StackTrace}", EventIds.Connect_Failed_General);
                    // 使用PlcConnectionManager替代直接设置ConstValue.MyCom.ComOpened
                    if (_plcConnectionManager != null)
                    {
                        // 确保"XYRAxis"连接被标记为未连接
                        _ = _plcConnectionManager.DisconnectAsync("XYRAxis");
                    }
                    Ret = -1;
                }
            }
            else
            {
                // 生产模式下，只在首次已连接时记录日志
                if (!_isProductionModeConnected)
                {
                    _loggingService?.LogInformation($"硬件控制器已连接，轴索引: {axisIndex}", EventIds.Already_Connected_General);
                    _isProductionModeConnected = true;
                }
                Ret = 1; // 已经连接，返回成功
            }
            return Ret;
        }

        //[RelayCommand]
        public int InitAxis()
        {
            int Ret = -1;
            // 使用PlcConnectionManager替代直接访问ConstValue.MyCom.ComOpened
            if (_plcConnectionManager?.IsConnected("XYRAxis") ?? false)
            {
                Ret = EnableAxis();
            }
            else
            {
                MessageBox.Show("还没打开打开控制器");
            }
            //初始化
            Ret = DLL_SetFIMode_S((int)axisIndex, 'S');
            return Ret;
        }
        public int ClearMotorError()
        {
            int Ret = -1;
            // 使用PlcConnectionManager替代直接访问ConstValue.MyCom.ComOpened
            if (_plcConnectionManager?.IsConnected("XYRAxis") ?? false)
            {
                Ret = DLL_ClearMotorError((int)axisIndex);
            }

            return Ret;
        }


        public int Home()
        {
            int Ret = -1;
            //上使能
            Ret = EnableAxis();
            return DLL_SetFIMode_S((int)axisIndex, 'S');
        }

        public int SetPosition(int value)
        {
            return targetPos = value;
        }

        public int GoPosition()
        {
            return DLL_PositionAbsoluteMove(axisIndex, targetPos);
        }
        public int Stop()
        {
            return DLL_AxisStop(axisIndex);
        }

        /// <summary>
        /// 异步停止轴运动
        /// </summary>
        /// <returns>操作是否成功</returns>
        public async Task<bool> StopAsync()
        {
            try
            {
                return await Task.Run(() => 
                {
                    int result = Stop();
                    return result == 1;
                });
            }
            catch (Exception ex)
            {
                _loggingService?.LogError(ex, $"停止轴{axisIndex}运动时发生错误", EventIds.Axis_Move_Error);
                return false;
            }
        }

        public int GetPosition()
        {
            return DLL_ReadPosition(axisIndex);//读出的位置信息单位为平台位置反馈的最小分辨率
        }


        public int GetRunState()
        {
            return DLL_GetAxisStatus((int)axisIndex, 2);//查询是否运动
        }
        public int GetEnableState()
        {
            return DLL_GetAxisStatus((int)axisIndex, 1);//查询是否使能
        }
        public int GetAlarmState()
        {
            StringBuilder Display_string = new StringBuilder("", 50);//初始化，字符串为""，长度为50
            int r = DLL_GetStatusError((int)axisIndex, Display_string);
            //if (r != 0) MessageBox.Show(Display_string.ToString());
            return r;
        }

        public int GetSpeed()
        {
            return DLL_GetSpeed4(axisIndex, 'V');//查询是否运动(AxisIndex);//读出的位置信息单位为平台位置反馈的最小分辨率
        }



        public int SetRunSpeed(UInt32 value)
        {
            int res = DLL_SetSpeed4(axisIndex, 'V', value);
            if (res == -1)
            {
                _loggingService?.LogError($"{axisIndex}轴设置速度失败", EventIds.Axis_Speed_Set_Failed);

                //MessageBox.Show("错误号：" + DLL_GetError().ToString());
            }
            return res;
        }

        #region JOG运动

        public int SetJogSpeed(UInt32 value)
        {
            int res = DLL_SetSpeed4(axisIndex, 'J', value);
            if (res == -1)
            {
                _loggingService?.LogError($"{axisIndex}轴设置速度失败", EventIds.Set_Jog_Speed_Failed);

                //MessageBox.Show("错误号：" + DLL_GetError().ToString());
            }
            return res;
        }
        public int GetJogSpeed()
        {
            return DLL_GetSpeed4(axisIndex, 'J');//查询是否运动(AxisIndex);//读出的位置信息单位为平台位置反馈的最小分辨率
        }

        [RelayCommand]
        public void JOF_F_Start()
        {
            DLL_JOG_Move((char)axisIndex, 'L');//Jog右运行           
        }

        [RelayCommand]
        public void JOG_B_Start()
        {
            DLL_JOG_Move((char)axisIndex, 'R');//Jog右运行           
        }

        [RelayCommand]
        public void JOG_Stop()
        {
            DLL_JOG_Move((char)axisIndex, 'T');//Jog停止
        }
        #endregion JOG运动

        #region  函数库
        /*
        功能：获取函数库版本
        返回：DLL释放编号
        */
        [DllImport("SerialCom.dll", CallingConvention = CallingConvention.Cdecl)] public static extern Int32 GetDLLVersion();

        /*
        函数功能：打开驱控设备
        参数说明：controlNum 为 0 关闭设备，为 2 打开设备
        返回参数：若串口成功打开，返回1，否则返回-1(可通过DLL_GetError()查看返回错误代码)
        */
        [DllImport("SerialCom.dll", CallingConvention = CallingConvention.Cdecl)] public static extern Int32 DLL_OpenQK(Int32 controlNum);

        /*
        函数功能：打开串口
        参数说明：串口号（1, 2, 3...）, 波特率（115200, ...）
        返回参数：若串口成功打开，返回1，否则返回-1
        */
        [DllImport("SerialCom.dll", CallingConvention = CallingConvention.Cdecl)] public static extern Int32 DLL_OpenCom(Int32 Com, Int32 Baud);

        /*
        函数功能：关闭串口
        参数说明：无
        返回参数：1 串口关闭成功或串口本身处于关闭状态，-1 关闭失败
        */
        [DllImport("SerialCom.dll", CallingConvention = CallingConvention.Cdecl)] public static extern Int32 DLL_CloseCom();

        /*
        函数功能：设置操作轴 0x01控制轴1，左移以为控制轴2，以此类推
        参数说明：只对轴1操作 Axis_Shift=0x01
        只对轴2操作 Axis_Shift=0x02
        只对轴3操作 Axis_Shift=0x04
        轴1、轴2和轴3都操作 Axis_Shift=0x04+0x02+0x01
        */
        [DllImport("SerialCom.dll", CallingConvention = CallingConvention.Cdecl)] public static extern Int32 SetControlAxis(UInt32 Axis_Shift);//用于设置控制几个轴

        /*
        函数功能：读取指定轴号的位置数据
        参数说明：目标轴号(1~9)
        返回值：  所查轴位置信息
        */
        [DllImport("SerialCom.dll", CallingConvention = CallingConvention.Cdecl)] public static extern Int32 DLL_ReadPosition(UInt32 Address);//读指定轴位置

        /*
        函数功能：控制器使能与失能设置
        参数说明：Address为要设置的目标轴号(1~9)，KG为控制字符当KG='K'时控制器使能，当KG=‘G’时，马达失能。
        返回值：  1设置成功，-1 设置失败(可通过DLL_GetError()查看返回错误代码)
        */
        [DllImport("SerialCom.dll", CallingConvention = CallingConvention.Cdecl)] public static extern Int32 DLL_AxisEnable(UInt32 Address, char KG);//设置平台使能等控制信息时用

        /*
        函数功能：相对移动(以当前平台当前位置为参考进行移动)
        参数说明：驱控器地址(1~9)，移动距离(单位count，为位置反馈的最小分辨率)（数值正负代表向哪个方向移动）
        返回值：  1设置成功，-1 设置失败(可通过DLL_GetError()查看返回错误代码)
        */
        [DllImport("SerialCom.dll", CallingConvention = CallingConvention.Cdecl)] public static extern Int32 DLL_PositionRelativeMove(UInt32 Address, Int32 Position);//相对移动（增量式移动）

        /*
        函数功能：绝对移动(以所设零点位置为参考进行移动)
        参数说明：驱控器地址(1~9)，移动目标位置值（单位count，为位置反馈的最小分辨率)（数值正负代表目标点位于0点正方向还是负方向）
        返回值：  1设置成功，-1 设置失败(可通过DLL_GetError()查看返回错误代码)
        */
        [DllImport("SerialCom.dll", CallingConvention = CallingConvention.Cdecl)] public static extern Int32 DLL_PositionAbsoluteMove(UInt32 Address, Int32 Position);//绝对移动


        /*
        函数功能：设置位移台当前位置为0点
        参数说明：驱控器地址(1~9)
        返回值：  1设置成功，-1设置失败(可通过DLL_GetError()查看返回错误代码)
        */
        [DllImport("SerialCom.dll", CallingConvention = CallingConvention.Cdecl)] public static extern Int32 DLL_SetZeroPosition(UInt32 Address);


        /*
        函数功能：JOG模式运动
        参数说明：Address 驱控器地址(1~9)，CMD 运动命令 'L'左运行，'R'右运行，'T'停止运行
        返回值：  1设置成功，-1 设置失败(可通过DLL_GetError()查看返回错误代码)
        */
        [DllImport("SerialCom.dll", CallingConvention = CallingConvention.Cdecl)] public static extern Int32 DLL_JOG_Move(UInt32 Address, char CMD);

        /*
        函数功能：读取最近一次软件配置错误信息
        输入参数：无
        返回值：  错误信息 32位长度的错误码，没有错误为0
                  991    //马达相关错误
                  992    //没有检测到马达连接
                  993    //没有使能
                  994    //没有设备连接
                  995    //所查询的目标位置没有最新内容
                  996    //输入参数不符合要求
                  997    //DLL_OpenCom 串口打开失败
                  998    //DLL_CloseCom 串口关闭失败
                  999    //ComClose 串口处于关闭状态
                  1000   //控制器收到命令，但没有正常设置（可能由于控制器处于异常状态或和其他命令设置有冲突）
                  1001   //设置超时
                  1002   //Socket初始化失败
                  1003   //Socket链接失败
                  1004   //Socket
                  1100   //操作轴号为无效轴号
        */
        [DllImport("SerialCom.dll", CallingConvention = CallingConvention.Cdecl)] public static extern Int32 DLL_GetError();

        /*
        函数功能：速度设置（定位速度最低分辨率1counts/s，速度设置范围1~2000000 counts/s；Jog速度最低分辨率10000counts/s，速度设置范围30000~600000 counts/s）
        输入参数：Address驱控器地址(1~9),Type速度类型（'V'普通定位速度，'J'Jog速度），mmPerS速度设置值
        返回值：  1设置成功，-1 失败(可通过DLL_GetError()查看返回错误代码)
        */
        [DllImport("SerialCom.dll", CallingConvention = CallingConvention.Cdecl)] public static extern Int32 DLL_SetSpeed4(UInt32 Address, char Type, UInt32 countsPerS);


        /*
        函数功能：读取设置速度（counts/s）
        输入参数：Address驱控器地址(1~9),Type速度类型（'V'普通定位速度，'J'Jog速度，'S'脉冲扫描速度，'F'开机找index速度）
        返回值：  速度（counts/s），若返回0则读取失败(可通过DLL_GetError()查看返回错误代码)
        */
        [DllImport("SerialCom.dll", CallingConvention = CallingConvention.Cdecl)] public static extern Int32 DLL_GetSpeed4(UInt32 Address, char Type);


        /*
        函数功能：查询指定轴的运动状态
        参数说明：Address为要设置的目标轴号（1~9），StatusName （1查询控制器是否使能，2查询是否运动，3查询马达是否接入，4查询控制器是否有异常反馈）
        返回值：  1是，0否，-1 设置失败(可通过DLL_GetError()查看返回错误代码)
        */
        [DllImport("SerialCom.dll", CallingConvention = CallingConvention.Cdecl)] public static extern Int32 DLL_GetAxisStatus(Int32 Address, Int32 StatusNum);


        /*
        函数功能：驱控指令发送（综合上位机用）
        输入参数：用户设置的命令字符串，驱控器类型识别
        返回值：  1成功，-1 失败(可通过DLL_GetError()查看返回错误代码)
        */
        [DllImport("SerialCom.dll", CallingConvention = CallingConvention.Cdecl)] public static extern Int32 DLL_CMDSet_DS(char[] CMD, char DS);

        /*
         函数功能：1、读驱控器返回的命令 （用于读取DLL_CMDSet函数查询或设置参数后的返回）（综合上位机用）
        2、响应DLL_CMDSet查询DLL级别的命令（即只对DLL状态进行查询，不经过驱控器）
         参数：    Output输出的字符串，驱控器类型识别
        返回值：  1读到了驱控器发来的数据(或DLL的状态信息)，-1 读取失败(可通过DLL_GetError()查看返回错误代码)(unsigned char* Output, char DS)
        */
        [DllImport("SerialCom.dll", EntryPoint = "DLL_CMDRead_DS", CallingConvention = CallingConvention.Cdecl)] public static extern Int32 DLL_CMDRead_DS(StringBuilder Output, char DS);

        /*
        函数功能： 清除驱动异常（当S控制器有驱动异常时可以调用此函数）（只能清除当次异常标记，如果异常原因未找到可能还会报异常）
        参数说明： 驱控器地址（即轴号）
        返回值：   1设置成功，-1 设置失败(可通过DLL_GetError()查看返回错误代码)DLL_SendQuenClear
        */
        [DllImport("SerialCom.dll", CallingConvention = CallingConvention.Cdecl)] public static extern Int32 DLL_ClearMotorError(Int32 Address);

        /*
        函数功能： 清空命令发送队列
        参数说明： 驱控器地址（即轴号）
        返回值：   1设置成功，-1 设置失败(可通过DLL_GetError()查看返回错误代码)
        */
        [DllImport("SerialCom.dll", CallingConvention = CallingConvention.Cdecl)] public static extern Int32 DLL_SendQuenClear(Int32 Address);


        /*
        函数功能：获取该轴状态码
        输入参数：Address驱控器地址(1~9),Output保存异常状态说明
        返回值：  状态码，十进制数，状态正常为0, 查询超时返回-1
        */
        //[DllImport("SerialCom.dll", CallingConvention = CallingConvention.Cdecl)] public static extern Int32 DLL_GetStatusError(Int32 Address, string Output);
        [DllImport("SerialCom.dll", EntryPoint = "DLL_GetStatusError", CallingConvention = CallingConvention.Cdecl)] public static extern Int32 DLL_GetStatusError(Int32 Address, StringBuilder Output);

        [DllImport("SerialCom.dll", CallingConvention = CallingConvention.Cdecl)] public static extern Int32 DLL_SetFIMode_S(Int32 Address, char Mode);

        [DllImport("SerialCom.dll", CallingConvention = CallingConvention.Cdecl)] public static extern Int32 DLL_AxisStop(UInt32 Address);
        #endregion 函数库
    }
}
