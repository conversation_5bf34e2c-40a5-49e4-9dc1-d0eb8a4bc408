using System;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using Microsoft.Extensions.Logging;
using WaferAligner.Services.Abstractions;
using WaferAligner.Services.Extensions;

namespace WaferAligner.Infrastructure.Common
{
    /// <summary>
    /// UI线程安全调用管理器
    /// 提供统一的、线程安全的UI操作接口
    /// </summary>
    public static class UIThreadManager
    {
        private static readonly object _lockObject = new object();
        private static volatile bool _isShuttingDown = false;
        
        /// <summary>
        /// 标记应用程序正在关闭
        /// </summary>
        public static void SetShuttingDown()
        {
            lock (_lockObject)
            {
                _isShuttingDown = true;
            }
        }
        
        /// <summary>
        /// 安全地在UI线程执行操作
        /// </summary>
        /// <param name="control">控件实例</param>
        /// <param name="action">要执行的操作</param>
        /// <param name="loggingService">日志记录服务（可选）</param>
        /// <returns>操作是否成功执行</returns>
        public static bool SafeInvoke(this Control control, Action action, ILoggingService loggingService = null)
        {
            if (control == null || action == null || _isShuttingDown)
                return false;

            try
            {
                // 检查控件状态
                if (control.IsDisposed || control.Disposing || !control.IsHandleCreated)
                    return false;

                if (control.InvokeRequired)
                {
                    return InvokeOnUIThread(control, action, loggingService);
                }
                else
                {
                    return ExecuteDirectly(control, action, loggingService);
                }
            }
            catch (Exception ex)
            {
                loggingService?.LogWarning($"SafeInvoke执行异常被忽略: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 异步安全地在UI线程执行操作
        /// </summary>
        public static Task<bool> SafeInvokeAsync(this Control control, Action action, ILoggingService loggingService = null)
        {
            // 使用TaskCompletionSource创建可由回调完成的Task
            var tcs = new TaskCompletionSource<bool>();
            
            if (control == null || action == null || _isShuttingDown)
            {
                tcs.SetResult(false);
                return tcs.Task;
            }
            
            try
            {
                // 检查控件状态
                if (control.IsDisposed || control.Disposing || !control.IsHandleCreated)
                {
                    tcs.SetResult(false);
                    return tcs.Task;
                }
                
                // 获取UI线程同步上下文
                var context = SynchronizationContext.Current;
                
                if (control.InvokeRequired)
                {
                    // 使用Post方法避免阻塞调用线程
                    control.BeginInvoke(new MethodInvoker(() =>
                    {
                        try
                        {
                            // 在UI线程执行操作并设置结果
                            bool result = ExecuteDirectly(control, action, loggingService);
                            tcs.SetResult(result);
                        }
                        catch (Exception ex)
                        {
                            loggingService?.LogWarning($"SafeInvokeAsync执行异常: {ex.Message}");
                            tcs.SetResult(false);
                        }
                    }));
                }
                else
                {
                    // 已在UI线程，直接执行
                    bool result = ExecuteDirectly(control, action, loggingService);
                    tcs.SetResult(result);
                }
            }
            catch (Exception ex)
            {
                loggingService?.LogWarning($"SafeInvokeAsync调度异常: {ex.Message}");
                tcs.SetResult(false);
            }
            
            return tcs.Task;
        }
        
        /// <summary>
        /// 安全地更新控件文本
        /// </summary>
        public static bool SafeSetText(this Control control, string text, ILoggingService loggingService = null)
        {
            return control.SafeInvoke(() => control.Text = text, loggingService);
        }
        
        /// <summary>
        /// 安全地更新标签文本
        /// </summary>
        public static bool SafeSetText(this Label label, string text, ILoggingService loggingService = null)
        {
            return label.SafeInvoke(() => label.Text = text, loggingService);
        }
        
        private static bool InvokeOnUIThread(Control control, Action action, ILoggingService loggingService)
        {
            try
            {
                if (control.IsDisposed || control.Disposing || !control.IsHandleCreated)
                    return false;
                
                // 使用BeginInvoke避免阻塞
                control.BeginInvoke(new MethodInvoker(() =>
                {
                    // 在UI线程挂起布局，减少刷新次数
                    bool needSuspendLayout = control is Form || control is UserControl || control is Panel;
                    if (needSuspendLayout)
                    {
                        control.SuspendLayout();
                    }
                    
                    try
                    {
                        ExecuteDirectly(control, action, loggingService);
                    }
                    finally
                    {
                        // 恢复布局
                        if (needSuspendLayout)
                        {
                            control.ResumeLayout();
                        }
                    }
                }));
                
                return true;
            }
            catch (ObjectDisposedException)
            {
                return false; // 静默处理
            }
            catch (InvalidOperationException)
            {
                return false; // 静默处理
            }
            catch (Exception ex)
            {
                loggingService?.LogWarning($"BeginInvoke调用失败: {ex.Message}");
                return false;
            }
        }
        
        private static bool ExecuteDirectly(Control control, Action action, ILoggingService loggingService)
        {
            try
            {
                if (control.IsDisposed || control.Disposing || !control.IsHandleCreated)
                    return false;
                    
                action();
                return true;
            }
            catch (ObjectDisposedException)
            {
                return false; // 静默处理
            }
            catch (InvalidOperationException)
            {
                return false; // 静默处理
            }
            catch (Exception ex)
            {
                loggingService?.LogWarning($"UI操作执行失败: {ex.Message}");
                return false;
            }
        }
    }
} 